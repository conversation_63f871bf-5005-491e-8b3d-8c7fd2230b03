<?php
/**
 * Debug script to check available agents
 */

// WordPress environment
require_once('../../../wp-config.php');
require_once('includes/class-lexai-db.php');

echo "<h1>LexAI Agents Debug</h1>";

try {
    $db = new LexAI_DB();
    $agents = $db->get_agents('active');
    
    echo "<h2>Active Agents Found: " . count($agents) . "</h2>";
    
    if (empty($agents)) {
        echo "<p style='color: red;'>No active agents found!</p>";
        
        // Try to get all agents regardless of status
        $all_agents = $db->get_agents();
        echo "<h3>All Agents (any status): " . count($all_agents) . "</h3>";
        
        foreach ($all_agents as $agent) {
            echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
            echo "<h4>{$agent->name} (Status: {$agent->status})</h4>";
            echo "<p><strong>Description:</strong> {$agent->description}</p>";
            echo "<p><strong>Model:</strong> {$agent->model}</p>";
            echo "<p><strong>Tools:</strong> {$agent->tools}</p>";
            echo "</div>";
        }
    } else {
        foreach ($agents as $agent) {
            echo "<div style='border: 1px solid #green; margin: 10px; padding: 10px;'>";
            echo "<h4>{$agent->name}</h4>";
            echo "<p><strong>Description:</strong> {$agent->description}</p>";
            echo "<p><strong>Model:</strong> {$agent->model}</p>";
            echo "<p><strong>Tools:</strong> {$agent->tools}</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test agent selection
echo "<h2>Testing Agent Selection</h2>";

try {
    require_once('includes/class-lexai-orchestrator.php');
    require_once('includes/class-lexai-api-handler.php');
    require_once('includes/class-lexai-tools-manager.php');
    require_once('includes/class-lexai-tool-executor.php');
    
    $tools_manager = new LexAI_Tools_Manager();
    $tool_executor = new LexAI_Tool_Executor();
    $orchestrator = new LexAI_Orchestrator($tools_manager, $tool_executor);
    
    // Test different task types
    $test_tasks = [
        ['type' => 'buscar_legislacion', 'details' => 'Buscar Código Civil de Guanajuato'],
        ['type' => 'buscar_jurisprudencia', 'details' => 'Buscar tesis sobre contratos'],
        ['type' => 'redactar_clausula', 'details' => 'Redactar contrato de compraventa'],
        ['type' => 'analizar_documento', 'details' => 'Analizar documento legal'],
    ];
    
    foreach ($test_tasks as $task) {
        echo "<h3>Testing task: {$task['type']}</h3>";
        
        // Use reflection to access private method
        $reflection = new ReflectionClass($orchestrator);
        $method = $reflection->getMethod('select_agent_for_task');
        $method->setAccessible(true);
        
        try {
            $selected_agent = $method->invoke($orchestrator, $task['type'], $task['details']);
            echo "<p><strong>Selected Agent:</strong> {$selected_agent->name}</p>";
            echo "<p><strong>Description:</strong> {$selected_agent->description}</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error selecting agent: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error testing agent selection: " . $e->getMessage() . "</p>";
}
?>
