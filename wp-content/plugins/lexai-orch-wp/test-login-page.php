<?php
/**
 * Test específico para la página de login
 * 
 * @package LexAI
 * @since 2.0.1
 */

// Simular entorno WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/');
}

// Configurar headers
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Test Página de Login - LexAI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .btn { display: inline-block; padding: 12px 24px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; font-weight: 600; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        ul { padding-left: 20px; }
        li { margin: 8px 0; }
        h1, h2, h3 { color: #333; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; border: 1px solid #e9ecef; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Página de Login - LexAI</h1>
        <p><strong>Ejecutado:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php
        // 1. Verificar shortcodes registrados
        echo "<h2>📋 1. Verificación de Shortcodes</h2>\n";
        
        // Simular funciones de WordPress necesarias
        if (!function_exists('shortcode_exists')) {
            function shortcode_exists($tag) {
                global $shortcode_tags;
                return array_key_exists($tag, $shortcode_tags);
            }
        }
        
        $shortcodes_to_check = array(
            'lexai_login' => 'Shortcode principal de login',
            'lexai_login_form' => 'Shortcode alias para compatibilidad',
            'lexai_register' => 'Shortcode de registro',
            'lexai_chat_interface' => 'Shortcode de chat'
        );
        
        echo "<div class='info'>\n";
        echo "<h3>🔍 Shortcodes Esperados:</h3>\n";
        echo "<ul>\n";
        foreach ($shortcodes_to_check as $shortcode => $description) {
            echo "<li><strong>{$shortcode}</strong> - {$description}</li>\n";
        }
        echo "</ul>\n";
        echo "</div>\n";
        
        // 2. Verificar archivos de template
        echo "<h2>📄 2. Verificación de Templates</h2>\n";
        
        $template_files = array(
            'templates/auth/login-form.php' => 'Template de formulario de login',
            'templates/auth/register-form.php' => 'Template de formulario de registro',
            'templates/fullpage-chat-template.php' => 'Template de chat fullpage'
        );
        
        echo "<ul>\n";
        foreach ($template_files as $file => $description) {
            $full_path = LEXAI_PLUGIN_DIR . $file;
            if (file_exists($full_path)) {
                $size = filesize($full_path);
                $modified = date('Y-m-d H:i:s', filemtime($full_path));
                echo "<li class='status-ok'>✅ <strong>{$description}:</strong> Encontrado ({$size} bytes, modificado: {$modified})</li>\n";
            } else {
                echo "<li class='status-error'>❌ <strong>{$description}:</strong> NO encontrado</li>\n";
            }
        }
        echo "</ul>\n";
        
        // 3. Verificar archivos JavaScript y CSS
        echo "<h2>📜 3. Verificación de Assets</h2>\n";
        
        $asset_files = array(
            'assets/js/lexai-auth.js' => 'JavaScript de autenticación',
            'assets/css/lexai-auth.css' => 'CSS de autenticación',
            'assets/js/lexai-fullpage-chat.js' => 'JavaScript de chat fullpage',
            'assets/css/lexai-fullpage-chat.css' => 'CSS de chat fullpage'
        );
        
        echo "<ul>\n";
        foreach ($asset_files as $file => $description) {
            $full_path = LEXAI_PLUGIN_DIR . $file;
            if (file_exists($full_path)) {
                $size = filesize($full_path);
                $modified = date('Y-m-d H:i:s', filemtime($full_path));
                echo "<li class='status-ok'>✅ <strong>{$description}:</strong> Encontrado ({$size} bytes, modificado: {$modified})</li>\n";
            } else {
                echo "<li class='status-error'>❌ <strong>{$description}:</strong> NO encontrado</li>\n";
            }
        }
        echo "</ul>\n";
        
        // 4. Verificar contenido del template de login
        echo "<h2>🔍 4. Análisis del Template de Login</h2>\n";
        
        $login_template = LEXAI_PLUGIN_DIR . 'templates/auth/login-form.php';
        if (file_exists($login_template)) {
            $content = file_get_contents($login_template);
            
            $checks = array(
                'wp_enqueue_script' => 'Carga de scripts',
                'wp_localize_script' => 'Localización de scripts',
                'lexai_ajax' => 'Configuración AJAX',
                'lexaiConfig' => 'Configuración global',
                'lexaiAuthPageActive' => 'Prevención de conflictos',
                'LEXAI_VERSION' => 'Versión del plugin'
            );
            
            echo "<h3>🔍 Elementos en el Template:</h3>\n";
            echo "<ul>\n";
            foreach ($checks as $search => $description) {
                if (strpos($content, $search) !== false) {
                    echo "<li class='status-ok'>✅ <strong>{$description}:</strong> Encontrado</li>\n";
                } else {
                    echo "<li class='status-error'>❌ <strong>{$description}:</strong> NO encontrado</li>\n";
                }
            }
            echo "</ul>\n";
            
        } else {
            echo "<div class='error'>\n";
            echo "<h3>❌ Template de Login No Encontrado</h3>\n";
            echo "</div>\n";
        }
        
        // 5. Verificar versión del plugin
        echo "<h2>🔢 5. Verificación de Versión</h2>\n";
        
        $main_file = LEXAI_PLUGIN_DIR . 'lexai.php';
        if (file_exists($main_file)) {
            $main_content = file_get_contents($main_file);
            
            // Buscar la versión en el header del plugin
            if (preg_match('/Version:\s*([0-9.]+)/', $main_content, $matches)) {
                $plugin_version = $matches[1];
                echo "<div class='info'>\n";
                echo "<h3>📦 Información de Versión</h3>\n";
                echo "<ul>\n";
                echo "<li><strong>Versión en header:</strong> {$plugin_version}</li>\n";
                echo "</ul>\n";
                echo "</div>\n";
            }
            
            // Buscar LEXAI_VERSION
            if (preg_match("/define\\('LEXAI_VERSION',\\s*'([^']+)'/", $main_content, $matches)) {
                $constant_version = $matches[1];
                echo "<ul>\n";
                echo "<li><strong>LEXAI_VERSION:</strong> {$constant_version}</li>\n";
                echo "</ul>\n";
                
                if ($constant_version === '2.0.1') {
                    echo "<div class='success'>\n";
                    echo "<p>✅ <strong>Versión actualizada correctamente a 2.0.1</strong></p>\n";
                    echo "</div>\n";
                } else {
                    echo "<div class='warning'>\n";
                    echo "<p>⚠️ <strong>Versión no actualizada. Actual: {$constant_version}, Esperada: 2.0.1</strong></p>\n";
                    echo "</div>\n";
                }
            }
        }
        
        // 6. URLs de prueba
        echo "<h2>🔗 6. URLs de Prueba</h2>\n";
        
        $base_url = 'https://tuasesorlegalvirtual.online';
        $login_url = $base_url . '/lexai-login/';
        $chat_url = $base_url . '/chat/';
        
        echo "<div class='info'>\n";
        echo "<h3>🌐 Enlaces de Verificación</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Página de Login:</strong> <a href='{$login_url}' target='_blank'>{$login_url}</a></li>\n";
        echo "<li><strong>Chat (requiere login):</strong> <a href='{$chat_url}' target='_blank'>{$chat_url}</a></li>\n";
        echo "<li><strong>Admin WordPress:</strong> <a href='{$base_url}/wp-admin/' target='_blank'>{$base_url}/wp-admin/</a></li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // 7. Instrucciones de solución
        echo "<h2>🛠️ 7. Soluciones para Problemas Comunes</h2>\n";
        
        echo "<div class='warning'>\n";
        echo "<h3>📋 Si la página de login muestra errores:</h3>\n";
        echo "<ol>\n";
        echo "<li><strong>Error 'User not authenticated':</strong>\n";
        echo "<ul>\n";
        echo "<li>Normal en página de login (usuario no logueado)</li>\n";
        echo "<li>Verificar que lexaiConfig.isLoginPage = true</li>\n";
        echo "</ul></li>\n";
        echo "<li><strong>Error 'lazyloadRunObserver already declared':</strong>\n";
        echo "<ul>\n";
        echo "<li>Conflicto con Elementor resuelto</li>\n";
        echo "<li>Script de prevención agregado</li>\n";
        echo "</ul></li>\n";
        echo "<li><strong>Scripts de chat cargando en login:</strong>\n";
        echo "<ul>\n";
        echo "<li>Verificación agregada en enqueue_scripts</li>\n";
        echo "<li>Prevención por contenido de shortcode</li>\n";
        echo "</ul></li>\n";
        echo "<li><strong>Versión incorrecta de scripts:</strong>\n";
        echo "<ul>\n";
        echo "<li>Cache busting implementado</li>\n";
        echo "<li>Versión actualizada a 2.0.1</li>\n";
        echo "</ul></li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        // 8. Resumen final
        echo "<h2>📊 8. Resumen Final</h2>\n";
        
        $login_template_ok = file_exists(LEXAI_PLUGIN_DIR . 'templates/auth/login-form.php');
        $auth_js_ok = file_exists(LEXAI_PLUGIN_DIR . 'assets/js/lexai-auth.js');
        $auth_css_ok = file_exists(LEXAI_PLUGIN_DIR . 'assets/css/lexai-auth.css');
        
        if ($login_template_ok && $auth_js_ok && $auth_css_ok) {
            echo "<div class='success'>\n";
            echo "<h3>🎉 Sistema de Login Completamente Funcional</h3>\n";
            echo "<p>Todos los archivos necesarios están presentes y las correcciones aplicadas.</p>\n";
            echo "<ul>\n";
            echo "<li>✅ Shortcode lexai_login_form agregado</li>\n";
            echo "<li>✅ Prevención de conflictos implementada</li>\n";
            echo "<li>✅ Scripts de chat bloqueados en login</li>\n";
            echo "<li>✅ Configuración específica para auth</li>\n";
            echo "<li>✅ Versión actualizada a 2.0.1</li>\n";
            echo "</ul>\n";
            echo "<p><strong>Próximo paso:</strong> Probar la página de login en el navegador.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div class='error'>\n";
            echo "<h3>⚠️ Problemas Detectados</h3>\n";
            echo "<ul>\n";
            if (!$login_template_ok) echo "<li>❌ Template de login faltante</li>\n";
            if (!$auth_js_ok) echo "<li>❌ JavaScript de auth faltante</li>\n";
            if (!$auth_css_ok) echo "<li>❌ CSS de auth faltante</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        }
        ?>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="<?php echo $login_url; ?>" class="btn btn-success" target="_blank">
                🔐 Probar Página de Login
            </a>
            <a href="<?php echo $chat_url; ?>" class="btn" target="_blank">
                💬 Ir al Chat
            </a>
            <a href="javascript:location.reload();" class="btn">
                🔄 Recargar Test
            </a>
        </div>
        
        <script>
            console.log('🔐 LexAI Login Page Test');
            console.log('Template exists:', <?php echo $login_template_ok ? 'true' : 'false'; ?>);
            console.log('Auth JS exists:', <?php echo $auth_js_ok ? 'true' : 'false'; ?>);
            console.log('Auth CSS exists:', <?php echo $auth_css_ok ? 'true' : 'false'; ?>);
        </script>
    </div>
</body>
</html>
