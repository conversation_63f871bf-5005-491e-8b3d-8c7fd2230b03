cambia esto en el light mode

.lexai-conversation-item {
    padding: 0.875rem;
    background: var(--lexai-bg-card);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    color: var(--lexai-text-primary);
}

por:

.lexai-conversation-item {
    padding: 0.875rem;
    background: #4a5667;
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    color: #e5e5e5;
}

---

reemplaza en lightmode esto

.lexai-search-box input {
    width: 100%;
    padding: 0.75rem 0.875rem 0.75rem 2.5rem;
    background: var(--lexai-bg-card);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    color: var(--lexai-text-primary);
    font-size: 0.875rem;
    transition: all var(--lexai-transition-fast);
}

por esto

.lexai-search-box input {
    width: 100%;
    padding: 0.75rem 0.875rem 0.75rem 2.5rem;
    background: rgb(74 86 103);
    border: 1px solid rgb(151 171 199 / 82%);
    border-radius: var(--lexai-border-radius);
    color: #ffffff;
    font-size: 0.875rem;
    transition: all var(--lexai-transition-fast);
}

---

DEBES REEMPLAZAR EN LIGHTMODE ESTO

[type=button], [type=submit], button {
    background-color: transparent;
    border: 1px solid #c36;
    border-radius: 3px;
    color: #c36;
    display: inline-block;
    font-size: 1rem;
    font-weight: 400;
    padding: .5rem 1rem;
    text-align: center;
    transition: all .3s;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    white-space: nowrap;
}

POR ESTO 

[type=button], [type=submit], button {
    background-color: transparent;
    border: 1px solid #64748b;
    border-radius: 3px;
    color: #64748b;
    display: inline-block;
    font-size: 1rem;
    font-weight: 400;
    padding: .5rem 1rem;
    text-align: center;
    transition: all .3s;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    white-space: nowrap;
}

QUE SON COLORES MÁS ACORDE AL ESTILO GRPÁFICO

---

EN DARK-MODE DEBES REEMPLAZAR ESTO

.lexai-header {
    height: var(--lexai-header-height);
    background: rgb(35 35 35);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-bottom: 1px solid var(--lexai-border);
    display: flex
;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    z-index: var(--lexai-z-header);
}

POR

.lexai-header {
    height: var(--lexai-header-height);
    background: rgb(35 35 35 / 0%);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-bottom: 1px solid var(--lexai-border);
    display: flex
;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    z-index: var(--lexai-z-header);
}

---

esto

---
ESTO 

.lexai-settings-btn {
    background: rgb(35 35 35);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    width: 44px;
    height: 44px;
    display: flex
;
    align-items: center;
    justify-content: center;
}

POR

.lexai-settings-btn {
    background: rgb(35 35 35);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    width: 44px;
    height: 44px;
    display: flex
;
    align-items: center;
    justify-content: center;
}

---

EN DARK MODE ESTO

.lexai-theme-toggle {
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    position: relative;
    width: 44px;
    height: 44px;
    display: flex
;
    align-items: center;
    justify-content: center;
}

POR ESTO

.lexai-theme-toggle {
    background: rgb(35 35 35);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    position: relative;
    width: 44px;
    height: 44px;
    display: flex
;
    align-items: center;
    justify-content: center;
}

---

ESTO EN DARKMODE 

.lexai-btn-glass {
    background: var(--lexai-bg-glass);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    display: flex
;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
}

POR ESTO

.lexai-btn-glass {
    background: rgb(35 35 35);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    padding: 0.75rem;
    color: var(--lexai-text-secondary);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    display: flex
;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
}


---

en darkmode esto:

.lexai-input-area {
    padding: 1.5rem;
    background: var(--lexai-bg-card);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-top: 1px solid var(--lexai-border);
}


por esto:

.lexai-input-area {
    padding: 1.5rem;
    background: rgb(35 35 35 / 0%);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-top: 1px solid var(--lexai-border);
}

---

en darkmode esto:

.lexai-input-wrapper {
    background: var(--lexai-bg-input);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius-lg);
    display: flex
;
    align-items: flex-end;
    gap: 0.75rem;
    padding: 0.75rem;
    transition: all var(--lexai-transition-fast);
    position: relative;
}

por esto:

.lexai-input-wrapper {
    background: #232323;
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius-lg);
    display: flex
;
    align-items: flex-end;
    gap: 0.75rem;
    padding: 0.75rem;
    transition: all var(--lexai-transition-fast);
    position: relative;
}

---

en darkmode esto:

.lexai-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

por esto:

.lexai-sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #23232300;
}
---

en darkmode esto:

.lexai-conversation-item {
    padding: 0.875rem;
    background: #4a5667;
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    color: #e5e5e5;
}

por esto:

.lexai-conversation-item {
    padding: 0.875rem;
    background: #2b2a2a;
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    color: #e5e5e5;
}


---

en darkmode esto:

.lexai-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--lexai-border);
}

por esto:

.lexai-sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--lexai-border);
    background: #23232300;
}

---

en darkmode esto:

.lexai-sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--lexai-border);
}

por esto:

.lexai-sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--lexai-border);
    background: #232323;
}

---

en darkmode esto:

.lexai-new-chat-btn {
    width: 100%;
    padding: 0.875rem 1rem;
    background: var(--lexai-gradient-primary);
    color: var(--lexai-text-tertiary);
    border: none;
    border-radius: var(--lexai-border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--lexai-transition);
    display: flex
;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

por esto:

.lexai-new-chat-btn {
    width: 100%;
    padding: 0.875rem 1rem;
    background: #353535;
    color: var(--lexai-text-tertiary);
    border: none;
    border-radius: var(--lexai-border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--lexai-transition);
    display: flex
;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

---

en dark mode reemplazar esto

.lexai-animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    pointer-events: none;
}

por esto .lexai-animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    pointer-events: none;
    background: #232323;
}


---


esta debe ser los estilos darkmode de .lexai-floating-element

.lexai-floating-element {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 60% 40%, #5d5b5b 0%, #696969 60%, transparent 100%);
    filter: blur(40px);
    opacity: 0.15;
    animation: lexai-float 20s ease-in-out infinite;
}


---

estos deben ser los estilos dark mode de:

.lexai-message-user .lexai-message-text {
    background: #3939397d;
    color: var(--lexai-text-tertiary);
    border-color: #505051;
}

---

estos deben ser los estilos dark mode de:


.lexai-message-text {
    background: rgb(37 37 37 / 64%);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border: 1px solid var(--lexai-border);
    border-radius: var(--lexai-border-radius-lg);
    padding: 1rem 1.25rem;
    color: var(--lexai-text-primary);
    line-height: 1.6;
    word-wrap: break-word;
}


---

estos deben ser los estilos dark mode de:

.lexai-message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #3d3d3d;
    display: flex
;
    align-items: center;
    justify-content: center;
    color: var(--lexai-text-tertiary);
    font-size: 0.875rem;
    flex-shrink: 0;
}

---

estos deben ser los estilos darkmode de:

.lexai-message-user .lexai-message-avatar {
    background: #3d3d3d;
}

---

esta clase tanto en darkmode como en lightmode debe ser un popup que se despliegue en todoe el medio de la pantalla con un backdropfilter blur 10px y teniendo estilos equivalentes a los temas para que haya consistencia

class="lexai-user-menu"

---



el darkmode del left sidebar es:

.lexai-sidebar {
    width: var(--lexai-sidebar-width);
    background: rgb(15 23 42 / 0%);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-right: 1px solid var(--lexai-border);
    display: flex
;
    flex-direction: column;
    transition: width var(--lexai-transition), transform var(--lexai-transition);
    z-index: var(--lexai-z-sidebar);
    position: relative;
}

---


este debe ser el darkmode de:

.lexai-send-btn {
    border: none;
    border-radius: var(--lexai-border-radius);
    color: #ffffff;
    cursor: pointer;
    padding: 0.5rem;
    transition: all var(--lexai-transition-fast);
    display: flex
;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

---

debe de corregirse la versión colapsada del sidebar, los elementos nose acoplan correctamente y el texto se termina comprimiento en el historial de conversaciones, yo creo que cuando el sidebar este minimizado no se debe ver el historial, cuando esté maximizado si se debe de ver elhistoria

al igual que con el nombre y rol del usuario ya que no permite que se aprecie todo correctamente

---

el lightmode de esta clase debe ser:

.lexai-sidebar {
    width: var(--lexai-sidebar-width);
    background: rgb(51 65 85 / 48%);
    backdrop-filter: var(--lexai-backdrop-blur);
    -webkit-backdrop-filter: var(--lexai-backdrop-blur);
    border-right: 1px solid var(--lexai-border);
    display: flex
;
    flex-direction: column;
    transition: width var(--lexai-transition), transform var(--lexai-transition);
    z-index: var(--lexai-z-sidebar);
    position: relative;
}

---

este debe ser el lightmode de esta clase:

.lexai-conversation-item {
    padding: 0.875rem;
    background: rgb(145 153 163);
    border: 1px solid rgb(117 117 117 / 34%);
    border-radius: var(--lexai-border-radius);
    cursor: pointer;
    transition: all var(--lexai-transition-fast);
    color: #ffffff;
}

---

este es el lightmode de esta clase:

.lexai-search-box input {
    width: 100%;
    padding: 0.75rem 0.875rem 0.75rem 2.5rem;
    background: rgb(98 102 107 / 27%);
    border: 1px solid rgb(107 107 107 / 22%);
    border-radius: var(--lexai-border-radius);
    color: #ffffff;
    font-size: 0.875rem;
    transition: all var(--lexai-transition-fast);
}


---
este debe ser el lightmode de:

.lexai-new-chat-btn {
    width: 100%;
    padding: 0.875rem 1rem;
    background: #626d7b00;
    color: var(--lexai-text-tertiary);
    border: solid 1px #d5d5d5;
    border-radius: var(--lexai-border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--lexai-transition);
    display: flex
;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
}

---

HAS UN ANÁLISIS PROFUNDO DEL ACTUAL CSS PARA QUE HAGAS UNA INTEGRACIÓN EFECTIVA Y FUNCIONAL DE LOS ESTILOS CON EL FIN DE GARANTIZAR LA ARMONÍA DE ESTILO Y COLORES POR ÚLTIMO EN LA PÁGINA DEL CHAT EN EL SIDEBAR REEMPLAZA "LEXAI"POR "ALVI" REEMPLAZA EL TEXTO "Nueva conversación" POR "Asistente Legal Virtual" en la versión mobile debe haber un botón que se muestre cuando aparezca el sidebar offcanvas para que se pueda ocultar

DEBES DE REEMPLAZAR EL ACTUAL BACKGROUND EN LA PÁGINA DE LOGIN /lexai-login POR LA MISMA ANIMACIÓN BACKGROUND QUE HAY EN EL CHAT CON EXACTAMENTE LOS MISMOS ESTILOS AL IGUAL QUE EN lexai-register/ Y EN /lexai-reset-password/ QUE TODOS LOS ESTILOS SEAN COMPATIBLES EN COLORES COLORES EXTRAS A LOS QUE SE ESTÁN IMPLEMENTANDO EN LOS TEMAS DEL CHAT CON LAS CORRECCIONES SOLICITADAS NO SON VÁLIDOS TAMBIEN EN ESAS PÁGINAs de LOGIN, REGISTER Y RESET PASSWORD EN LOS MODALES SALE EL TEXTO "LEXAI" REEMPLAZALOS POR "ALVI"
