<?php
/**
 * Settings Page
 *
 * @package LexAI
 * @since 1.0.1
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$settings = get_option('lexai_settings', array());
$general_settings = $settings['general'] ?? array();
$usage_limits = $settings['usage_limits'] ?? array();
$pinecone_settings = $settings['pinecone'] ?? array();
$google_search_settings = get_option('lexai_google_search_settings', array());
?>

<div class="wrap lexai-admin-wrap">
    <h1><?php _e('Configuración de LexAI', 'lexai'); ?></h1>
    
    <div class="lexai-admin-header">
        <h2><?php _e('Configuración General del Sistema', 'lexai'); ?></h2>
        <p><?php _e('Configura el comportamiento general de LexAI, límites de uso y opciones avanzadas.', 'lexai'); ?></p>
    </div>

    <div class="lexai-tabs">
        <div class="lexai-tab-nav">
            <button class="lexai-tab-button active" data-tab="general"><?php _e('General', 'lexai'); ?></button>
            <button class="lexai-tab-button" data-tab="usage-limits"><?php _e('Límites de Uso', 'lexai'); ?></button>
            <button class="lexai-tab-button" data-tab="pinecone"><?php _e('Pinecone', 'lexai'); ?></button>
            <button class="lexai-tab-button" data-tab="advanced"><?php _e('Avanzado', 'lexai'); ?></button>
            <button class="lexai-tab-button" data-tab="import-export"><?php _e('Importar/Exportar', 'lexai'); ?></button>
        </div>

        <!-- General Settings Tab -->
        <div id="general" class="lexai-tab-panel active">
            <div class="lexai-admin-card">
                <h2><?php _e('Configuración General', 'lexai'); ?></h2>
                
                <form class="lexai-admin-form" data-action="lexai_save_general_settings">
                    <?php wp_nonce_field('lexai_general_settings', 'general_nonce'); ?>
                    
                    <table class="lexai-form-table">
                        <tr>
                            <th scope="row">
                                <label for="enable-validator-agent"><?php _e('Agente Validador', 'lexai'); ?></label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" id="enable-validator-agent" name="enable_validator_agent" 
                                           <?php checked(!empty($general_settings['enable_validator_agent'])); ?>>
                                    <?php _e('Habilitar agente validador de respuestas', 'lexai'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Un agente adicional revisará y validará las respuestas antes de mostrarlas al usuario', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="max-conversation-history"><?php _e('Historial de Conversación', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="number" id="max-conversation-history" name="max_conversation_history" 
                                       value="<?php echo esc_attr($general_settings['max_conversation_history'] ?? 50); ?>"
                                       min="10" max="200" step="10" class="small-text">
                                <span class="description"><?php _e('mensajes', 'lexai'); ?></span>
                                <p class="description">
                                    <?php _e('Número máximo de mensajes a mantener en el historial de cada conversación', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="session-timeout"><?php _e('Tiempo de Sesión', 'lexai'); ?></label>
                            </th>
                            <td>
                                <select id="session-timeout" name="session_timeout">
                                    <option value="1800" <?php selected($general_settings['session_timeout'] ?? 3600, 1800); ?>>30 minutos</option>
                                    <option value="3600" <?php selected($general_settings['session_timeout'] ?? 3600, 3600); ?>>1 hora</option>
                                    <option value="7200" <?php selected($general_settings['session_timeout'] ?? 3600, 7200); ?>>2 horas</option>
                                    <option value="14400" <?php selected($general_settings['session_timeout'] ?? 3600, 14400); ?>>4 horas</option>
                                    <option value="28800" <?php selected($general_settings['session_timeout'] ?? 3600, 28800); ?>>8 horas</option>
                                </select>
                                <p class="description">
                                    <?php _e('Tiempo antes de que expire una sesión de chat inactiva', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="enable-logging"><?php _e('Registro de Actividad', 'lexai'); ?></label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" id="enable-logging" name="enable_logging" 
                                           <?php checked(!empty($general_settings['enable_logging'])); ?>>
                                    <?php _e('Habilitar registro detallado de actividad', 'lexai'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Registra todas las interacciones para análisis y depuración', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="default-model"><?php _e('Modelo por Defecto', 'lexai'); ?></label>
                            </th>
                            <td>
                                <select id="default-model" name="default_model">
                                    <option value="gemini-2.5-flash" <?php selected($general_settings['default_model'] ?? 'gemini-2.5-flash', 'gemini-2.5-flash'); ?>>Gemini 2.5 Flash</option>
                                    <option value="gemini-2.5-pro" <?php selected($general_settings['default_model'] ?? 'gemini-2.5-flash', 'gemini-2.5-pro'); ?>>Gemini 2.5 Pro</option>
                                    <option value="gemini-1.5-flash" <?php selected($general_settings['default_model'] ?? 'gemini-2.5-flash', 'gemini-1.5-flash'); ?>>Gemini 1.5 Flash</option>
                                    <option value="gemini-1.5-pro" <?php selected($general_settings['default_model'] ?? 'gemini-2.5-flash', 'gemini-1.5-pro'); ?>>Gemini 1.5 Pro</option>
                                </select>
                                <p class="description">
                                    <?php _e('Modelo de Gemini a usar por defecto para nuevos agentes', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="response-language"><?php _e('Idioma de Respuestas', 'lexai'); ?></label>
                            </th>
                            <td>
                                <select id="response-language" name="response_language">
                                    <option value="es" <?php selected($general_settings['response_language'] ?? 'es', 'es'); ?>>Español</option>
                                    <option value="en" <?php selected($general_settings['response_language'] ?? 'es', 'en'); ?>>English</option>
                                </select>
                                <p class="description">
                                    <?php _e('Idioma principal para las respuestas de los agentes', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label><?php _e('Google Search', 'lexai'); ?></label>
                            </th>
                            <td>
                                <span style="color: #46b450; font-weight: bold;">✅ <?php _e('Integrado automáticamente', 'lexai'); ?></span>
                                <p class="description">
                                    <?php _e('Google Search está integrado nativamente en Gemini 2.5 Flash. Se activa automáticamente cuando es necesario.', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                    
                    <div class="lexai-form-actions">
                        <input type="submit" class="button button-primary" value="<?php _e('Guardar Configuración General', 'lexai'); ?>">
                    </div>
                </form>
            </div>
        </div>

        <!-- Usage Limits Tab -->
        <div id="usage-limits" class="lexai-tab-panel">
            <div class="lexai-admin-card">
                <h2><?php _e('Límites de Uso por Rol', 'lexai'); ?></h2>
                <p><?php _e('Configura los límites de uso diarios y mensuales para cada rol de usuario. Usa -1 para ilimitado.', 'lexai'); ?></p>
                
                <form class="lexai-admin-form" data-action="lexai_save_usage_limits">
                    <?php wp_nonce_field('lexai_usage_limits', 'usage_limits_nonce'); ?>
                    
                    <table class="lexai-form-table">
                        <?php
                        $roles = array(
                            'administrator' => 'Administrador',
                            'editor' => 'Editor',
                            'author' => 'Autor',
                            'contributor' => 'Colaborador',
                            'subscriber' => 'Suscriptor',
                            'guest' => 'Invitado (sin cuenta)'
                        );
                        
                        foreach ($roles as $role => $role_name):
                            $role_limits = $usage_limits[$role] ?? array();
                        ?>
                        <tr>
                            <th scope="row">
                                <label><?php echo esc_html($role_name); ?></label>
                            </th>
                            <td>
                                <div class="lexai-role-limits">
                                    <div class="lexai-limit-field">
                                        <label for="<?php echo $role; ?>-daily"><?php _e('Mensajes diarios:', 'lexai'); ?></label>
                                        <input type="number" id="<?php echo $role; ?>-daily" 
                                               name="<?php echo $role; ?>[daily_messages]" 
                                               value="<?php echo esc_attr($role_limits['daily_messages'] ?? ($role === 'administrator' ? -1 : 50)); ?>"
                                               min="-1" step="1" class="small-text">
                                    </div>
                                    <div class="lexai-limit-field">
                                        <label for="<?php echo $role; ?>-monthly"><?php _e('Mensajes mensuales:', 'lexai'); ?></label>
                                        <input type="number" id="<?php echo $role; ?>-monthly" 
                                               name="<?php echo $role; ?>[monthly_messages]" 
                                               value="<?php echo esc_attr($role_limits['monthly_messages'] ?? ($role === 'administrator' ? -1 : 1000)); ?>"
                                               min="-1" step="1" class="small-text">
                                    </div>
                                    <div class="lexai-limit-field">
                                        <label for="<?php echo $role; ?>-tokens"><?php _e('Tokens por mensaje:', 'lexai'); ?></label>
                                        <input type="number" id="<?php echo $role; ?>-tokens" 
                                               name="<?php echo $role; ?>[max_tokens_per_message]" 
                                               value="<?php echo esc_attr($role_limits['max_tokens_per_message'] ?? 8192); ?>"
                                               min="1024" max="32768" step="256" class="small-text">
                                    </div>
                                </div>
                                <p class="description">
                                    <?php _e('Usa -1 para límites ilimitados. Los tokens controlan la longitud de las respuestas.', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </table>
                    
                    <div class="lexai-form-actions">
                        <input type="submit" class="button button-primary" value="<?php _e('Guardar Límites de Uso', 'lexai'); ?>">
                        <button type="button" id="reset-usage-stats" class="button">
                            <?php _e('Resetear Estadísticas de Uso', 'lexai'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Pinecone Tab -->
        <div id="pinecone" class="lexai-tab-panel">
            <div class="lexai-admin-card">
                <h2><?php _e('Configuración de Pinecone', 'lexai'); ?></h2>
                <p><?php _e('Configura la conexión con Pinecone para la base de conocimientos vectorial.', 'lexai'); ?></p>
                
                <form class="lexai-admin-form" data-action="lexai_save_pinecone_settings">
                    <?php wp_nonce_field('lexai_pinecone_settings', 'pinecone_nonce'); ?>
                    
                    <table class="lexai-form-table">
                        <tr>
                            <th scope="row">
                                <label for="pinecone_api_key"><?php _e('API Key de Pinecone', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="password" id="pinecone_api_key" name="pinecone_api_key" 
                                       placeholder="<?php echo !empty($pinecone_settings['api_key']) ? 'Clave configurada (oculta por seguridad)' : 'Ingresa tu API Key de Pinecone'; ?>"
                                       class="regular-text">
                                <p class="description">
                                    <?php _e('Tu clave API de Pinecone. Puedes obtenerla en tu dashboard de Pinecone.', 'lexai'); ?>
                                    <a href="https://app.pinecone.io/" target="_blank"><?php _e('Ir a Pinecone', 'lexai'); ?></a>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="pinecone_environment"><?php _e('Environment/Host', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="pinecone_environment" name="pinecone_environment" 
                                       value="<?php echo esc_attr($pinecone_settings['environment'] ?? ''); ?>" 
                                       class="regular-text" 
                                       placeholder="us-east-1-aws.pinecone.io">
                                <p class="description">
                                    <?php _e('El host de tu índice de Pinecone (formato serverless).', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="pinecone_index"><?php _e('Nombre del Índice', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="pinecone_index" name="pinecone_index" 
                                       value="<?php echo esc_attr($pinecone_settings['index_name'] ?? 'lexai-knowledge-base'); ?>" 
                                       class="regular-text">
                                <p class="description">
                                    <?php _e('Nombre del índice donde se almacenarán los vectores.', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="embedding_model"><?php _e('Modelo de Embeddings', 'lexai'); ?></label>
                            </th>
                            <td>
                                <select id="embedding_model" name="embedding_model">
                                    <option value="text-embedding-004" <?php selected($pinecone_settings['embedding_model'] ?? 'text-embedding-004', 'text-embedding-004'); ?>>
                                        text-embedding-004 (768 dimensiones) - <?php _e('Recomendado', 'lexai'); ?>
                                    </option>
                                    <option value="text-embedding-003" <?php selected($pinecone_settings['embedding_model'] ?? '', 'text-embedding-003'); ?>>
                                        text-embedding-003 (768 dimensiones)
                                    </option>
                                </select>
                                <p class="description">
                                    <?php _e('Modelo de Gemini para generar embeddings. Recomendado: text-embedding-004', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="chunk_size"><?php _e('Tamaño de Chunk', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="number" id="chunk_size" name="chunk_size" 
                                       value="<?php echo esc_attr($pinecone_settings['chunk_size'] ?? '1000'); ?>" 
                                       min="500" max="4000" step="100" class="small-text">
                                <span class="description"><?php _e('caracteres', 'lexai'); ?></span>
                                <p class="description">
                                    <?php _e('Número de caracteres por chunk. Recomendado: 1000-2000', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="chunk_overlap"><?php _e('Overlap entre Chunks', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="number" id="chunk_overlap" name="chunk_overlap" 
                                       value="<?php echo esc_attr($pinecone_settings['chunk_overlap'] ?? '200'); ?>" 
                                       min="0" max="500" step="50" class="small-text">
                                <span class="description"><?php _e('caracteres', 'lexai'); ?></span>
                                <p class="description">
                                    <?php _e('Caracteres de solapamiento entre chunks. Recomendado: 200', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                    
                    <div class="lexai-form-actions">
                        <input type="submit" class="button button-primary" value="<?php _e('Guardar Configuración de Pinecone', 'lexai'); ?>">
                        <button type="button" id="test-pinecone-connection" class="button">
                            <?php _e('Probar Conexión', 'lexai'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Advanced Settings Tab -->
        <div id="advanced" class="lexai-tab-panel">
            <div class="lexai-admin-card">
                <h2><?php _e('Configuración Avanzada', 'lexai'); ?></h2>
                
                <form class="lexai-admin-form" data-action="lexai_save_advanced_settings">
                    <?php wp_nonce_field('lexai_advanced_settings', 'advanced_nonce'); ?>
                    
                    <table class="lexai-form-table">
                        <tr>
                            <th scope="row">
                                <label for="debug-mode"><?php _e('Modo Debug', 'lexai'); ?></label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" id="debug-mode" name="debug_mode" 
                                           <?php checked(!empty($general_settings['debug_mode'])); ?>>
                                    <?php _e('Habilitar modo debug (solo para desarrollo)', 'lexai'); ?>
                                </label>
                                <p class="description">
                                    <?php _e('Muestra información adicional de depuración en los logs', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="cache-duration"><?php _e('Duración de Caché', 'lexai'); ?></label>
                            </th>
                            <td>
                                <select id="cache-duration" name="cache_duration">
                                    <option value="300" <?php selected($general_settings['cache_duration'] ?? 3600, 300); ?>>5 minutos</option>
                                    <option value="900" <?php selected($general_settings['cache_duration'] ?? 3600, 900); ?>>15 minutos</option>
                                    <option value="1800" <?php selected($general_settings['cache_duration'] ?? 3600, 1800); ?>>30 minutos</option>
                                    <option value="3600" <?php selected($general_settings['cache_duration'] ?? 3600, 3600); ?>>1 hora</option>
                                    <option value="7200" <?php selected($general_settings['cache_duration'] ?? 3600, 7200); ?>>2 horas</option>
                                </select>
                                <p class="description">
                                    <?php _e('Tiempo de vida del caché para respuestas de búsqueda', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="max-tokens"><?php _e('Tokens Máximos', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="number" id="max-tokens" name="max_tokens" 
                                       value="<?php echo esc_attr($general_settings['max_tokens'] ?? 8192); ?>"
                                       min="1024" max="32768" step="256" class="small-text">
                                <p class="description">
                                    <?php _e('Número máximo de tokens para las respuestas de los agentes', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="temperature"><?php _e('Temperatura', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="number" id="temperature" name="temperature" 
                                       value="<?php echo esc_attr($general_settings['temperature'] ?? 0.7); ?>"
                                       min="0" max="2" step="0.1" class="small-text">
                                <p class="description">
                                    <?php _e('Controla la creatividad de las respuestas (0 = conservador, 2 = creativo)', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="cleanup-frequency"><?php _e('Limpieza Automática', 'lexai'); ?></label>
                            </th>
                            <td>
                                <select id="cleanup-frequency" name="cleanup_frequency">
                                    <option value="daily" <?php selected($general_settings['cleanup_frequency'] ?? 'daily', 'daily'); ?>>Diaria</option>
                                    <option value="weekly" <?php selected($general_settings['cleanup_frequency'] ?? 'daily', 'weekly'); ?>>Semanal</option>
                                    <option value="monthly" <?php selected($general_settings['cleanup_frequency'] ?? 'daily', 'monthly'); ?>>Mensual</option>
                                    <option value="disabled" <?php selected($general_settings['cleanup_frequency'] ?? 'daily', 'disabled'); ?>>Deshabilitada</option>
                                </select>
                                <p class="description">
                                    <?php _e('Frecuencia de limpieza automática de archivos temporales y logs antiguos', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="rate-limit-window"><?php _e('Ventana de Rate Limiting', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="number" id="rate-limit-window" name="rate_limit_window" 
                                       value="<?php echo esc_attr($general_settings['rate_limit_window'] ?? 60); ?>"
                                       min="30" max="3600" step="30" class="small-text">
                                <span class="description"><?php _e('segundos', 'lexai'); ?></span>
                                <p class="description">
                                    <?php _e('Ventana de tiempo para el rate limiting de requests', 'lexai'); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                    
                    <div class="lexai-form-actions">
                        <input type="submit" class="button button-primary" value="<?php _e('Guardar Configuración Avanzada', 'lexai'); ?>">
                        <button type="button" class="button" id="lexai-clear-cache">
                            <?php _e('Limpiar Caché', 'lexai'); ?>
                        </button>
                        <button type="button" class="button" id="lexai-run-cleanup">
                            <?php _e('Ejecutar Limpieza', 'lexai'); ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Import/Export Tab -->
        <div id="import-export" class="lexai-tab-panel">
            <div class="lexai-admin-grid">
                <div class="lexai-admin-card">
                    <h2><?php _e('Exportar Configuración', 'lexai'); ?></h2>
                    <p><?php _e('Exporta la configuración actual para hacer respaldo o transferir a otro sitio.', 'lexai'); ?></p>
                    
                    <div class="lexai-export-options">
                        <label>
                            <input type="checkbox" id="export-settings" checked>
                            <?php _e('Configuración general', 'lexai'); ?>
                        </label>
                        <label>
                            <input type="checkbox" id="export-agents" checked>
                            <?php _e('Agentes configurados', 'lexai'); ?>
                        </label>
                        <label>
                            <input type="checkbox" id="export-tools">
                            <?php _e('Herramientas personalizadas', 'lexai'); ?>
                        </label>
                        <label>
                            <input type="checkbox" id="export-usage-limits">
                            <?php _e('Límites de uso', 'lexai'); ?>
                        </label>
                    </div>
                    
                    <div class="lexai-form-actions">
                        <button type="button" class="button button-primary" id="lexai-export-config">
                            <?php _e('Exportar Configuración', 'lexai'); ?>
                        </button>
                    </div>
                </div>
                
                <div class="lexai-admin-card">
                    <h2><?php _e('Importar Configuración', 'lexai'); ?></h2>
                    <p><?php _e('Importa configuración desde un archivo de respaldo.', 'lexai'); ?></p>
                    
                    <form id="lexai-import-form" enctype="multipart/form-data">
                        <?php wp_nonce_field('lexai_import_config', 'import_nonce'); ?>
                        
                        <table class="lexai-form-table">
                            <tr>
                                <th scope="row">
                                    <label for="import-file"><?php _e('Archivo de Configuración', 'lexai'); ?></label>
                                </th>
                                <td>
                                    <input type="file" id="import-file" name="import_file" accept=".json">
                                    <p class="description">
                                        <?php _e('Selecciona un archivo JSON de configuración exportado previamente', 'lexai'); ?>
                                    </p>
                                </td>
                            </tr>
                            
                            <tr>
                                <th scope="row">
                                    <label for="import-mode"><?php _e('Modo de Importación', 'lexai'); ?></label>
                                </th>
                                <td>
                                    <select id="import-mode" name="import_mode">
                                        <option value="merge"><?php _e('Combinar con configuración actual', 'lexai'); ?></option>
                                        <option value="replace"><?php _e('Reemplazar configuración actual', 'lexai'); ?></option>
                                    </select>
                                    <p class="description">
                                        <?php _e('Combinar mantiene la configuración actual y agrega la nueva. Reemplazar sobrescribe todo.', 'lexai'); ?>
                                    </p>
                                </td>
                            </tr>
                        </table>
                        
                        <div class="lexai-form-actions">
                            <button type="submit" class="button button-primary">
                                <?php _e('Importar Configuración', 'lexai'); ?>
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="lexai-admin-card">
                    <h2><?php _e('Resetear Sistema', 'lexai'); ?></h2>
                    <p><?php _e('Opciones para resetear diferentes partes del sistema.', 'lexai'); ?></p>
                    
                    <div class="lexai-reset-options">
                        <button type="button" class="button" id="reset-conversations">
                            <?php _e('Resetear Conversaciones', 'lexai'); ?>
                        </button>
                        <p class="description"><?php _e('Elimina todas las conversaciones y mensajes', 'lexai'); ?></p>
                        
                        <button type="button" class="button" id="reset-usage-stats">
                            <?php _e('Resetear Estadísticas de Uso', 'lexai'); ?>
                        </button>
                        <p class="description"><?php _e('Reinicia los contadores de uso de todos los usuarios', 'lexai'); ?></p>
                        
                        <button type="button" class="button button-delete" id="reset-all-data">
                            <?php _e('Resetear Todos los Datos', 'lexai'); ?>
                        </button>
                        <p class="description"><?php _e('⚠️ PELIGRO: Elimina TODOS los datos de LexAI (excepto configuración)', 'lexai'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.lexai-role-limits {
    display: flex;
    gap: 20px;
    align-items: flex-start;
    flex-wrap: wrap;
}

.lexai-limit-field {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.lexai-limit-field label {
    font-size: 12px;
    font-weight: 600;
    color: #646970;
}

.lexai-limit-field input {
    width: 100px;
}

.lexai-export-options {
    margin: 15px 0;
}

.lexai-export-options label {
    display: block;
    margin-bottom: 8px;
}

.lexai-export-options input[type="checkbox"] {
    margin-right: 8px;
}

.lexai-reset-options {
    margin-top: 15px;
}

.lexai-reset-options .button {
    display: block;
    margin-bottom: 10px;
    width: auto;
}

.lexai-reset-options .description {
    margin-bottom: 20px;
    font-style: italic;
}

.button-delete {
    background: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.button-delete:hover {
    background: #c82333;
    border-color: #bd2130;
    color: #fff;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Form submissions
    $('.lexai-admin-form').on('submit', function(e) {
        e.preventDefault();
        
        const form = $(this);
        const action = form.data('action');
        const submitBtn = form.find('input[type="submit"]');
        const originalText = submitBtn.val();
        
        submitBtn.val('Guardando...').prop('disabled', true);
        
        const formData = new FormData(this);
        formData.append('action', action);
        
        $.ajax({
            url: lexaiAdmin.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                submitBtn.val(originalText).prop('disabled', false);
                
                if (response.success) {
                    showNotification('Configuración guardada correctamente', 'success');
                } else {
                    showNotification('Error: ' + response.data.message, 'error');
                }
            },
            error: function() {
                submitBtn.val(originalText).prop('disabled', false);
                showNotification('Error de conexión', 'error');
            }
        });
    });
    
    // Clear cache
    $('#lexai-clear-cache').on('click', function() {
        if (confirm('¿Estás seguro de que quieres limpiar el caché?')) {
            $(this).prop('disabled', true).text('Limpiando...');
            
            $.ajax({
                url: lexaiAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lexai_clear_cache',
                    nonce: lexaiAdmin.nonce
                },
                success: function(response) {
                    $('#lexai-clear-cache').prop('disabled', false).text('Limpiar Caché');
                    
                    if (response.success) {
                        showNotification('Caché limpiado correctamente', 'success');
                    } else {
                        showNotification('Error: ' + response.data.message, 'error');
                    }
                },
                error: function() {
                    $('#lexai-clear-cache').prop('disabled', false).text('Limpiar Caché');
                    showNotification('Error de conexión', 'error');
                }
            });
        }
    });
    
    // Run cleanup
    $('#lexai-run-cleanup').on('click', function() {
        if (confirm('¿Ejecutar limpieza de archivos temporales?')) {
            $(this).prop('disabled', true).text('Ejecutando...');
            
            $.ajax({
                url: lexaiAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lexai_run_cleanup',
                    nonce: lexaiAdmin.nonce
                },
                success: function(response) {
                    $('#lexai-run-cleanup').prop('disabled', false).text('Ejecutar Limpieza');
                    
                    if (response.success) {
                        showNotification('Limpieza ejecutada correctamente', 'success');
                    } else {
                        showNotification('Error: ' + response.data.message, 'error');
                    }
                },
                error: function() {
                    $('#lexai-run-cleanup').prop('disabled', false).text('Ejecutar Limpieza');
                    showNotification('Error de conexión', 'error');
                }
            });
        }
    });
    
    // Test Pinecone connection
    $('#test-pinecone-connection').on('click', function() {
        $(this).prop('disabled', true).text('Probando...');
        
        $.ajax({
            url: lexaiAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_vector_test_connection',
                nonce: lexaiAdmin.nonce
            },
            success: function(response) {
                $('#test-pinecone-connection').prop('disabled', false).text('Probar Conexión');
                
                if (response.success) {
                    showNotification('Conexión exitosa con Pinecone', 'success');
                } else {
                    showNotification('Error de conexión: ' + response.data.message, 'error');
                }
            },
            error: function() {
                $('#test-pinecone-connection').prop('disabled', false).text('Probar Conexión');
                showNotification('Error de conexión', 'error');
            }
        });
    });
    
    // Export configuration
    $('#lexai-export-config').on('click', function() {
        const options = {
            settings: $('#export-settings').is(':checked'),
            agents: $('#export-agents').is(':checked'),
            tools: $('#export-tools').is(':checked'),
            usage_limits: $('#export-usage-limits').is(':checked')
        };
        
        $(this).prop('disabled', true).text('Exportando...');
        
        $.ajax({
            url: lexaiAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_export_config',
                nonce: lexaiAdmin.nonce,
                options: options
            },
            success: function(response) {
                $('#lexai-export-config').prop('disabled', false).text('Exportar Configuración');
                
                if (response.success) {
                    // Download file
                    const blob = new Blob([JSON.stringify(response.data.config, null, 2)], {
                        type: 'application/json'
                    });
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'lexai-config-' + new Date().toISOString().split('T')[0] + '.json';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    showNotification('Configuración exportada correctamente', 'success');
                } else {
                    showNotification('Error: ' + response.data.message, 'error');
                }
            },
            error: function() {
                $('#lexai-export-config').prop('disabled', false).text('Exportar Configuración');
                showNotification('Error de conexión', 'error');
            }
        });
    });
    
    // Import configuration
    $('#lexai-import-form').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        formData.append('action', 'lexai_import_config');
        
        const submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).text('Importando...');
        
        $.ajax({
            url: lexaiAdmin.ajaxUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                submitBtn.prop('disabled', false).text('Importar Configuración');
                
                if (response.success) {
                    showNotification('Configuración importada correctamente', 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showNotification('Error: ' + response.data.message, 'error');
                }
            },
            error: function() {
                submitBtn.prop('disabled', false).text('Importar Configuración');
                showNotification('Error de conexión', 'error');
            }
        });
    });
    
    // Reset functions
    $('#reset-conversations').on('click', function() {
        if (confirm('¿Estás seguro de que quieres eliminar TODAS las conversaciones? Esta acción no se puede deshacer.')) {
            resetData('conversations', $(this));
        }
    });
    
    $('#reset-usage-stats').on('click', function() {
        if (confirm('¿Estás seguro de que quieres resetear todas las estadísticas de uso?')) {
            resetData('usage_stats', $(this));
        }
    });
    
    $('#reset-all-data').on('click', function() {
        const confirmText = prompt('Esta acción eliminará TODOS los datos de LexAI. Escribe "ELIMINAR TODO" para confirmar:');
        if (confirmText === 'ELIMINAR TODO') {
            resetData('all_data', $(this));
        }
    });
    
    function resetData(type, button) {
        const originalText = button.text();
        button.prop('disabled', true).text('Procesando...');
        
        $.ajax({
            url: lexaiAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_reset_data',
                nonce: lexaiAdmin.nonce,
                reset_type: type
            },
            success: function(response) {
                button.prop('disabled', false).text(originalText);
                
                if (response.success) {
                    showNotification('Datos reseteados correctamente', 'success');
                } else {
                    showNotification('Error: ' + response.data.message, 'error');
                }
            },
            error: function() {
                button.prop('disabled', false).text(originalText);
                showNotification('Error de conexión', 'error');
            }
        });
    }
    
    function showNotification(message, type) {
        const notification = $(`
            <div class="notice notice-${type} is-dismissible">
                <p>${message}</p>
            </div>
        `);
        
        $('.lexai-admin-wrap h1').after(notification);
        
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 5000);
    }
});
</script>