<?php
/**
 * Vector Database Management View
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$settings = get_option('lexai_settings', array());
$pinecone_settings = $settings['pinecone'] ?? array();
?>

<div class="wrap">
    <h1><?php _e('Base de Datos Vectorial - Pinecone', 'lexai'); ?></h1>
    
    <!-- Navigation Tabs -->
    <nav class="nav-tab-wrapper">
        <a href="#configuration" class="nav-tab nav-tab-active" data-tab="configuration">
            <?php _e('Configuración', 'lexai'); ?>
        </a>
        <a href="#upload" class="nav-tab" data-tab="upload">
            <?php _e('Subir Documentos', 'lexai'); ?>
        </a>
        <a href="#queue" class="nav-tab" data-tab="queue">
            <?php _e('Cola de Procesamiento', 'lexai'); ?>
        </a>
        <a href="#management" class="nav-tab" data-tab="management">
            <?php _e('Gestión de Índices', 'lexai'); ?>
        </a>
    </nav>

    <!-- Configuration Tab -->
    <div id="configuration-tab" class="tab-content active">
        <div class="lexai-admin-section">
            <div class="notice notice-info">
                <p><strong><?php _e('ℹ️ Configuración Centralizada', 'lexai'); ?></strong></p>
                <p><?php _e('La configuración de Pinecone se gestiona desde el panel principal de API Keys para mantener la seguridad y consistencia.', 'lexai'); ?></p>
                <p>
                    <a href="<?php echo admin_url('admin.php?page=lexai-api-keys'); ?>" class="button button-primary">
                        <?php _e('Ir a Configuración de API Keys', 'lexai'); ?>
                    </a>
                </p>
            </div>

            <h2><?php _e('Estado de Configuración de Pinecone', 'lexai'); ?></h2>

            <div class="lexai-config-status">
                <?php
                // Check if Pinecone is configured
                $pinecone_handler = new LexAI_Pinecone_Handler();
                $is_configured = $pinecone_handler->is_configured();
                ?>

                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('Estado de Configuración', 'lexai'); ?></th>
                        <td>
                            <?php if ($is_configured): ?>
                                <span style="color: #46b450; font-weight: bold;">✅ <?php _e('Configurado correctamente', 'lexai'); ?></span>
                            <?php else: ?>
                                <span style="color: #dc3232; font-weight: bold;">❌ <?php _e('Configuración requerida', 'lexai'); ?></span>
                                <p class="description">
                                    <a href="<?php echo admin_url('admin.php?page=lexai-api-keys'); ?>">
                                        <?php _e('Configurar Pinecone ahora', 'lexai'); ?>
                                    </a>
                                </p>
                            <?php endif; ?>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Índice Configurado', 'lexai'); ?></th>
                        <td>
                            <code><?php echo esc_html($pinecone_settings['index_name'] ?? 'lexai-knowledge-base'); ?></code>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Host/Environment', 'lexai'); ?></th>
                        <td>
                            <?php
                            $host = $pinecone_settings['host'] ?? $pinecone_settings['environment'] ?? '';
                            echo $host ? '<code>' . esc_html($host) . '</code>' : '<em>' . __('No configurado', 'lexai') . '</em>';
                            ?>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row"><?php _e('Namespaces Disponibles', 'lexai'); ?></th>
                        <td>
                            <div class="lexai-namespaces-list">
                                <div class="namespace-item">
                                    <strong>leyesycodigos</strong> - <?php _e('Leyes, códigos y normativas mexicanas', 'lexai'); ?>
                                </div>
                                <div class="namespace-item">
                                    <strong>jurisprudencia</strong> - <?php _e('Jurisprudencia y precedentes judiciales', 'lexai'); ?>
                                </div>
                                <div class="namespace-item">
                                    <strong>tesisscjn</strong> - <?php _e('Tesis jurisprudenciales de la SCJN', 'lexai'); ?>
                                </div>
                                <div class="namespace-item">
                                    <strong>formatos</strong> - <?php _e('Templates y formatos de documentos legales', 'lexai'); ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="embedding_model"><?php _e('Modelo de Embeddings', 'lexai'); ?></label>
                        </th>
                        <td>
                            <select id="embedding_model" name="embedding_model">
                                <option value="text-embedding-004" <?php selected($pinecone_settings['embedding_model'] ?? 'text-embedding-004', 'text-embedding-004'); ?>>
                                    text-embedding-004 (768 dimensiones)
                                </option>
                                <option value="gemini-embedding-exp-03-07" <?php selected($pinecone_settings['embedding_model'] ?? '', 'gemini-embedding-exp-03-07'); ?>>
                                    gemini-embedding-exp-03-07 (768 dimensiones)
                                </option>
                            </select>
                            <p class="description">
                                <?php _e('Modelo de Gemini para generar embeddings. Recomendado: text-embedding-004', 'lexai'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="chunk_size"><?php _e('Tamaño de Chunk', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="number" 
                                   id="chunk_size" 
                                   name="chunk_size" 
                                   value="<?php echo esc_attr($pinecone_settings['chunk_size'] ?? '1000'); ?>" 
                                   min="100" 
                                   max="8000" 
                                   class="small-text" />
                            <p class="description">
                                <?php _e('Número de caracteres por chunk. Recomendado: 1000-2000', 'lexai'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="chunk_overlap"><?php _e('Overlap entre Chunks', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="number" 
                                   id="chunk_overlap" 
                                   name="chunk_overlap" 
                                   value="<?php echo esc_attr($pinecone_settings['chunk_overlap'] ?? '200'); ?>" 
                                   min="0" 
                                   max="500" 
                                   class="small-text" />
                            <p class="description">
                                <?php _e('Caracteres de solapamiento entre chunks. Recomendado: 200', 'lexai'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <div class="lexai-admin-actions">
                    <button type="submit" class="button-primary">
                        <?php _e('Guardar Configuración', 'lexai'); ?>
                    </button>
                    <button type="button" id="test-connection" class="button-secondary">
                        <?php _e('Probar Conexión', 'lexai'); ?>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Upload Tab -->
    <div id="upload-tab" class="tab-content">
        <div class="lexai-admin-section">
            <h2><?php _e('Subir Documentos Legales', 'lexai'); ?></h2>
            
            <div class="lexai-upload-area">
                <div class="lexai-dropzone" id="document-dropzone">
                    <div class="lexai-dropzone-content">
                        <i class="dashicons dashicons-upload"></i>
                        <h3><?php _e('Arrastra archivos aquí o haz clic para seleccionar', 'lexai'); ?></h3>
                        <p><?php _e('Formatos soportados: PDF, DOC, DOCX, TXT', 'lexai'); ?></p>
                        <p><?php _e('Tamaño máximo: 50MB por archivo', 'lexai'); ?></p>
                    </div>
                    <input type="file" id="file-input" multiple accept=".pdf,.doc,.docx,.txt" style="display: none;">
                </div>
            </div>
            
            <div class="lexai-metadata-form" id="metadata-form" style="display: none;">
                <h3><?php _e('Metadatos del Documento', 'lexai'); ?></h3>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="doc_title"><?php _e('Título del Documento', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="doc_title" name="doc_title" class="regular-text" />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="doc_namespace"><?php _e('Namespace de Destino', 'lexai'); ?></label>
                        </th>
                        <td>
                            <select id="doc_namespace" name="doc_namespace" required>
                                <option value=""><?php _e('Selecciona un namespace...', 'lexai'); ?></option>
                                <option value="leyesycodigos"><?php _e('Leyes y Códigos (leyesycodigos)', 'lexai'); ?></option>
                                <option value="jurisprudencia"><?php _e('Jurisprudencia (jurisprudencia)', 'lexai'); ?></option>
                                <option value="tesisscjn"><?php _e('Tesis SCJN (tesisscjn)', 'lexai'); ?></option>
                                <option value="formatos"><?php _e('Formatos y Templates (formatos)', 'lexai'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('Selecciona el namespace donde se almacenará el documento según su tipo de contenido legal.', 'lexai'); ?>
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="doc_category"><?php _e('Categoría Legal', 'lexai'); ?></label>
                        </th>
                        <td>
                            <select id="doc_category" name="doc_category">
                                <option value="civil"><?php _e('Derecho Civil', 'lexai'); ?></option>
                                <option value="penal"><?php _e('Derecho Penal', 'lexai'); ?></option>
                                <option value="laboral"><?php _e('Derecho Laboral', 'lexai'); ?></option>
                                <option value="fiscal"><?php _e('Derecho Fiscal', 'lexai'); ?></option>
                                <option value="constitucional"><?php _e('Derecho Constitucional', 'lexai'); ?></option>
                                <option value="administrativo"><?php _e('Derecho Administrativo', 'lexai'); ?></option>
                                <option value="mercantil"><?php _e('Derecho Mercantil', 'lexai'); ?></option>
                                <option value="otros"><?php _e('Otros', 'lexai'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('Categoría específica dentro del namespace seleccionado.', 'lexai'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="doc_source"><?php _e('Fuente', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="doc_source" name="doc_source" class="regular-text" 
                                   placeholder="Ej: Código Civil Federal, SCJN, etc." />
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="doc_tags"><?php _e('Etiquetas', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="doc_tags" name="doc_tags" class="regular-text" 
                                   placeholder="Separadas por comas" />
                        </td>
                    </tr>
                </table>
                
                <div class="lexai-admin-actions">
                    <button type="button" id="start-processing" class="button-primary">
                        <?php _e('Procesar y Subir', 'lexai'); ?>
                    </button>
                    <button type="button" id="cancel-upload" class="button-secondary">
                        <?php _e('Cancelar', 'lexai'); ?>
                    </button>
                </div>
            </div>
            
            <div class="lexai-upload-progress" id="upload-progress" style="display: none;">
                <h3><?php _e('Progreso de Procesamiento', 'lexai'); ?></h3>
                <div class="lexai-progress-bar">
                    <div class="lexai-progress-fill" id="progress-fill"></div>
                </div>
                <div class="lexai-progress-text" id="progress-text">0%</div>
                <div class="lexai-progress-details" id="progress-details"></div>
            </div>
        </div>
    </div>

    <!-- Queue Tab -->
    <div id="queue-tab" class="tab-content">
        <div class="lexai-admin-section">
            <h2><?php _e('Cola de Procesamiento', 'lexai'); ?></h2>
            
            <div class="lexai-queue-controls">
                <button type="button" id="refresh-queue" class="button-secondary">
                    <?php _e('Actualizar', 'lexai'); ?>
                </button>
                <button type="button" id="clear-completed" class="button-secondary">
                    <?php _e('Limpiar Completados', 'lexai'); ?>
                </button>
                <button type="button" id="pause-queue" class="button-secondary">
                    <?php _e('Pausar Cola', 'lexai'); ?>
                </button>
            </div>
            
            <div class="lexai-queue-stats">
                <div class="lexai-stat-card">
                    <h4><?php _e('En Cola', 'lexai'); ?></h4>
                    <span class="lexai-stat-number" id="queue-pending">0</span>
                </div>
                <div class="lexai-stat-card">
                    <h4><?php _e('Procesando', 'lexai'); ?></h4>
                    <span class="lexai-stat-number" id="queue-processing">0</span>
                </div>
                <div class="lexai-stat-card">
                    <h4><?php _e('Completados', 'lexai'); ?></h4>
                    <span class="lexai-stat-number" id="queue-completed">0</span>
                </div>
                <div class="lexai-stat-card">
                    <h4><?php _e('Errores', 'lexai'); ?></h4>
                    <span class="lexai-stat-number" id="queue-errors">0</span>
                </div>
            </div>
            
            <div class="lexai-queue-list">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th><?php _e('Archivo', 'lexai'); ?></th>
                            <th><?php _e('Estado', 'lexai'); ?></th>
                            <th><?php _e('Progreso', 'lexai'); ?></th>
                            <th><?php _e('Chunks', 'lexai'); ?></th>
                            <th><?php _e('Fecha', 'lexai'); ?></th>
                            <th><?php _e('Acciones', 'lexai'); ?></th>
                        </tr>
                    </thead>
                    <tbody id="queue-table-body">
                        <!-- Queue items will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Management Tab -->
    <div id="management-tab" class="tab-content">
        <div class="lexai-admin-section">
            <h2><?php _e('Gestión de Índices', 'lexai'); ?></h2>
            
            <div class="lexai-index-info">
                <h3><?php _e('Información del Índice Actual', 'lexai'); ?></h3>
                <div class="lexai-index-stats" id="index-stats">
                    <!-- Index stats will be loaded here -->
                </div>
            </div>
            
            <div class="lexai-index-actions">
                <h3><?php _e('Acciones del Índice', 'lexai'); ?></h3>
                
                <div class="lexai-action-group">
                    <button type="button" id="refresh-index-stats" class="button-secondary">
                        <?php _e('Actualizar Estadísticas', 'lexai'); ?>
                    </button>
                    <button type="button" id="test-search" class="button-secondary">
                        <?php _e('Probar Búsqueda', 'lexai'); ?>
                    </button>
                </div>
                
                <div class="lexai-action-group">
                    <h4><?php _e('Gestión de Datos', 'lexai'); ?></h4>
                    <button type="button" id="export-metadata" class="button-secondary">
                        <?php _e('Exportar Metadatos', 'lexai'); ?>
                    </button>
                    <button type="button" id="clear-index" class="button-delete">
                        <?php _e('Limpiar Índice', 'lexai'); ?>
                    </button>
                </div>
            </div>
            
            <div class="lexai-search-test" id="search-test" style="display: none;">
                <h3><?php _e('Probar Búsqueda en Base de Conocimientos', 'lexai'); ?></h3>
                <div class="lexai-search-form">
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="search-query"><?php _e('Consulta', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="search-query"
                                       placeholder="<?php _e('Ej: ¿Cuáles son las obligaciones del arrendador?', 'lexai'); ?>"
                                       class="regular-text" style="width: 100%;" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="search-namespace"><?php _e('Namespace', 'lexai'); ?></label>
                            </th>
                            <td>
                                <select id="search-namespace">
                                    <option value=""><?php _e('Todos los namespaces', 'lexai'); ?></option>
                                    <option value="leyesycodigos"><?php _e('Leyes y Códigos', 'lexai'); ?></option>
                                    <option value="jurisprudencia"><?php _e('Jurisprudencia', 'lexai'); ?></option>
                                    <option value="tesisscjn"><?php _e('Tesis SCJN', 'lexai'); ?></option>
                                    <option value="formatos"><?php _e('Formatos y Templates', 'lexai'); ?></option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="search-limit"><?php _e('Resultados', 'lexai'); ?></label>
                            </th>
                            <td>
                                <select id="search-limit">
                                    <option value="5">5</option>
                                    <option value="10">10</option>
                                    <option value="15">15</option>
                                    <option value="20">20</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="search-threshold"><?php _e('Umbral de Similitud', 'lexai'); ?></label>
                            </th>
                            <td>
                                <input type="range" id="search-threshold" min="0" max="1" step="0.1" value="0.7" />
                                <span id="threshold-value">0.7</span>
                            </td>
                        </tr>
                    </table>

                    <div class="lexai-admin-actions">
                        <button type="button" id="execute-search" class="button-primary">
                            <?php _e('Buscar', 'lexai'); ?>
                        </button>
                    </div>
                </div>
                <div class="lexai-search-results" id="search-results"></div>
            </div>
        </div>
    </div>
</div>

<style>
.lexai-admin-section {
    background: #fff;
    padding: 20px;
    margin: 20px 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.lexai-dropzone {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.lexai-dropzone:hover {
    border-color: #0073aa;
    background-color: #f8f9fa;
}

.lexai-dropzone.dragover {
    border-color: #0073aa;
    background-color: #e3f2fd;
}

.lexai-progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.lexai-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #00a0d2);
    width: 0%;
    transition: width 0.3s ease;
}

.lexai-queue-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.lexai-stat-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
}

.lexai-stat-number {
    font-size: 2em;
    font-weight: bold;
    color: #0073aa;
    display: block;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.nav-tab.nav-tab-active {
    background: #fff;
    border-bottom: 1px solid #fff;
}

.lexai-admin-actions {
    margin-top: 20px;
}

.lexai-admin-actions .button {
    margin-right: 10px;
}

.button-delete {
    background: #dc3545;
    border-color: #dc3545;
    color: #fff;
}

.button-delete:hover {
    background: #c82333;
    border-color: #bd2130;
}
</style>
