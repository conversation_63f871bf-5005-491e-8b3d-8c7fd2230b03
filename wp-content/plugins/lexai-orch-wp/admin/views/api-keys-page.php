<?php
/**
 * API Keys Management Page
 *
 * @package LexAI
 * @since 1.0.1
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get API handler
$api_handler = new LexAI_API_Handler();
$api_keys = $api_handler->get_api_keys();
?>

<div class="wrap lexai-admin-wrap">
    <h1><?php _e('Gestión de Claves API', 'lexai'); ?></h1>
    
    <div class="lexai-admin-header">
        <h2><?php _e('Configuración de APIs', 'lexai'); ?></h2>
        <p><?php _e('Configura las claves API necesarias para el funcionamiento de LexAI. Todas las claves se almacenan de forma encriptada.', 'lexai'); ?></p>
    </div>

    <div class="lexai-admin-grid">
        <!-- Gemini API Configuration -->
        <div class="lexai-admin-card">
            <h2><?php _e('Google Gemini API', 'lexai'); ?></h2>
            <p><?php _e('Clave API para acceder a los modelos de Gemini de Google.', 'lexai'); ?></p>
            
            <form class="lexai-admin-form" data-action="lexai_save_api_key">
                <input type="hidden" name="provider" value="gemini">
                
                <table class="lexai-form-table">
                    <tr>
                        <th scope="row">
                            <label for="gemini-name"><?php _e('Nombre', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="gemini-name" name="name" 
                                   value="<?php echo esc_attr(get_api_key_name($api_keys, 'gemini')); ?>"
                                   placeholder="Ej: Clave Principal Gemini">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="gemini-api-key"><?php _e('Clave API', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="gemini-api-key" name="api_key" 
                                   placeholder="<?php echo has_api_key($api_keys, 'gemini') ? 'Clave configurada (oculta por seguridad)' : 'Ingresa tu clave API de Gemini'; ?>">
                            <p class="description">
                                <?php _e('Obtén tu clave API en:', 'lexai'); ?> 
                                <a href="https://aistudio.google.com/app/apikey" target="_blank">Google AI Studio</a>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <div class="lexai-form-actions">
                    <input type="submit" class="button button-primary" value="<?php _e('Guardar Clave Gemini', 'lexai'); ?>">
                    <button type="button" class="button lexai-test-api" data-api-type="gemini">
                        <?php _e('Probar Conexión', 'lexai'); ?>
                    </button>
                    
                    <?php if (has_api_key($api_keys, 'gemini')): ?>
                        <button type="button" class="button button-link-delete lexai-delete-api-key" 
                                data-provider="gemini">
                            <?php _e('Eliminar Clave', 'lexai'); ?>
                        </button>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Pinecone Configuration -->
        <div class="lexai-admin-card">
            <h2><?php _e('Pinecone Vector Database', 'lexai'); ?></h2>
            <p><?php _e('Configuración para la base de datos vectorial de conocimientos.', 'lexai'); ?></p>
            
            <?php
            $pinecone_settings = get_option('lexai_settings', array())['pinecone'] ?? array();
            ?>
            
            <form class="lexai-admin-form" data-action="lexai_save_pinecone_config">
                <table class="lexai-form-table">
                    <tr>
                        <th scope="row">
                            <label for="pinecone-api-key"><?php _e('API Key', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="pinecone-api-key" name="api_key" 
                                   placeholder="<?php echo !empty($pinecone_settings['api_key']) ? 'Clave configurada (oculta por seguridad)' : 'Ingresa tu API Key de Pinecone'; ?>">
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="pinecone-host"><?php _e('Host del Índice', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="pinecone-host" name="host"
                                   value="<?php echo esc_attr($pinecone_settings['host'] ?? ''); ?>"
                                   placeholder="your-index-abc123.svc.us-east1-aws.pinecone.io">
                            <p class="description">
                                <?php _e('El host único de tu índice. Obtén este valor desde la consola de Pinecone.', 'lexai'); ?>
                                <br><strong><?php _e('Cómo obtener el host:', 'lexai'); ?></strong>
                                <br>1. <?php _e('Ve a la', 'lexai'); ?> <a href="https://app.pinecone.io" target="_blank"><?php _e('consola de Pinecone', 'lexai'); ?></a>
                                <br>2. <?php _e('Selecciona tu proyecto e índice', 'lexai'); ?>
                                <br>3. <?php _e('Copia la URL que aparece bajo "HOST"', 'lexai'); ?>
                                <br><strong><?php _e('Nota:', 'lexai'); ?></strong> <?php _e('El "environment" ya no se usa en la nueva API de Pinecone (2024+).', 'lexai'); ?>
                            </p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="pinecone-index"><?php _e('Nombre del Índice', 'lexai'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="pinecone-index" name="index_name" 
                                   value="<?php echo esc_attr($pinecone_settings['index_name'] ?? 'lexai-knowledge-base'); ?>"
                                   placeholder="lexai-knowledge-base">
                            <p class="description">
                                <?php _e('Nombre del índice donde se almacenarán los vectores', 'lexai'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <div class="lexai-form-actions">
                    <input type="submit" class="button button-primary" value="<?php _e('Guardar Configuración', 'lexai'); ?>">
                    <button type="button" class="button lexai-test-api" data-api-type="pinecone">
                        <?php _e('Probar Conexión', 'lexai'); ?>
                    </button>
                </div>
            </form>
        </div>

        <!-- Google Search Information -->
        <div class="lexai-admin-card">
            <h2><?php _e('Google Search (Integrado)', 'lexai'); ?></h2>
            <div class="notice notice-info inline">
                <p><strong><?php _e('✅ Google Search ya está integrado en Gemini', 'lexai'); ?></strong></p>
                <p><?php _e('Los modelos Gemini 2.5 Flash y superiores incluyen la herramienta Google Search nativa con grounding automático. No se requiere configuración adicional.', 'lexai'); ?></p>
                <ul style="margin-left: 20px;">
                    <li><?php _e('• Usa la misma API Key de Gemini', 'lexai'); ?></li>
                    <li><?php _e('• Incluye citas y metadatos automáticamente', 'lexai'); ?></li>
                    <li><?php _e('• Soporte completo para español', 'lexai'); ?></li>
                    <li><?php _e('• Facturación integrada con Gemini', 'lexai'); ?></li>
                </ul>
                <p><strong><?php _e('Estado:', 'lexai'); ?></strong>
                    <span style="color: #46b450;"><?php _e('Activo automáticamente con tus claves de Gemini', 'lexai'); ?></span>
                </p>
            </div>
        </div>

        <!-- API Status Overview -->
        <div class="lexai-admin-card">
            <h2><?php _e('Estado de las APIs', 'lexai'); ?></h2>
            
            <div class="lexai-api-status">
                <div class="lexai-status-item">
                    <span class="lexai-status-indicator <?php echo has_api_key($api_keys, 'gemini') ? 'lexai-status-ok' : 'lexai-status-error'; ?>"></span>
                    <strong>Gemini API:</strong>
                    <span><?php echo has_api_key($api_keys, 'gemini') ? 'Configurada' : 'No configurada'; ?></span>
                </div>
                
                <div class="lexai-status-item">
                    <span class="lexai-status-indicator <?php echo !empty($pinecone_settings['api_key']) ? 'lexai-status-ok' : 'lexai-status-error'; ?>"></span>
                    <strong>Pinecone:</strong>
                    <span><?php echo !empty($pinecone_settings['api_key']) ? 'Configurada' : 'No configurada'; ?></span>
                </div>
                
                <div class="lexai-status-item">
                    <span class="lexai-status-indicator <?php echo !empty($google_search_settings['enabled']) ? 'lexai-status-ok' : 'lexai-status-warning'; ?>"></span>
                    <strong>Google Search:</strong>
                    <span><?php echo !empty($google_search_settings['enabled']) ? 'Habilitada' : 'Deshabilitada'; ?></span>
                </div>
            </div>
            
            <div class="lexai-api-usage">
                <h4><?php _e('Uso de APIs (últimos 30 días)', 'lexai'); ?></h4>
                <div id="lexai-api-usage-stats">
                    <p><?php _e('Cargando estadísticas...', 'lexai'); ?></p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.lexai-api-status {
    margin-bottom: 20px;
}

.lexai-status-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.lexai-status-item:last-child {
    border-bottom: none;
}

.lexai-status-item strong {
    margin-left: 8px;
    margin-right: 8px;
    min-width: 120px;
}

.lexai-form-actions {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.lexai-form-actions .button {
    margin-right: 10px;
}

.lexai-api-usage {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #ddd;
}

.lexai-api-usage h4 {
    margin-bottom: 10px;
}

#lexai-api-usage-stats {
    background: #f9f9f9;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Load API usage statistics
    loadApiUsageStats();
    
    // Delete API key
    $('.lexai-delete-api-key').on('click', function() {
        const provider = $(this).data('provider');
        
        if (confirm('¿Estás seguro de que quieres eliminar esta clave API?')) {
            $.ajax({
                url: lexaiAdmin.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'lexai_delete_api_key',
                    nonce: lexaiAdmin.nonce,
                    provider: provider
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + response.data.message);
                    }
                }
            });
        }
    });
    
    function loadApiUsageStats() {
        $.ajax({
            url: lexaiAdmin.ajaxUrl,
            type: 'POST',
            data: {
                action: 'lexai_get_api_usage_stats',
                nonce: lexaiAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#lexai-api-usage-stats').html(response.data.html);
                } else {
                    $('#lexai-api-usage-stats').html('<p>Error al cargar estadísticas</p>');
                }
            },
            error: function() {
                $('#lexai-api-usage-stats').html('<p>Error de conexión</p>');
            }
        });
    }
});
</script>

<?php
// Helper functions for the view
function has_api_key($api_keys, $provider) {
    foreach ($api_keys as $key) {
        if ($key->provider === $provider && $key->status === 'active') {
            return true;
        }
    }
    return false;
}

function get_api_key_name($api_keys, $provider) {
    foreach ($api_keys as $key) {
        if ($key->provider === $provider && $key->status === 'active') {
            return $key->name;
        }
    }
    return '';
}
?>