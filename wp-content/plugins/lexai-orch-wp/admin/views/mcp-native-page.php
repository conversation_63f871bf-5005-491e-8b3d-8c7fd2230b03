<?php
/**
 * MCP Configuration Page - Unified Native Implementation
 *
 * Unified page for MCP management using 100% native PHP implementation.
 * All Node.js dependencies have been removed.
 *
 * @package LexAI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load native MCP classes
require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/class-lexai-mcp-manager-native.php';

// Initialize native MCP manager for testing
$native_manager = null;
$native_error = null;

try {
    $native_manager = new LexAI_MCP_Manager_Native();
} catch (Exception $e) {
    $native_error = $e->getMessage();
}

// Handle form submissions
if ($_POST && wp_verify_nonce($_POST['lexai_mcp_native_nonce'], 'lexai_mcp_native_action')) {

    // Handle configuration updates
    if (isset($_POST['save_config'])) {
        $config_updated = false;

        // Pinecone 2025-04 API configuration
        if (isset($_POST['pinecone_api_key'])) {
            update_option('lexai_pinecone_api_key', sanitize_text_field($_POST['pinecone_api_key']));
            $config_updated = true;
        }
        if (isset($_POST['pinecone_index_host'])) {
            update_option('lexai_pinecone_index_host', sanitize_text_field($_POST['pinecone_index_host']));
            $config_updated = true;
        }
        if (isset($_POST['pinecone_index_name'])) {
            update_option('lexai_pinecone_index_name', sanitize_text_field($_POST['pinecone_index_name']));
            $config_updated = true;
        }
        // Legacy environment (fallback)
        if (isset($_POST['pinecone_environment'])) {
            update_option('lexai_pinecone_environment', sanitize_text_field($_POST['pinecone_environment']));
            $config_updated = true;
        }

        // Web scraping configuration
        if (isset($_POST['allowed_domains'])) {
            $domains = array_map('trim', explode("\n", sanitize_textarea_field($_POST['allowed_domains'])));
            $domains = array_filter($domains); // Remove empty lines
            update_option('lexai_web_scraper_allowed_domains', $domains);
            $config_updated = true;
        }

        // MCP Native configuration
        if (isset($_POST['prefer_native'])) {
            update_option('lexai_mcp_prefer_native', (bool)$_POST['prefer_native']);
            $config_updated = true;
        }
        if (isset($_POST['fallback_enabled'])) {
            update_option('lexai_mcp_fallback_enabled', (bool)$_POST['fallback_enabled']);
            $config_updated = true;
        }
        if (isset($_POST['nodejs_timeout'])) {
            update_option('lexai_mcp_nodejs_timeout', intval($_POST['nodejs_timeout']));
            $config_updated = true;
        }

        if ($config_updated) {
            echo '<div class="notice notice-success"><p>Configuración guardada exitosamente. API Pinecone 2025-04 actualizada.</p></div>';

            // Reinitialize native manager with new config
            try {
                $native_manager = new LexAI_MCP_Manager_Native();
            } catch (Exception $e) {
                $native_error = $e->getMessage();
            }
        }
    }

    if (isset($_POST['test_native_tools'])) {
        require_once LEXAI_PLUGIN_DIR . 'tests/test-mcp-native.php';
        $test_results = LexAI_MCP_Native_Tests::run_all_tests();
    }

    if (isset($_POST['test_individual_tools'])) {
        $test_results = array();

        if ($native_manager) {
            $available_tools = $native_manager->get_available_tools();

            foreach ($available_tools as $tool) {
                try {
                    // Test each tool with sample parameters
                    $test_params = get_test_parameters($tool['name']);
                    $result = $native_manager->execute_tool($tool['name'], $test_params);

                    $test_results[$tool['name']] = array(
                        'success' => true,
                        'result' => $result
                    );
                } catch (Exception $e) {
                    $test_results[$tool['name']] = array(
                        'success' => false,
                        'error' => $e->getMessage()
                    );
                }
            }
        }
    }
    
    if (isset($_POST['force_mode'])) {
        $mode = sanitize_text_field($_POST['implementation_mode']);
        if ($native_manager) {
            try {
                $native_manager->force_mode($mode);
                echo '<div class="notice notice-success"><p>Modo de implementación cambiado a: ' . esc_html($mode) . '</p></div>';
            } catch (Exception $e) {
                echo '<div class="notice notice-error"><p>Error al cambiar modo: ' . esc_html($e->getMessage()) . '</p></div>';
            }
        }
    }
}

// Get test parameters for tools
function get_test_parameters($tool_name) {
    switch ($tool_name) {
        case 'pinecone_search_native':
            return array(
                'query' => 'derecho laboral',
                'namespace' => 'leyesycodigos',
                'limit' => 3
            );
        case 'web_scraper_native':
            return array(
                'url' => 'https://www.scjn.gob.mx',
                'extract_type' => 'text',
                'max_length' => 1000
            );
        default:
            return array();
    }
}

?>

<div class="wrap">
    <h1><?php _e('Configuración MCP - Implementación Nativa', 'lexai'); ?></h1>
    
    <?php if ($native_error): ?>
        <div class="notice notice-error">
            <p><strong>Error al inicializar MCP Nativo:</strong> <?php echo esc_html($native_error); ?></p>
        </div>
    <?php endif; ?>

    <!-- Estado del Sistema -->
    <div class="card">
        <h2>Estado del Sistema MCP Nativo</h2>
        
        <?php if ($native_manager): ?>
            <?php $status = $native_manager->get_status(); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">Modo de Implementación</th>
                    <td>
                        <span class="dashicons dashicons-yes-alt" style="color: green;"></span>
                        <strong><?php echo esc_html($status['implementation_mode']); ?></strong>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Node.js Disponible</th>
                    <td>
                        <span class="dashicons <?php echo $status['nodejs_available'] ? 'dashicons-yes-alt' : 'dashicons-dismiss'; ?>" 
                              style="color: <?php echo $status['nodejs_available'] ? 'green' : 'red'; ?>;"></span>
                        <?php echo $status['nodejs_available'] ? 'Sí' : 'No'; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Implementación Nativa</th>
                    <td>
                        <span class="dashicons <?php echo $status['native_ready'] ? 'dashicons-yes-alt' : 'dashicons-dismiss'; ?>" 
                              style="color: <?php echo $status['native_ready'] ? 'green' : 'red'; ?>;"></span>
                        <?php echo $status['native_ready'] ? 'Lista' : 'No disponible'; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Implementación Legacy</th>
                    <td>
                        <span class="dashicons <?php echo $status['legacy_ready'] ? 'dashicons-yes-alt' : 'dashicons-dismiss'; ?>" 
                              style="color: <?php echo $status['legacy_ready'] ? 'green' : 'red'; ?>;"></span>
                        <?php echo $status['legacy_ready'] ? 'Lista' : 'No disponible'; ?>
                    </td>
                </tr>
                <tr>
                    <th scope="row">Herramientas Disponibles</th>
                    <td><?php echo esc_html($status['tools_count']); ?></td>
                </tr>
            </table>
            
        <?php else: ?>
            <p><strong>MCP Nativo no está disponible.</strong></p>
        <?php endif; ?>
    </div>

    <!-- Herramientas Disponibles -->
    <?php if ($native_manager): ?>
        <div class="card">
            <h2>Herramientas MCP Nativas</h2>
            
            <?php $tools = $native_manager->get_available_tools(); ?>
            
            <?php if (!empty($tools)): ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>Nombre</th>
                            <th>Descripción</th>
                            <th>Categoría</th>
                            <th>Implementación</th>
                            <th>Estado</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($tools as $tool): ?>
                            <tr>
                                <td><code><?php echo esc_html($tool['name']); ?></code></td>
                                <td><?php echo esc_html($tool['description']); ?></td>
                                <td><?php echo esc_html($tool['category']); ?></td>
                                <td>
                                    <span class="badge <?php echo $tool['implementation'] === 'native' ? 'badge-success' : 'badge-warning'; ?>">
                                        <?php echo esc_html($tool['implementation']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($tool['metadata']['available']): ?>
                                        <span class="dashicons dashicons-yes-alt" style="color: green;"></span> Disponible
                                    <?php else: ?>
                                        <span class="dashicons dashicons-dismiss" style="color: red;"></span> No disponible
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <p>No hay herramientas disponibles.</p>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Configuración MCP Nativo -->
    <div class="card">
        <h2>Configuración MCP Nativo (API 2025-04)</h2>

        <form method="post" action="">
            <?php wp_nonce_field('lexai_mcp_native_action', 'lexai_mcp_native_nonce'); ?>

            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="pinecone_api_key">API Key de Pinecone</label>
                    </th>
                    <td>
                        <input type="password" id="pinecone_api_key" name="pinecone_api_key"
                               value="<?php echo esc_attr(get_option('lexai_pinecone_api_key', '')); ?>"
                               class="regular-text" />
                        <p class="description">Tu API key de Pinecone para autenticación.</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="pinecone_index_host">Host del Índice (2025-04)</label>
                    </th>
                    <td>
                        <input type="text" id="pinecone_index_host" name="pinecone_index_host"
                               value="<?php echo esc_attr(get_option('lexai_pinecone_index_host', '')); ?>"
                               class="regular-text"
                               placeholder="accord.svc.us-east-1-aws.pinecone.io" />
                        <p class="description">
                            <strong>Nuevo formato 2025-04:</strong> Host directo del índice.
                            Formato: <code>{index-name}.svc.{region}.pinecone.io</code>
                        </p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="pinecone_index_name">Nombre del Índice</label>
                    </th>
                    <td>
                        <input type="text" id="pinecone_index_name" name="pinecone_index_name"
                               value="<?php echo esc_attr(get_option('lexai_pinecone_index_name', 'accord')); ?>"
                               class="regular-text" />
                        <p class="description">Nombre de tu índice de Pinecone.</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="pinecone_environment">Environment (Legacy)</label>
                    </th>
                    <td>
                        <input type="text" id="pinecone_environment" name="pinecone_environment"
                               value="<?php echo esc_attr(get_option('lexai_pinecone_environment', 'us-east-1-aws')); ?>"
                               class="regular-text" />
                        <p class="description">
                            <strong>Solo para fallback:</strong> Environment legacy si no se especifica host directo.
                        </p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="allowed_domains">Dominios Permitidos (Web Scraping)</label>
                    </th>
                    <td>
                        <textarea id="allowed_domains" name="allowed_domains" rows="5" class="large-text"><?php
                            $domains = get_option('lexai_web_scraper_allowed_domains', array(
                                'scjn.gob.mx',
                                'dof.gob.mx',
                                'ordenjuridico.gob.mx',
                                'tribunales.gob.mx',
                                'cjf.gob.mx',
                                'juridicas.unam.mx'
                            ));
                            echo esc_textarea(implode("\n", $domains));
                        ?></textarea>
                        <p class="description">Un dominio por línea. Solo estos dominios serán permitidos para web scraping.</p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">Configuración MCP</th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="prefer_native" value="1"
                                       <?php checked(get_option('lexai_mcp_prefer_native', true)); ?> />
                                Preferir implementación nativa PHP
                            </label>
                            <br><br>
                            <label>
                                <input type="checkbox" name="fallback_enabled" value="1"
                                       <?php checked(get_option('lexai_mcp_fallback_enabled', true)); ?> />
                                Habilitar fallback a Node.js
                            </label>
                            <br><br>
                            <label>
                                Timeout Node.js:
                                <input type="number" name="nodejs_timeout"
                                       value="<?php echo esc_attr(get_option('lexai_mcp_nodejs_timeout', 5)); ?>"
                                       min="1" max="30" style="width: 60px;" /> segundos
                            </label>
                        </fieldset>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="save_config" class="button button-primary" value="Guardar Configuración" />
            </p>
        </form>
    </div>

    <!-- Controles de Administración -->
    <?php if ($native_manager): ?>
        <div class="card">
            <h2>Controles de Administración</h2>
            
            <form method="post" action="">
                <?php wp_nonce_field('lexai_mcp_native_action', 'lexai_mcp_native_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Forzar Modo de Implementación</th>
                        <td>
                            <select name="implementation_mode">
                                <option value="native">Nativo (PHP)</option>
                                <option value="legacy">Legacy (Node.js)</option>
                                <option value="hybrid">Híbrido</option>
                            </select>
                            <input type="submit" name="force_mode" class="button" value="Cambiar Modo">
                            <p class="description">Fuerza el uso de una implementación específica.</p>
                        </td>
                    </tr>
                </table>
            </form>
            
            <form method="post" action="">
                <?php wp_nonce_field('lexai_mcp_native_action', 'lexai_mcp_native_nonce'); ?>
                <p>
                    <input type="submit" name="test_native_tools" class="button button-primary" value="Ejecutar Tests Completos (API 2025-04)">
                    <input type="submit" name="test_individual_tools" class="button" value="Probar Herramientas Individuales">
                </p>
            </form>
        </div>
    <?php endif; ?>

    <!-- Resultados de Pruebas -->
    <?php if (isset($test_results)): ?>
        <div class="card">
            <h2>Resultados de Pruebas MCP Nativo (API 2025-04)</h2>

            <?php foreach ($test_results as $category => $result): ?>
                <div class="test-result">
                    <h3><?php echo esc_html(strtoupper($category)); ?></h3>

                    <?php if ($result['success']): ?>
                        <div class="notice notice-success inline">
                            <p><strong>✓ Categoría exitosa</strong></p>

                            <?php if (isset($result['tests'])): ?>
                                <div class="test-details">
                                    <?php foreach ($result['tests'] as $test): ?>
                                        <div class="individual-test">
                                            <span class="test-status <?php echo $test['success'] ? 'success' : 'error'; ?>">
                                                <?php echo $test['success'] ? '✓' : '✗'; ?>
                                            </span>
                                            <strong><?php echo esc_html($test['name']); ?>:</strong>
                                            <?php echo esc_html($test['message']); ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (isset($result['result'])): ?>
                                <details>
                                    <summary>Ver resultado completo</summary>
                                    <pre><?php echo esc_html(json_encode($result['result'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
                                </details>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="notice notice-error inline">
                            <p><strong>✗ Categoría fallida:</strong> <?php echo esc_html($result['error'] ?? 'Error desconocido'); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>

            <?php if (class_exists('LexAI_MCP_Native_Tests')): ?>
                <div class="test-report">
                    <h4>Reporte de Tests</h4>
                    <pre><?php echo esc_html(LexAI_MCP_Native_Tests::generate_report($test_results)); ?></pre>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Información Técnica -->
    <div class="card">
        <h2>Información Técnica</h2>
        
        <h3>Ventajas de la Implementación Nativa:</h3>
        <ul>
            <li><strong>Sin dependencias de Node.js:</strong> Funciona en cualquier servidor con PHP</li>
            <li><strong>Mejor rendimiento:</strong> Sin overhead de procesos externos</li>
            <li><strong>Mayor estabilidad:</strong> Sin riesgo de fallos de procesos Node.js</li>
            <li><strong>Fácil mantenimiento:</strong> Todo el código en PHP</li>
            <li><strong>Mejor integración:</strong> Acceso directo a WordPress APIs</li>
        </ul>
        
        <h3>Herramientas Implementadas (API 2025-04):</h3>
        <ul>
            <li><strong>Búsqueda Pinecone:</strong> API REST 2025-04 con soporte para vectores densos y sparse</li>
            <li><strong>Búsqueda Híbrida:</strong> Combinación de vectores densos y sparse para mejor relevancia</li>
            <li><strong>Gestión de Namespaces:</strong> Soporte completo para namespaces de Pinecone</li>
            <li><strong>Web Scraping:</strong> cURL + DOMDocument para sitios legales mexicanos</li>
            <li><strong>Fallback automático:</strong> Usa legacy si nativo falla</li>
        </ul>

        <h3>Nuevas Características API 2025-04:</h3>
        <ul>
            <li><strong>Host directo:</strong> Conexión directa sin llamadas adicionales a describe_index</li>
            <li><strong>Vectores sparse:</strong> Soporte para búsqueda lexical y híbrida</li>
            <li><strong>Mejor rendimiento:</strong> Menos latencia en conexiones</li>
            <li><strong>Gestión de backups:</strong> API para backup y restauración de índices</li>
            <li><strong>Índices sparse-only:</strong> Soporte para índices solo con vectores sparse</li>
        </ul>

        <h3>SDK Utilizado:</h3>
        <p>
            <strong>logiscape/mcp-sdk-php v1.2.1</strong><br>
            Implementación completa del protocolo MCP en PHP nativo.<br>
            <strong>API Pinecone 2025-04</strong> - Última versión estable
        </p>
    </div>
</div>

<style>
.card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin: 20px 0;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.card h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.badge-success {
    background: #46b450;
    color: white;
}

.badge-warning {
    background: #ffb900;
    color: white;
}

.test-result {
    margin: 15px 0;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.notice.inline {
    margin: 10px 0;
}

details {
    margin-top: 10px;
}

pre {
    background: #f1f1f1;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    max-height: 300px;
}

.test-details {
    margin: 10px 0;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.individual-test {
    margin: 5px 0;
    padding: 5px;
    border-left: 3px solid #ddd;
    padding-left: 10px;
}

.test-status.success {
    color: #46b450;
    font-weight: bold;
}

.test-status.error {
    color: #dc3232;
    font-weight: bold;
}

.test-report {
    margin-top: 20px;
    padding: 15px;
    background: #f0f0f1;
    border-radius: 4px;
}

.test-report h4 {
    margin-top: 0;
    color: #333;
}
</style>
