<?php
/**
 * Debug CSS Loading - Verificar por qué no se cargan los estilos
 * 
 * @package LexAI
 * @since 2.0.1
 */

// Headers para evitar caché
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug CSS Loading - LexAI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .btn { display: inline-block; padding: 12px 24px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; font-weight: 600; }
        .btn:hover { background: #005a87; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; border: 1px solid #e9ecef; margin: 10px 0; white-space: pre-wrap; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: 600; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug CSS Loading - LexAI</h1>
        <p><strong>Ejecutado:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php
        // Definir rutas
        $plugin_dir = '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/';
        $css_file = $plugin_dir . 'assets/css/lexai-auth.css';
        $base_url = 'https://tuasesorlegalvirtual.online';
        
        // 1. Verificar archivo CSS
        echo "<h2>📄 1. Verificación del Archivo CSS</h2>\n";
        
        if (file_exists($css_file)) {
            $css_size = filesize($css_file);
            $css_modified = date('Y-m-d H:i:s', filemtime($css_file));
            $css_content = file_get_contents($css_file);
            
            echo "<div class='success'>\n";
            echo "<h3>✅ Archivo CSS Encontrado</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>Ruta:</strong> {$css_file}</li>\n";
            echo "<li><strong>Tamaño:</strong> " . number_format($css_size) . " bytes</li>\n";
            echo "<li><strong>Modificado:</strong> {$css_modified}</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
            
            // Verificar contenido específico
            $checks = array(
                '--lexai-bg-main' => 'Variable de fondo principal',
                'lexai-floating-element' => 'Elementos flotantes',
                'lexai-float' => 'Animación de flotación',
                'backdrop-filter' => 'Efecto glassmorphism',
                'lexai-auth-container' => 'Contenedor principal'
            );
            
            echo "<h3>🔍 Contenido del CSS:</h3>\n";
            echo "<ul>\n";
            foreach ($checks as $search => $description) {
                if (strpos($css_content, $search) !== false) {
                    echo "<li class='status-ok'>✅ {$description}: Encontrado</li>\n";
                } else {
                    echo "<li class='status-error'>❌ {$description}: NO encontrado</li>\n";
                }
            }
            echo "</ul>\n";
            
        } else {
            echo "<div class='error'>\n";
            echo "<h3>❌ Archivo CSS NO Encontrado</h3>\n";
            echo "<p>Ruta: {$css_file}</p>\n";
            echo "</div>\n";
        }
        
        // 2. Verificar URLs de carga
        echo "<h2>🌐 2. URLs de Carga</h2>\n";
        
        $css_url = $base_url . '/wp-content/plugins/lexai-orch-wp/assets/css/lexai-auth.css';
        $css_url_versioned = $css_url . '?v=' . time();
        
        echo "<div class='info'>\n";
        echo "<h3>📋 URLs de Prueba</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>CSS Normal:</strong> <a href='{$css_url}' target='_blank'>{$css_url}</a></li>\n";
        echo "<li><strong>CSS con Versión:</strong> <a href='{$css_url_versioned}' target='_blank'>{$css_url_versioned}</a></li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // 3. Verificar templates
        echo "<h2>📄 3. Verificación de Templates</h2>\n";
        
        $templates = array(
            'login' => $plugin_dir . 'templates/auth/login-form.php',
            'register' => $plugin_dir . 'templates/auth/register-form.php',
            'forgot' => $plugin_dir . 'templates/auth/forgot-password-form.php'
        );
        
        echo "<table>\n";
        echo "<thead>\n";
        echo "<tr><th>Template</th><th>Estado</th><th>Tamaño</th><th>Modificado</th><th>CSS Inline</th></tr>\n";
        echo "</thead>\n";
        echo "<tbody>\n";
        
        foreach ($templates as $name => $path) {
            if (file_exists($path)) {
                $size = filesize($path);
                $modified = date('Y-m-d H:i:s', filemtime($path));
                $content = file_get_contents($path);
                $has_inline = strpos($content, 'FORCE INLINE CSS') !== false;
                
                echo "<tr>\n";
                echo "<td><strong>{$name}</strong></td>\n";
                echo "<td class='status-ok'>✅ Existe</td>\n";
                echo "<td>" . number_format($size) . " bytes</td>\n";
                echo "<td>{$modified}</td>\n";
                echo "<td>" . ($has_inline ? "✅ Sí" : "❌ No") . "</td>\n";
                echo "</tr>\n";
            } else {
                echo "<tr>\n";
                echo "<td><strong>{$name}</strong></td>\n";
                echo "<td class='status-error'>❌ No existe</td>\n";
                echo "<td>-</td>\n";
                echo "<td>-</td>\n";
                echo "<td>-</td>\n";
                echo "</tr>\n";
            }
        }
        
        echo "</tbody>\n";
        echo "</table>\n";
        
        // 4. Test de carga directa
        echo "<h2>🧪 4. Test de Carga Directa</h2>\n";
        
        echo "<div class='warning'>\n";
        echo "<h3>📋 Instrucciones de Verificación</h3>\n";
        echo "<ol>\n";
        echo "<li><strong>Abrir DevTools:</strong> F12 → Network</li>\n";
        echo "<li><strong>Ir a página de login:</strong> <a href='{$base_url}/lexai-login/' target='_blank'>{$base_url}/lexai-login/</a></li>\n";
        echo "<li><strong>Verificar en Network:</strong> ¿Se carga lexai-auth.css?</li>\n";
        echo "<li><strong>Verificar en Console:</strong> ¿Hay errores CSS?</li>\n";
        echo "<li><strong>Verificar en Elements:</strong> ¿Están los elementos flotantes?</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        // 5. CSS de emergencia inline
        echo "<h2>🚨 5. CSS de Emergencia (Inline)</h2>\n";
        
        echo "<div class='info'>\n";
        echo "<h3>💡 Si los estilos no se cargan, usa este CSS inline:</h3>\n";
        echo "<div class='code'>";
        echo htmlspecialchars('
<style>
:root {
    --lexai-bg-main: #f8fafc !important;
    --lexai-accent: #64748b !important;
}
.lexai-auth-container {
    background: var(--lexai-bg-main) !important;
    position: relative !important;
    overflow: hidden !important;
}
.lexai-floating-element {
    position: absolute !important;
    border-radius: 50% !important;
    background: radial-gradient(circle, #64748b 0%, transparent 70%) !important;
    filter: blur(40px) !important;
    opacity: 0.15 !important;
    animation: float 20s ease-in-out infinite !important;
}
.lexai-element-1 { width: 300px !important; height: 300px !important; top: 10% !important; left: 10% !important; }
.lexai-element-2 { width: 200px !important; height: 200px !important; top: 60% !important; right: 15% !important; }
.lexai-element-3 { width: 250px !important; height: 250px !important; bottom: 20% !important; left: 20% !important; }
@keyframes float {
    0%, 100% { transform: translate(0, 0) scale(1); }
    50% { transform: translate(20px, -20px) scale(1.1); }
}
.lexai-auth-card {
    background: rgba(248, 250, 252, 0.6) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
}
</style>
        ');
        echo "</div>\n";
        echo "</div>\n";
        
        // 6. Comandos de limpieza
        echo "<h2>🧹 6. Comandos de Limpieza</h2>\n";
        
        echo "<div class='warning'>\n";
        echo "<h3>🔄 Pasos para Forzar Actualización</h3>\n";
        echo "<ol>\n";
        echo "<li><strong>Navegador:</strong> Ctrl+Shift+R (hard refresh)</li>\n";
        echo "<li><strong>WordPress:</strong> Limpiar caché de plugins</li>\n";
        echo "<li><strong>CDN/Cloudflare:</strong> Purgar caché si aplica</li>\n";
        echo "<li><strong>Servidor:</strong> Reiniciar si es necesario</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        // 7. Verificación final
        echo "<h2>✅ 7. Verificación Final</h2>\n";
        
        $all_good = file_exists($css_file) && filesize($css_file) > 10000;
        
        if ($all_good) {
            echo "<div class='success'>\n";
            echo "<h3>🎉 Archivos CSS Listos</h3>\n";
            echo "<p>Los archivos CSS están en su lugar y tienen el contenido correcto.</p>\n";
            echo "<p><strong>Si aún no ves los cambios:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>1. Haz hard refresh (Ctrl+Shift+R)</li>\n";
            echo "<li>2. Abre modo incógnito</li>\n";
            echo "<li>3. Verifica que no haya plugins de caché activos</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        } else {
            echo "<div class='error'>\n";
            echo "<h3>⚠️ Problemas Detectados</h3>\n";
            echo "<p>Hay problemas con los archivos CSS. Revisa los puntos anteriores.</p>\n";
            echo "</div>\n";
        }
        ?>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="<?php echo $base_url; ?>/lexai-login/" class="btn" target="_blank">
                🔐 Probar Login
            </a>
            <a href="<?php echo $css_url_versioned; ?>" class="btn" target="_blank">
                📄 Ver CSS Directo
            </a>
            <a href="javascript:location.reload();" class="btn">
                🔄 Recargar Debug
            </a>
        </div>
        
        <script>
            console.log('🔍 Debug CSS Loading ejecutado');
            console.log('CSS URL:', '<?php echo $css_url; ?>');
            console.log('Timestamp:', <?php echo time(); ?>);
        </script>
    </div>
</body>
</html>
