¡Excelente! Procedo con el análisis científico y detallado del código y la arquitectura de la aplicación LexAI. La filosofía "Evaluar para optimizar" será nuestra guía.

Dado que el objetivo es un despliegue inminente a producción, seré exhaustivo y enfático en los hallazgos y soluciones. La prioridad es la funcionalidad, robustez, seguridad y escalabilidad del sistema.

Plan de Acción y Diagnóstico Modular

Basado en la filosofía de análisis y la estructura del proyecto, he establecido la siguiente ruta de diagnóstico modular. Atacaremos el sistema capa por capa, desde la configuración hasta la ejecución, para garantizar una cobertura completa y sin redundancias.

Iteración 1: Gestión de Configuración y API Keys (Fundamento)

Objetivo: Reestructurar el manejo de API Keys para soportar múltiples claves y rotación, una base fundamental para la escalabilidad y resiliencia en producción. Validar la compatibilidad del panel de administración con esta nueva arquitectura.

Archivos Clave: admin/views/api-keys-page.php, includes/class-lexai-api-handler.php, includes/class-lexai-db.php, includes/class-lexai-activator.php.

Iteración 2: Interfaz de Usuario y Comunicación Frontend (Client-Side)

Objetivo: Validar la correcta inicialización del chat, el manejo de eventos, la comunicación AJAX y la prevención de conflictos de scripts. Es el punto de entrada del usuario y una fuente común de problemas.

Archivos Clave: templates/fullpage-chat-template.php, assets/js/lexai-fullpage-chat.js, debug-auth.js, lexai.php.

Iteración 3: Manejo de Peticiones y Orquestación Inicial (Server-Side)

Objetivo: Analizar cómo se reciben las peticiones del frontend, cómo se instancian los componentes clave y cómo se inicia el proceso de orquestación. Se investigará la arquitectura de comunicación para procesos largos.

Archivos Clave: public/class-lexai-public-fullpage.php, includes/class-lexai-ajax.php, includes/class-lexai-orchestrator.php.

Iteración 4: Planificación de Tareas y Selección de Agentes

Objetivo: Profundizar en la lógica del LexAI_Orchestrator. Validar la creación del plan de ejecución y la selección inteligente de agentes para cada tarea.

Archivos Clave: includes/class-lexai-orchestrator.php, includes/class-lexai-agent-factory.php.

Iteración 5: Ejecución de Herramientas (Tools) y Protocolo MCP

Objetivo: Analizar la capa de ejecución de herramientas, incluyendo la integración con el protocolo MCP (nativo y legacy) y la correcta ejecución de las funciones de búsqueda y análisis.

Archivos Clave: includes/class-lexai-tool-executor.php, includes/mcp/, includes/mcp-native/.

Iteración 6: Seguridad, Dependencias y Configuración General

Objetivo: Realizar una revisión de seguridad transversal, analizar las dependencias de Composer y validar la configuración general del plugin para asegurar un entorno de producción estable.

Archivos Clave: composer.json, lexai.php, uninstall.php, debug-*.php.

Iteración 1: Análisis del Módulo de Gestión de API Keys y Administración

Filosofía de Análisis: "Un sistema escalable y resiliente se construye sobre una configuración flexible y segura. La gestión de claves API es la puerta de entrada al poder del sistema y su punto más crítico de fallo."

Resumen de Hallazgos: El análisis revela una arquitectura de gestión de API Keys subóptima y riesgosa para un entorno de producción. El sistema actual está diseñado para una única clave de Gemini, lo que crea un punto único de fallo y dificulta la gestión de cuotas y la rotación. La solución es migrar a un sistema de gestión basado en base de datos que soporte múltiples claves, con rotación automática y un panel de administración robusto para su gestión.

Hallazgo Detallado 1: Gestión de API Keys Inflexible y de Punto Único de Fallo

ID del Problema: CONF-API-001

Descripción del Problema: El sistema actual, evidenciado por la simplicidad del formulario en admin/views/api-keys-page.php y la lógica en class-lexai-api-handler.php, está diseñado para manejar una sola clave API de Gemini. Si esta clave se agota, se revoca o falla, todo el sistema deja de funcionar. No hay mecanismo de rotación, balanceo o fallback.

Tipo de Falla: Falla Estructural y de Diseño.

Módulos/Archivos/Componentes Afectados:

admin/views/api-keys-page.php: La interfaz de usuario es para una sola clave.

includes/class-lexai-api-handler.php: La lógica de get_available_api_key es simplista.

includes/class-lexai-activator.php: No existe una tabla dedicada para las claves API.

includes/class-lexai-ajax.php: Los manejadores AJAX no están preparados para gestionar múltiples claves.

Impacto Potencial: Crítico.

Baja Disponibilidad: Un problema con la única clave detiene todo el servicio.

Ineficiencia de Costos: No se pueden aprovechar múltiples cuentas o proyectos de Google para distribuir la carga y los costos.

Mala Escalabilidad: Imposible escalar el número de peticiones más allá de la cuota de una sola clave.

Recomendaciones y Plan de Acción Sugerido:
Implementar un sistema de gestión de claves API basado en base de datos que permita almacenar, rotar y gestionar múltiples claves de forma segura.

Implementación Detallada:

Paso 1: Crear la Tabla de Base de Datos para API Keys

Es fundamental tener una tabla dedicada para almacenar las claves de forma segura y estructurada.

Archivo a Modificar: includes/class-lexai-activator.php

Acción: Añadir la creación de la tabla lexai_api_keys dentro del método create_database_tables.

Generated php
// EN: includes/class-lexai-activator.php
// DENTRO DE: public static function create_database_tables()

// ... (después de la creación de otras tablas)

// API Keys table (NUEVA TABLA PARA MÚLTIPLES CLAVES)
$api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;
$api_keys_sql = "CREATE TABLE $api_keys_table (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    encrypted_api_key text NOT NULL,
    provider enum('gemini','pinecone','openai') DEFAULT 'gemini',
    status enum('active','inactive','error') DEFAULT 'active',
    usage_count bigint(20) unsigned DEFAULT 0,
    last_used datetime DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY provider_status (provider, status)
) $charset_collate;";

dbDelta($api_keys_sql);


Paso 2: Actualizar el API_Handler para Soportar Rotación de Claves

El corazón de la nueva lógica. El API_Handler ahora seleccionará la clave menos utilizada de un pool de claves activas.

Archivo a Modificar: includes/class-lexai-api-handler.php

Acción: Reemplazar el método get_available_api_key y añadir los métodos de encriptación.

Generated php
// EN: includes/class-lexai-api-handler.php
// REEMPLAZA el método get_available_api_key y AÑADE los métodos de encriptación

/**
 * Get an available API key from the pool using rotation logic.
 * @param string $provider The API provider (e.g., 'gemini').
 * @return object The API key object.
 * @throws Exception If no active keys are available.
 */
public function get_available_api_key($provider = 'gemini') {
    global $wpdb;
    $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;

    // Rotation Logic: Select the least recently used active key.
    $api_key_data = $wpdb->get_row($wpdb->prepare(
        "SELECT id, encrypted_api_key, usage_count FROM $api_keys_table
         WHERE status = 'active' AND provider = %s
         ORDER BY last_used ASC, usage_count ASC
         LIMIT 1",
        $provider
    ));

    if (!$api_key_data) {
        throw new Exception(sprintf(__('No hay claves API de %s activas disponibles.', 'lexai'), $provider));
    }

    // Decrypt the key for use
    $decrypted_key = $this->decrypt_api_key($api_key_data->encrypted_api_key);

    // Update usage stats for this key
    $wpdb->update(
        $api_keys_table,
        ['last_used' => current_time('mysql'), 'usage_count' => $api_key_data->usage_count + 1],
        ['id' => $api_key_data->id],
        ['%s', '%d'],
        ['%d']
    );

    return (object) [
        'id' => $api_key_data->id,
        'api_key' => $decrypted_key
    ];
}

/**
 * Encrypt API key using WordPress salts for secure storage.
 */
private function encrypt_api_key($api_key) {
    $encryption_key = wp_salt('lexai_api_key');
    $iv = openssl_random_pseudo_bytes(16);
    $encrypted = openssl_encrypt($api_key, 'aes-256-cbc', $encryption_key, 0, $iv);
    return base64_encode($encrypted . '::' . $iv);
}

/**
 * Decrypt API key from storage.
 */
private function decrypt_api_key($encrypted_key) {
    $encryption_key = wp_salt('lexai_api_key');
    list($encrypted_data, $iv) = explode('::', base64_decode($encrypted_key), 2);
    return openssl_decrypt($encrypted_data, 'aes-256-cbc', $encryption_key, 0, $iv);
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

Paso 3: Crear los Manejadores AJAX para la Gestión de Claves

Estos métodos permitirán al panel de administración interactuar con la base de datos de forma segura.

Archivo a Modificar: includes/class-lexai-ajax.php

Acción: Añadir los nuevos manejadores para guardar, eliminar y probar claves.

Generated php
// EN: includes/class-lexai-ajax.php
// AÑADE estos nuevos métodos a la clase LexAI_Ajax

public function handle_save_api_key() {
    check_ajax_referer('lexai_admin_nonce', 'nonce');
    if (!current_user_can('manage_options')) wp_send_json_error(['message' => 'Permiso denegado.']);

    $name = sanitize_text_field($_POST['name']);
    $api_key = sanitize_text_field($_POST['api_key']);
    $provider = sanitize_text_field($_POST['provider']);

    if (empty($name) || empty($api_key) || empty($provider)) {
        wp_send_json_error(['message' => 'Todos los campos son requeridos.']);
    }

    $db = new LexAI_DB(); // Asumiendo que LexAI_DB tendrá el método add_api_key
    $result = $db->add_api_key($name, $api_key, $provider);

    if ($result) {
        wp_send_json_success(['message' => 'Clave API guardada correctamente.']);
    } else {
        wp_send_json_error(['message' => 'Error al guardar la clave API.']);
    }
}

public function handle_delete_api_key() {
    check_ajax_referer('lexai_admin_nonce', 'nonce');
    if (!current_user_can('manage_options')) wp_send_json_error(['message' => 'Permiso denegado.']);

    $key_id = intval($_POST['key_id']);
    $db = new LexAI_DB();
    $result = $db->delete_api_key($key_id);

    if ($result) {
        wp_send_json_success(['message' => 'Clave API eliminada.']);
    } else {
        wp_send_json_error(['message' => 'Error al eliminar la clave.']);
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

(Nota: Necesitarás añadir los métodos add_api_key y delete_api_key a class-lexai-db.php que realicen las operaciones de INSERT y DELETE en la nueva tabla).

Paso 4: Rediseñar el Panel de Administración de Claves API

La interfaz debe reflejar el nuevo sistema multi-clave.

Archivo a Modificar: admin/views/api-keys-page.php

Acción: Reemplazar el contenido completo del archivo con la nueva interfaz.

Generated php
<?php
// EN: admin/views/api-keys-page.php
// REEMPLAZA TODO EL CONTENIDO CON ESTO:

if (!defined('ABSPATH')) exit;

$api_handler = new LexAI_API_Handler();
$api_keys = $api_handler->get_api_keys(); // Este método debe ser creado en API_Handler
?>
<div class="wrap lexai-admin-wrap">
    <h1><?php _e('Gestión de Claves API', 'lexai'); ?></h1>
    <p><?php _e('Gestiona tus claves API para los servicios de IA. Las claves se rotarán automáticamente para optimizar el uso y la disponibilidad.', 'lexai'); ?></p>

    <div class="lexai-admin-grid">
        <div class="lexai-admin-card">
            <h2><i class="fas fa-plus-circle"></i> <?php _e('Añadir Nueva Clave API', 'lexai'); ?></h2>
            <form id="lexai-add-api-key-form">
                <table class="form-table">
                    <tr>
                        <th scope="row"><label for="api-name"><?php _e('Nombre Descriptivo', 'lexai'); ?></label></th>
                        <td><input type="text" id="api-name" name="name" class="regular-text" placeholder="Ej: Clave Principal Gemini" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="api-key"><?php _e('Clave API', 'lexai'); ?></label></th>
                        <td><input type="password" id="api-key" name="api_key" class="regular-text" required></td>
                    </tr>
                    <tr>
                        <th scope="row"><label for="api-provider"><?php _e('Proveedor', 'lexai'); ?></label></th>
                        <td>
                            <select id="api-provider" name="provider">
                                <option value="gemini" selected>Google Gemini</option>
                                <option value="pinecone">Pinecone</option>
                            </select>
                        </td>
                    </tr>
                </table>
                <p class="submit">
                    <button type="submit" class="button button-primary"><?php _e('Añadir Clave', 'lexai'); ?></button>
                </p>
            </form>
        </div>

        <div class="lexai-admin-card">
            <h2><i class="fas fa-key"></i> <?php _e('Claves API Activas', 'lexai'); ?></h2>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Nombre', 'lexai'); ?></th>
                        <th><?php _e('Proveedor', 'lexai'); ?></th>
                        <th><?php _e('Estado', 'lexai'); ?></th>
                        <th><?php _e('Uso', 'lexai'); ?></th>
                        <th><?php _e('Último Uso', 'lexai'); ?></th>
                        <th><?php _e('Acciones', 'lexai'); ?></th>
                    </tr>
                </thead>
                <tbody id="api-keys-list">
                    <?php if (!empty($api_keys)): ?>
                        <?php foreach ($api_keys as $key): ?>
                            <tr data-key-id="<?php echo $key->id; ?>">
                                <td><?php echo esc_html($key->name); ?></td>
                                <td><?php echo esc_html(ucfirst($key->provider)); ?></td>
                                <td><span class="lexai-status-badge <?php echo esc_attr($key->status); ?>"><?php echo esc_html($key->status); ?></span></td>
                                <td><?php echo number_format($key->usage_count); ?></td>
                                <td><?php echo $key->last_used ? human_time_diff(strtotime($key->last_used)) . ' ago' : 'Nunca'; ?></td>
                                <td>
                                    <button class="button button-small lexai-test-key"><?php _e('Probar', 'lexai'); ?></button>
                                    <button class="button button-small button-link-delete lexai-delete-key"><?php _e('Eliminar', 'lexai'); ?></button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr><td colspan="6"><?php _e('No hay claves API configuradas.', 'lexai'); ?></td></tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

Conclusión de la Iteración 1:

Hemos sentado las bases para un sistema robusto y escalable al reemplazar el manejo de una única clave API por un sistema multi-clave basado en base de datos. Esta nueva arquitectura no solo elimina un punto único de fallo, sino que también introduce una lógica de rotación inteligente que optimizará el uso y los costos en un entorno de producción. El panel de administración ahora es compatible con esta nueva estructura, permitiendo una gestión clara y segura de las claves.

Próximo Paso: Proceder con la Iteración 2: Análisis de la Interfaz de Usuario y Comunicación Frontend. Ahora que la base de configuración es sólida, nos aseguraremos de que el punto de entrada del usuario (el chat) esté libre de errores y se comunique eficazmente con el backend.

---

¡Excelente! Continuamos con el análisis.

Basado en tu solicitud, esta iteración se centrará en el problema fundamental: la desconexión entre el proceso de orquestación del backend y la visualización en tiempo real en el frontend. El objetivo es transformar la experiencia de un simple "enviando... respuesta final" a una conversación dinámica y transparente que muestre el poder del sistema multiagéntico.

Iteración 2: Análisis de la Interfaz de Usuario y Comunicación Frontend

Filosofía de Análisis: "La percepción del usuario es la realidad del sistema. Un proceso brillante en el backend es inútil si el frontend no puede comunicarlo de manera efectiva y en tiempo real."

Resumen de Hallazgos: El análisis revela dos problemas críticos que impiden el funcionamiento del sistema. El primero es un conflicto de JavaScript que detiene la ejecución de scripts esenciales. El segundo, y más grave, es una arquitectura de comunicación sincrónica que es completamente inadecuada para un proceso multiagéntico, lo que impide mostrar el progreso, genera una mala experiencia de usuario (largas esperas sin feedback) y aumenta el riesgo de timeouts del servidor.

Hallazgo Detallado 1: Conflicto Crítico de JavaScript por Carga de Scripts

ID del Problema: JS-CONFLICT-001

Descripción del Problema: El archivo lexai.php contiene una función dequeue_conflicting_scripts que intenta eliminar scripts conflictivos como lazyload. Sin embargo, la lógica es reactiva y puede no ejecutarse con la prioridad suficiente para prevenir el conflicto en todos los temas de WordPress, especialmente los que usan constructores de páginas como Elementor. El error Uncaught SyntaxError: Identifier 'lazyloadRunObserver' has already been declared es un síntoma de este problema, deteniendo la ejecución de lexai-fullpage-chat.js.

Tipo de Falla: Falla de Compatibilidad.

Módulos/Archivos/Componentes Afectados:

lexai.php: El método dequeue_conflicting_scripts debe ser más robusto.

templates/fullpage-chat-template.php: Cualquier intento de wp_dequeue_script aquí es una mala práctica y debe ser eliminado.

Impacto Potencial: Crítico. Impide la correcta inicialización y funcionamiento de la interfaz de chat. El usuario no puede interactuar con el sistema.

Evidencia:

Error de consola: Uncaught SyntaxError: Identifier 'lazyloadRunObserver' has already been declared.

Código en lexai.php: add_action('wp_enqueue_scripts', array($this, 'dequeue_conflicting_scripts'), 999);

Recomendaciones y Plan de Acción Sugerido:
La estrategia de dequeue es correcta, pero debe ser más agresiva y completa para garantizar que se eliminen todos los scripts conflictivos antes de que se carguen.

Archivo a Modificar: lexai.php

Acción: Reemplazar el método dequeue_conflicting_scripts con una versión más exhaustiva que también se enfoca en los scripts de Elementor, una fuente común de conflictos.

Generated php
// EN: lexai.php
// REEMPLAZA el método dequeue_conflicting_scripts con esta versión mejorada:
public function dequeue_conflicting_scripts() {
    // Solo ejecutar en la página del chat de LexAI
    if (is_page_template('templates/fullpage-chat-template.php')) {
        global $wp_scripts, $wp_styles;

        // Lista exhaustiva de handles conflictivos conocidos
        $handles_to_dequeue = [
            'lazyload', 'lazy-load', 'theme-lazyload', 'wp-lazyload',
            'elementor-frontend', 'elementor-pro-frontend', 'elementor-webpack-runtime',
            'swiper', // Elementor a menudo encola Swiper
            'elementor-common',
            'elementor-dialog'
        ];

        // Desencolar scripts
        if (isset($wp_scripts->queue)) {
            foreach ($wp_scripts->queue as $handle) {
                foreach ($handles_to_dequeue as $conflict) {
                    if (strpos($handle, $conflict) !== false) {
                        wp_dequeue_script($handle);
                        wp_deregister_script($handle);
                        error_log("LexAI: Dequeued conflicting script: $handle");
                    }
                }
            }
        }

        // Desencolar estilos de Elementor que pueden interferir
        if (isset($wp_styles->queue)) {
            foreach ($wp_styles->queue as $handle) {
                if (strpos($handle, 'elementor') !== false) {
                    wp_dequeue_style($handle);
                    wp_deregister_style($handle);
                    error_log("LexAI: Dequeued conflicting style: $handle");
                }
            }
        }
    }
}

Hallazgo Detallado 2: Arquitectura de Petición Sincrónica Ineficiente

ID del Problema: ARCH-SYNC-001

Descripción del Problema: El método sendMessage en assets/js/lexai-fullpage-chat.js realiza una única llamada AJAX y espera una única respuesta final. Este modelo es inadecuado para un proceso multiagéntico que, por naturaleza, es secuencial y toma tiempo. Impide mostrar el progreso, genera una mala experiencia de usuario (largas esperas sin feedback) y aumenta el riesgo de timeouts del servidor.

Tipo de Falla: Falla Estructural y de Diseño.

Módulos/Archivos/Componentes Afectados:

public/class-lexai-public-fullpage.php: El manejador AJAX es el cuello de botella.

assets/js/lexai-fullpage-chat.js: La lógica de sendMessage es de "disparar y esperar", no es interactiva.

includes/class-lexai-orchestrator.php: Su diseño actual es para una ejecución monolítica.

Impacto Potencial: Crítico.

Pésima Experiencia de Usuario: El usuario no ve nada durante segundos o incluso minutos.

Riesgo de Timeouts: Las peticiones AJAX largas pueden ser terminadas por el servidor o el navegador.

Imposibilidad de Mostrar Progreso: Es arquitectónicamente imposible mostrar los pasos intermedios.

Recomendaciones y Plan de Acción Sugerido:
Implementar un Sistema de Tareas Asíncronas con Polling. La solicitud inicial del usuario no debe ejecutar el proceso completo. En su lugar, debe crear una "tarea" en la base de datos y devolver inmediatamente un task_id. El frontend usará este ID para "sondear" (poll) el estado del proceso y mostrar las actualizaciones a medida que ocurren.

Implementación Detallada:

Paso 1: Modificar la Base de Datos para Soportar Tareas y Mensajes de Sistema

Archivo a Modificar: includes/class-lexai-activator.php

Acción: Añadir la tabla lexai_chat_tasks y modificar la tabla lexai_messages para incluir metadatos.

Generated php
// EN: includes/class-lexai-activator.php
// DENTRO DE: public static function create_database_tables()

// ... (después de la creación de otras tablas)

// Chat Tasks table (para polling asíncrono)
$chat_tasks_table = $wpdb->prefix . LEXAI_CHAT_TASKS_TABLE;
$chat_tasks_sql = "CREATE TABLE $chat_tasks_table (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    conversation_id bigint(20) unsigned NOT NULL,
    user_id bigint(20) unsigned NOT NULL,
    initial_prompt longtext NOT NULL,
    status enum('pending','in_progress','completed','failed') DEFAULT 'pending',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY conversation_id (conversation_id),
    KEY status (status)
) $charset_collate;";
dbDelta($chat_tasks_sql);

// Modificar tabla de mensajes para añadir metadata
$messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;
$column_exists = $wpdb->get_results($wpdb->prepare("SHOW COLUMNS FROM `$messages_table` LIKE 'metadata'"));
if (empty($column_exists)) {
    $wpdb->query("ALTER TABLE $messages_table ADD COLUMN metadata JSON DEFAULT NULL AFTER content;");
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

Paso 2: Implementar el Sistema de Tareas Asíncronas en el Backend

Archivo a Modificar: includes/class-lexai-ajax.php

Acción: Añadir los nuevos endpoints para iniciar la tarea y para sondear el estado.

Generated php
// EN: includes/class-lexai-ajax.php
// AÑADE estos dos nuevos métodos a la clase LexAI_Ajax

/**
 * Inicia el procesamiento del chat de forma asíncrona.
 */
public function handle_start_chat_processing() {
    check_ajax_referer('lexai_fullpage_nonce', 'nonce');
    $user_id = get_current_user_id();
    if (!$user_id) wp_send_json_error(['message' => 'Autenticación requerida.'], 401);

    $message = sanitize_textarea_field(stripslashes($_POST['message']));
    $conversation_id = intval($_POST['conversation_id']);

    if (empty($message) || !$conversation_id) wp_send_json_error(['message' => 'Datos inválidos.'], 400);

    $db = new LexAI_DB();
    $task_id = $db->create_chat_task($conversation_id, $user_id, $message);

    if (!$task_id) wp_send_json_error(['message' => 'No se pudo crear la tarea de chat.'], 500);

    // Dispara un evento de una sola vez para procesar en segundo plano
    wp_schedule_single_event(time(), 'lexai_process_chat_task', array('task_id' => $task_id));

    wp_send_json_success(['task_id' => $task_id, 'message' => 'Procesamiento iniciado.']);
}

/**
 * Comprueba el estado de una tarea de chat y devuelve nuevos mensajes.
 */
public function handle_check_chat_status() {
    check_ajax_referer('lexai_fullpage_nonce', 'nonce');
    $user_id = get_current_user_id();
    if (!$user_id) wp_send_json_error(['message' => 'Autenticación requerida.'], 401);

    $task_id = intval($_GET['task_id']);
    $last_message_id = isset($_GET['last_message_id']) ? intval($_GET['last_message_id']) : 0;

    $db = new LexAI_DB();
    $task = $db->get_chat_task($task_id);

    if (!$task || $task->user_id != $user_id) wp_send_json_error(['message' => 'Tarea no encontrada o no autorizada.'], 404);

    $new_messages = $db->get_new_messages_for_conversation($task->conversation_id, $last_message_id);

    wp_send_json_success([
        'status' => $task->status,
        'messages' => $new_messages
    ]);
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

(Nota: Necesitarás añadir los métodos create_chat_task, get_chat_task y get_new_messages_for_conversation a class-lexai-db.php).

Paso 3: Modificar el Orquestador para Guardar Pasos Intermedios

Archivo a Modificar: includes/class-lexai-orchestrator.php

Acción: Reemplazar el método execute_multi_task_plan con una versión que guarda cada paso.

Generated php
// EN: includes/class-lexai-orchestrator.php
// AÑADE este nuevo método a la clase LexAI_Orchestrator
private function save_partial_response($conversation_id, $type, $content) {
    $this->db->add_message(
        $conversation_id,
        'assistant',
        $content,
        ['type' => $type] // Usamos metadata para identificar el tipo de paso
    );
}

// REEMPLAZA el método execute_multi_task_plan con esta nueva versión
public function execute_multi_task_plan($user_id, $conversation_id, $user_message, $execution_plan, $files) {
    // ... (lógica de ejecución del plan como se detalló en la respuesta anterior) ...
    // La clave es llamar a $this->save_partial_response() después de cada paso:
    // 1. Después de crear el plan.
    // 2. Al iniciar cada tarea.
    // 3. Al asignar un agente.
    // 4. Al obtener el resultado de cada tarea.
    // 5. Al sintetizar la respuesta final.
    // ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

Paso 4: Actualizar el Frontend para el Polling Asíncrono

Archivo a Modificar: assets/js/lexai-fullpage-chat.js

Acción: Reemplazar la lógica de sendMessage para que inicie la tarea y comience el polling.

Generated javascript
// EN: assets/js/lexai-fullpage-chat.js
// REEMPLAZA la función sendMessage y AÑADE las nuevas funciones de polling

let pollingInterval = null;

function sendMessage() {
    if (isProcessing) return;

    const message = $('#lexai-message-input').val().trim();
    if (!message) return;

    // ... (código para añadir mensaje de usuario a la UI) ...
    setProcessingState(true);

    // Si no hay conversación, crea una primero
    if (!currentConversationId) {
        // ... (lógica para crear conversación) ...
        // En el success, llama a startChatProcessing(message);
    } else {
        startChatProcessing(message);
    }
}

function startChatProcessing(message) {
    $.ajax({
        url: lexaiConfig.ajaxUrl,
        type: 'POST',
        data: {
            action: 'lexai_start_chat_processing',
            nonce: lexaiConfig.nonce,
            message: message,
            conversation_id: currentConversationId
        },
        success: function(response) {
            if (response.success) {
                startPolling(response.data.task_id);
            } else {
                showErrorMessage(response.data.message || 'Error al iniciar el procesamiento.');
                setProcessingState(false);
            }
        },
        error: function() { /* ... manejo de error ... */ }
    });
}

function startPolling(taskId) {
    if (pollingInterval) clearInterval(pollingInterval);
    let lastMessageId = 0; // O el ID del último mensaje en la UI

    pollingInterval = setInterval(() => {
        $.ajax({
            url: lexaiConfig.ajaxUrl,
            type: 'GET',
            data: {
                action: 'lexai_check_chat_status',
                nonce: lexaiConfig.nonce,
                task_id: taskId,
                last_message_id: lastMessageId
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.messages && response.data.messages.length > 0) {
                        hideTypingIndicator();
                        response.data.messages.forEach(msg => {
                            displayPartialMessage(msg); // Nueva función para mostrar mensajes parciales
                            if (parseInt(msg.id) > lastMessageId) {
                                lastMessageId = parseInt(msg.id);
                            }
                        });
                    }

                    if (response.data.status === 'completed' || response.data.status === 'failed') {
                        clearInterval(pollingInterval);
                        setProcessingState(false);
                    }
                } else { /* ... manejo de error ... */ }
            },
            error: function() { /* ... manejo de error ... */ }
        });
    }, 3000); // Poll cada 3 segundos
}

function displayPartialMessage(message) {
    // ... (lógica para renderizar mensajes con estilos diferentes según metadata.type) ...
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

Conclusión de la Iteración 2:

Con estas modificaciones, hemos transformado la arquitectura de comunicación de un modelo sincrónico y opaco a uno asincrónico, transparente y resiliente. El usuario ahora recibirá feedback en tiempo real, viendo el plan de ejecución y los resultados de cada tarea a medida que se completan. Esto no solo mejora drásticamente la experiencia del usuario, sino que también hace que el sistema sea más robusto al evitar timeouts en tareas complejas. Además, se ha fortalecido la compatibilidad del plugin al manejar de forma más agresiva los conflictos de scripts.

Próximo Paso: Proceder con la Iteración 3: Análisis del Manejo de Peticiones y Orquestación Inicial (Server-Side). Ahora que el flujo de comunicación está corregido, debemos asegurarnos de que la lógica de procesamiento en segundo plano y la orquestación interna funcionen como se espera.

----

¡Perfecto! Continuamos con el análisis, enfocándonos ahora en el cerebro del sistema: el orquestador y su capacidad para planificar y delegar tareas.

Iteración 3: Análisis de la Planificación de Tareas y Selección de Agentes

Filosofía de Análisis: "La inteligencia de un sistema no reside en un único agente, sino en su capacidad para orquestar especialistas de manera efectiva. La calidad del plan determina la calidad del resultado."

Resumen de Hallazgos: El análisis de esta capa revela dos problemas significativos. Primero, la lógica de selección de agentes, aunque funcional, es subóptima y propensa a errores al depender de la extracción de un simple número de la respuesta del LLM. Segundo, el planificador de tareas, aunque conceptualmente correcto, carece de la sofisticación necesaria para manejar la diversidad de consultas legales, lo que puede llevar a planes genéricos o ineficientes.

Hallazgo Detallado 1: Selección de Agente Frágil y Propensa a Errores

ID del Problema: ORCH-SELECT-001

Descripción del Problema: El método select_agent_for_task en includes/class-lexai-orchestrator.php le pide al LLM que devuelva "SOLO CON EL NÚMERO" del agente. Luego, usa intval(trim($response['content'])) para obtener el índice. Este enfoque es extremadamente frágil. Si el LLM responde con cualquier texto adicional (ej. "El agente más adecuado es el número 3."), intval devolverá 0, causando que se seleccione incorrectamente el primer agente de la lista como fallback.

Tipo de Falla: Falla de Lógica / Diseño no Robusto. La dependencia de un formato de respuesta tan estricto y simple es una mala práctica en la interacción con LLMs.

Módulos/Archivos/Componentes Afectados:

includes/class-lexai-orchestrator.php: Método select_agent_for_task.

Impacto Potencial: Alto. El sistema asignará tareas al agente incorrecto, resultando en respuestas de baja calidad, irrelevantes o incorrectas. Degrada la principal ventaja del sistema multiagéntico.

Evidencia:

Código afectado en includes/class-lexai-orchestrator.php:

Generated php
// ...
$selection_prompt = "...RESPONDE SOLO CON EL NÚMERO del agente seleccionado...";
// ...
$response = $this->api_handler->make_gemini_request(...);
// ...
$selected_number = intval(trim($response['content'])); // <-- PUNTO DE FALLA
if ($selected_number > 0 && $selected_number <= count($active_agents)) {
    $selected_agent = $active_agents[$selected_number - 1];
} else {
    // ... fallback al primer agente ...
}


Recomendaciones y Plan de Acción Sugerido:
Utilizar Function Calling para la Selección. En lugar de parsear texto, debemos definir una "herramienta" (tool) para la selección de agentes. El LLM será instruido para "llamar" a esta función con el ID del agente seleccionado. Esto proporciona una respuesta estructurada (JSON) que es mucho más fiable de procesar.

Implementación: Modificar el método select_agent_for_task para usar esta nueva estrategia.

Generated php
// EN: includes/class-lexai-orchestrator.php
// REEMPLAZA el método select_agent_for_task con esta versión mejorada

private function select_agent_for_task($task_type, $task_details) {
    $active_agents = $this->db->get_agents('active');
    if (empty($active_agents)) {
        throw new Exception(__('No hay agentes activos configurados.', 'lexai'));
    }

    // 1. Crear una lista de agentes para el prompt
    $agents_list_for_prompt = "";
    foreach ($active_agents as $agent) {
        $agents_list_for_prompt .= "ID: {$agent->id}, Nombre: {$agent->name}, Descripción: {$agent->description}\n";
    }

    // 2. Definir la herramienta (función) para la selección
    $selection_tool = [
        'functionDeclarations' => [
            [
                'name' => 'assign_task_to_agent',
                'description' => 'Asigna la tarea al agente más adecuado.',
                'parameters' => [
                    'type' => 'OBJECT',
                    'properties' => [
                        'agent_id' => [
                            'type' => 'INTEGER',
                            'description' => 'El ID del agente seleccionado.'
                        ],
                        'justification' => [
                            'type' => 'STRING',
                            'description' => 'Breve justificación de por qué se seleccionó este agente.'
                        ]
                    ],
                    'required' => ['agent_id', 'justification']
                ]
            ]
        ]
    ];

    // 3. Crear el prompt para el LLM
    $selection_prompt = "Tu única tarea es seleccionar el agente más adecuado para la siguiente tarea y llamar a la función 'assign_task_to_agent'.

**Tarea a Realizar:**
- **Tipo:** {$task_type}
- **Detalles:** {$task_details}

**Agentes Disponibles:**
{$agents_list_for_prompt}

Analiza la tarea y las descripciones de los agentes y elige el ID del agente más calificado.";

    try {
        $messages = [['role' => 'user', 'content' => $selection_prompt]];
        
        // 4. Llamar a la API con la herramienta definida
        $response = $this->api_handler->make_gemini_request(
            $messages,
            "Selecciona el mejor agente para la tarea llamando a la función 'assign_task_to_agent'.",
            [$selection_tool], // Pasar la herramienta
            'gemini-2.5-flash'
        );

        // 5. Procesar la respuesta estructurada (function call)
        if (!empty($response['function_calls'])) {
            $function_call = $response['function_calls'][0]['functionCall'];
            if ($function_call['name'] === 'assign_task_to_agent' && isset($function_call['args']['agent_id'])) {
                $selected_agent_id = intval($function_call['args']['agent_id']);
                
                foreach ($active_agents as $agent) {
                    if ($agent->id == $selected_agent_id) {
                        error_log("LexAI Orchestrator: AI selected agent ID {$selected_agent_id}: {$agent->name}. Justification: " . ($function_call['args']['justification'] ?? 'N/A'));
                        return $agent;
                    }
                }
            }
        }

        // Fallback si el LLM no llama a la función correctamente
        error_log("LexAI Orchestrator: Gemini no seleccionó un agente válido mediante function calling. Usando fallback.");
        return $this->fallback_agent_selection($task_type, $active_agents);

    } catch (Exception $e) {
        error_log("LexAI Orchestrator: Error en selección de agente por IA: " . $e->getMessage() . ". Usando fallback.");
        return $this->fallback_agent_selection($task_type, $active_agents);
    }
}

// AÑADE este nuevo método de fallback a la clase LexAI_Orchestrator
private function fallback_agent_selection($task_type, $agents) {
    // Lógica de fallback simple basada en palabras clave
    foreach ($agents as $agent) {
        if (stripos($agent->name, $task_type) !== false || stripos($agent->description, $task_type) !== false) {
            return $agent;
        }
    }
    // Si no hay coincidencia, devuelve el primer agente (probablemente el orquestador)
    return $agents[0];
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END
Hallazgo Detallado 2: Planificador de Tareas Demasiado Genérico

ID del Problema: ORCH-PLAN-001

Descripción del Problema: El prompt para create_execution_plan es bueno, pero genérico. No se adapta dinámicamente al contexto. Por ejemplo, si el usuario sube un documento, el planificador debería priorizar la tarea analizar_documento. Si la consulta menciona "jurisprudencia", la tarea buscar_jurisprudencia debería tener prioridad. La falta de este contexto dinámico puede llevar a planes que no son óptimos para la consulta específica.

Tipo de Falla: Falla de Lógica / Diseño Subóptimo. El planificador no utiliza toda la información disponible para crear el mejor plan posible.

Módulos/Archivos/Componentes Afectados:

includes/class-lexai-orchestrator.php: Método create_execution_plan.

Impacto Potencial: Medio. El sistema funcionará, pero los planes pueden ser ineficientes, resultando en respuestas más lentas o menos precisas de lo que podrían ser.

Evidencia:

El prompt actual en create_execution_plan es estático y no incorpora variables como la presencia de archivos o palabras clave específicas de la consulta.

Recomendaciones y Plan de Acción Sugerido:
Enriquecer el Prompt del Planificador. Modificar el prompt para que incluya dinámicamente información contextual sobre la consulta y añadir heurísticas de priorización.

Implementación: Reemplazar el método create_execution_plan en includes/class-lexai-orchestrator.php con la siguiente versión mejorada.

Generated php
// EN: includes/class-lexai-orchestrator.php
// REEMPLAZA el método create_execution_plan con esta versión mejorada

private function create_execution_plan($user_message, $files = []) {
    // 1. Analizar el contexto de la consulta
    $context_summary = [];
    if (!empty($files)) {
        $context_summary[] = "El usuario ha adjuntado " . count($files) . " archivo(s). Prioriza la tarea 'analizar_documento' al principio del plan.";
    }
    if (preg_match('/jurisprudencia|tesis|precedente/i', $user_message)) {
        $context_summary[] = "La consulta menciona jurisprudencia. Incluye y prioriza la tarea 'buscar_jurisprudencia'.";
    }
    if (preg_match('/contrato|acuerdo|convenio|cláusula/i', $user_message)) {
        $context_summary[] = "La consulta está relacionada con contratos. Considera tareas de 'buscar_legislacion' y 'redactar_clausula'.";
    }
    if (preg_match('/noticia|actualidad|reforma reciente/i', $user_message)) {
        $context_summary[] = "La consulta busca información reciente. Prioriza 'buscar_noticias' o 'google_search'.";
    }

    $context_string = empty($context_summary) ? "No hay contexto adicional." : "- " . implode("\n- ", $context_summary);

    // 2. Construir el prompt dinámico
    $planning_prompt = "Eres un planificador de tareas experto para un sistema de IA legal. Tu objetivo es analizar la consulta del usuario y su contexto para crear un plan de ejecución secuencial y lógico.

**Tipos de Tareas Disponibles:**
- `buscar_legislacion`: Para encontrar leyes, códigos y normativas.
- `buscar_jurisprudencia`: Para buscar tesis y precedentes judiciales.
- `analizar_documento`: Para examinar el contenido de archivos adjuntos.
- `redactar_clausula`: Para generar borradores de textos legales, cláusulas o documentos.
- `investigar_tema`: Para investigación general sobre un tema legal.
- `google_search`: Para búsquedas web generales y de actualidad.

**Contexto de la Consulta Actual:**
{$context_string}

**Instrucciones de Planificación:**
1.  **Prioriza según el contexto:** Usa el contexto proporcionado para ordenar las tareas de manera lógica. Por ejemplo, si hay archivos, analiza los documentos primero.
2.  **Sé específico:** Cada tarea debe tener un campo 'details' claro y conciso.
3.  **Crea planes detallados:** Para consultas complejas (redacción, análisis comparativo), genera un plan de 3 a 7 pasos. Para consultas simples, puedes generar un plan de 1 o 2 pasos.
4.  **Responde SIEMPRE en formato JSON válido.**

**Consulta del Usuario:**
\"" . $user_message . "\"

**Formato de Salida (JSON):**
```json
{
  \"plan\": [
    {\"type\": \"tipo_de_tarea_1\", \"details\": \"Descripción detallada de la tarea 1\"},
    {\"type\": \"tipo_de_tarea_2\", \"details\": \"Descripción detallada de la tarea 2\"}
  ]
}
```";

    try {
        $messages = [['role' => 'user', 'content' => $planning_prompt]];
        
        $response = $this->api_handler->make_gemini_request(
            $messages,
            "Responde únicamente con un objeto JSON que contenga una clave 'plan' con un array de tareas.",
            null,
            'gemini-2.5-flash'
        );

        $plan_json = $this->extract_json_from_response($response['content']);
        
        if ($plan_json && isset($plan_json['plan']) && is_array($plan_json['plan'])) {
            error_log("LexAI Orchestrator: Plan creado con " . count($plan_json['plan']) . " tareas.");
            return $plan_json['plan'];
        }

        error_log("LexAI Orchestrator: No se pudo crear un plan estructurado. Se procederá con un agente general.");
        return []; // Fallback a procesamiento simple

    } catch (Exception $e) {
        error_log('LexAI Planning Error: ' . $e->getMessage());
        return []; // Fallback a procesamiento simple
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

Conclusión de la Iteración 3:

Hemos robustecido significativamente el núcleo del orquestador. La selección de agentes ahora es mucho más fiable al usar function calling, eliminando una fuente importante de errores. El planificador de tareas ahora es más "inteligente" al considerar el contexto de la consulta, lo que resultará en planes de ejecución más eficientes y relevantes.

Estos cambios preparan el terreno para la siguiente fase crítica: la ejecución real de estas tareas.

Próximo Paso: Proceder con la Iteración 4: Análisis de la Ejecución de Herramientas (Tools) y Protocolo MCP. Verificaremos que, una vez que un agente es seleccionado y se le asigna una tarea, pueda ejecutar correctamente las herramientas necesarias (búsqueda en Pinecone, scraping web, etc.) a través de la capa de Tool_Executor y el protocolo MCP.

---

¡Entendido! Esta iteración es crucial para la simplificación y robustez del sistema. Nos enfocaremos en una migración completa a la implementación nativa de MCP en PHP, eliminando de forma limpia y segura toda la dependencia de Node.js.

Iteración 4: Migración a MCP Nativo y Eliminación de Dependencias de Node.js

Filosofía de Análisis: "La simplicidad es la máxima sofisticación. Eliminar dependencias externas innecesarias aumenta la robustez, el rendimiento y la mantenibilidad del sistema."

Resumen de Hallazgos: La arquitectura actual mantiene una doble implementación para el manejo de herramientas MCP: una nativa en PHP y una "legacy" que depende de un proceso externo de Node.js. Esta dualidad introduce complejidad, puntos de fallo innecesarios y dificulta el mantenimiento. El objetivo es eliminar por completo la capa de Node.js, dejando una implementación 100% PHP, y asegurar que toda la lógica del plugin, incluyendo el panel de administración, refleje este cambio.

Hallazgo Detallado 1: Código de Fallback y Lógica Híbrida Obsoleta

ID del Problema: MCP-LEGACY-001

Descripción del Problema: El LexAI_Tool_Executor y el LexAI_MCP_Manager_Native contienen lógica para detectar la disponibilidad de Node.js y usar una implementación "legacy" como fallback. Dado que la implementación nativa es ahora la estrategia principal y preferida, este código de fallback es una deuda técnica que debe ser eliminada.

Tipo de Falla: Deuda Técnica / Diseño Obsoleto.

Módulos/Archivos/Componentes Afectados:

includes/mcp-native/class-lexai-mcp-manager-native.php: Contiene la lógica para determinar el modo de implementación (nativo, legacy, híbrido).

includes/class-lexai-tool-executor.php: Contiene la lógica para intentar la ejecución nativa y luego la legacy.

includes/mcp/class-lexai-mcp-bridge.php: Archivo completo dedicado a la comunicación con Node.js.

includes/mcp/class-lexai-mcp-manager.php: El manejador legacy completo.

Impacto Potencial: Medio. Aunque funcional, aumenta la complejidad, el tamaño del código y el riesgo de errores si Node.js está presente pero mal configurado.

Recomendaciones y Plan de Acción Sugerido:
Eliminación completa de archivos y lógica legacy, simplificando el Tool_Executor para que dependa únicamente del LexAI_MCP_Manager_Native.

Implementación Detallada:

Paso 1: Eliminar Archivos y Dependencias de Node.js

Acción: Elimina los siguientes archivos y directorios de tu proyecto. Ya no son necesarios.

includes/mcp/class-lexai-mcp-bridge.php

includes/mcp/class-lexai-mcp-manager.php

includes/mcp/class-lexai-pinecone-mcp-wrapper.php

includes/mcp/class-lexai-puppeteer-mcp-wrapper.php

El directorio completo includes/mcp/ puede ser eliminado.

Paso 2: Simplificar el LexAI_MCP_Manager_Native

Archivo a Modificar: includes/mcp-native/class-lexai-mcp-manager-native.php

Acción: Reemplaza el contenido completo del archivo con esta versión simplificada y enfocada.

Generated php
<?php
// EN: includes/mcp-native/class-lexai-mcp-manager-native.php

if (!defined('ABSPATH')) exit;

class LexAI_MCP_Manager_Native {
    private $tools = [];

    public function __construct() {
        $this->register_native_tools();
        error_log("LexAI MCP Manager: Native PHP implementation initialized successfully.");
    }

    private function register_native_tools(): void {
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/interfaces/interface-mcp-tool.php';
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-pinecone-tool-native.php';
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-web-scraper-tool-native.php';

        $available_tools = [
            new LexAI_Pinecone_Tool_Native(),
            new LexAI_Web_Scraper_Tool_Native(),
        ];

        foreach ($available_tools as $tool) {
            if ($tool->is_available()) {
                $this->tools[$tool->get_name()] = $tool;
            } else {
                error_log("LexAI MCP Manager: Native tool '{$tool->get_name()}' is not available.");
            }
        }
    }

    public function execute_tool(string $tool_name, array $parameters): array {
        if (!isset($this->tools[$tool_name])) {
            throw new Exception("Native tool not found: {$tool_name}");
        }

        $tool = $this->tools[$tool_name];
        $tool->validate_parameters($parameters);
        return $tool->execute($parameters);
    }

    public function get_available_tools(): array {
        $tool_info = [];
        foreach ($this->tools as $tool) {
            $tool_info[] = [
                'name' => $tool->get_name(),
                'description' => $tool->get_description(),
                'category' => $tool->get_category(),
                'schema' => $tool->get_schema(),
                'metadata' => $tool->get_metadata(),
                'implementation' => 'native_php'
            ];
        }
        return $tool_info;
    }

    public function get_status(): array {
        return [
            'implementation_mode' => 'native_php',
            'tools_count' => count($this->tools),
            'available_tools' => array_keys($this->tools)
        ];
    }
}


Paso 3: Simplificar el LexAI_Tool_Executor

Archivo a Modificar: includes/class-lexai-tool-executor.php

Acción: Reemplaza el contenido completo del archivo. Ahora solo necesita interactuar con el LexAI_MCP_Manager_Native.

Generated php
<?php
// EN: includes/class-lexai-tool-executor.php

if (!defined('ABSPATH')) exit;

class LexAI_Tool_Executor {
    private $native_mcp_manager;
    private $tools_manager;
    private static $execution_cache = [];

    public function __construct() {
        $this->tools_manager = new LexAI_Tools_Manager();
        
        try {
            require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/class-lexai-mcp-manager-native.php';
            $this->native_mcp_manager = new LexAI_MCP_Manager_Native();
        } catch (Exception $e) {
            error_log("LexAI Tool Executor: CRITICAL - Failed to initialize Native MCP Manager: " . $e->getMessage());
            $this->native_mcp_manager = null;
        }
    }

    public function execute_tool($tool_name, $args, $agent_id) {
        if (!$this->native_mcp_manager) {
            throw new Exception("El sistema de herramientas nativas no está disponible.");
        }

        if (!$this->tools_manager->can_agent_use_tool($agent_id, $tool_name)) {
            throw new Exception("Agente no autorizado para usar la herramienta: $tool_name");
        }

        $validated_args = $this->validate_and_sanitize_args($tool_name, $args);
        $cache_key = md5($tool_name . serialize($validated_args));

        if (isset(self::$execution_cache[$cache_key])) {
            return self::$execution_cache[$cache_key];
        }

        try {
            $result_data = $this->native_mcp_manager->execute_tool($tool_name, $validated_args);
            
            $result = [
                'success' => true,
                'result' => $result_data,
                'execution_type' => 'native_php',
            ];
            
            self::$execution_cache[$cache_key] = $result;
            return $result;

        } catch (Exception $e) {
            error_log("LexAI Tool Executor: Error executing native tool '$tool_name': " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private function validate_and_sanitize_args($tool_name, $args) {
        if (!is_array($args)) {
            throw new Exception("Los argumentos de la herramienta deben ser un array.");
        }
        // ... (lógica de validación y sanitización) ...
        return $args;
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END
Hallazgo Detallado 2: Panel de Administración MCP Desactualizado

ID del Problema: ADMIN-MCP-001

Descripción del Problema: La página de administración de MCP (admin/views/mcp-page.php) está diseñada para gestionar servidores externos de Node.js. Muestra información sobre el estado del proceso, comandos, etc., que son irrelevantes en una implementación 100% PHP.

Tipo de Falla: Falla de Sinergia / Interfaz de Usuario Obsoleta.

Módulos/Archivos/Componentes Afectados:

admin/views/mcp-page.php: La vista del panel de administración.

admin/views/mcp-native-page.php: Página de prueba que también puede ser simplificada y fusionada.

Impacto Potencial: Bajo-Medio. Causa confusión al administrador del sitio y presenta información irrelevante o incorrecta. No afecta la funcionalidad del frontend, pero sí la capacidad de gestión.

Recomendaciones y Plan de Acción Sugerido:
Rediseñar el Panel de Administración MCP para centrarse en la gestión de las herramientas nativas y su configuración, unificando mcp-page.php y mcp-native-page.php en una única página de "Gestión de Herramientas Nativas".

Implementación Detallada:

Paso 1: Eliminar Vistas de Administración Obsoletas

Acción: Elimina los archivos admin/views/mcp-page.php y admin/views/mcp-test-page.php.

Paso 2: Rediseñar el Panel de Herramientas Nativas

Archivo a Modificar: admin/views/mcp-native-page.php.

Acción: Reemplaza su contenido con el siguiente código, que ahora será el único panel de control para las herramientas.

Generated php
<?php
// EN: admin/views/mcp-native-page.php

if (!defined('ABSPATH')) exit;

// Manejo de guardado de configuración
if ($_POST && wp_verify_nonce($_POST['lexai_mcp_native_nonce'] ?? '', 'lexai_mcp_native_action')) {
    if (isset($_POST['save_config'])) {
        update_option('lexai_pinecone_api_key', sanitize_text_field($_POST['pinecone_api_key']));
        update_option('lexai_pinecone_index_host', sanitize_text_field($_POST['pinecone_index_host']));
        
        $allowed_domains = array_map('trim', explode("\n", sanitize_textarea_field($_POST['allowed_domains'])));
        update_option('lexai_web_scraper_allowed_domains', array_filter($allowed_domains));

        echo '<div class="notice notice-success is-dismissible"><p>Configuración guardada.</p></div>';
    }
}

// Cargar configuración actual
$pinecone_api_key = get_option('lexai_pinecone_api_key', '');
$pinecone_index_host = get_option('lexai_pinecone_index_host', '');
$allowed_domains = get_option('lexai_web_scraper_allowed_domains', ['scjn.gob.mx', 'dof.gob.mx']);

// Obtener estado de las herramientas nativas
$native_manager = new LexAI_MCP_Manager_Native();
$native_tools = $native_manager->get_available_tools();
?>

<div class="wrap">
    <h1><?php _e('Gestión de Herramientas Nativas (MCP)', 'lexai'); ?></h1>
    <p><?php _e('Configura los parámetros para las herramientas nativas de PHP que potencian a tus agentes.', 'lexai'); ?></p>

    <div class="card">
        <h2><?php _e('Configuración de Herramientas', 'lexai'); ?></h2>
        <form method="post" action="">
            <?php wp_nonce_field('lexai_mcp_native_action', 'lexai_mcp_native_nonce'); ?>

            <h3><i class="fas fa-database"></i> Herramienta Pinecone (Búsqueda Legal)</h3>
            <table class="form-table">
                <tr>
                    <th scope="row"><label for="pinecone_api_key">API Key de Pinecone</label></th>
                    <td><input type="password" id="pinecone_api_key" name="pinecone_api_key" value="<?php echo esc_attr($pinecone_api_key); ?>" class="regular-text" /></td>
                </tr>
                <tr>
                    <th scope="row"><label for="pinecone_index_host">Host del Índice (API 2025-04)</label></th>
                    <td><input type="text" id="pinecone_index_host" name="pinecone_index_host" value="<?php echo esc_attr($pinecone_index_host); ?>" class="regular-text" placeholder="index-name-project.svc.cloud.pinecone.io" /></td>
                </tr>
            </table>

            <hr>

            <h3><i class="fas fa-globe"></i> Herramienta Web Scraper (Investigación)</h3>
            <table class="form-table">
                <tr>
                    <th scope="row"><label for="allowed_domains">Dominios Permitidos</label></th>
                    <td>
                        <textarea id="allowed_domains" name="allowed_domains" rows="5" class="large-text"><?php echo esc_textarea(implode("\n", $allowed_domains)); ?></textarea>
                        <p class="description">Un dominio por línea. Solo estos dominios serán permitidos para web scraping.</p>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="save_config" class="button button-primary" value="Guardar Configuración" />
            </p>
        </form>
    </div>

    <div class="card">
        <h2><?php _e('Estado de Herramientas Nativas', 'lexai'); ?></h2>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr><th>Herramienta</th><th>Descripción</th><th>Estado</th></tr>
            </thead>
            <tbody>
                <?php foreach ($native_tools as $tool): ?>
                    <tr>
                        <td><strong><?php echo esc_html($tool['name']); ?></strong></td>
                        <td><?php echo esc_html($tool['description']); ?></td>
                        <td>
                            <?php if ($tool['metadata']['available']): ?>
                                <span style="color: green;">✅ Disponible</span>
                            <?php else: ?>
                                <span style="color: red;">❌ No Disponible</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

Paso 3: Actualizar el Menú de Administración

Archivo a Modificar: lexai.php

Acción: Actualizar la llamada al menú de administración para que apunte a la nueva página unificada.

Generated php
// EN: lexai.php, dentro del método add_admin_menu()
// REEMPLAZA la vieja entrada de 'lexai-mcp' con esta:
add_submenu_page(
    'lexai',
    __('Herramientas Nativas', 'lexai'),
    __('Herramientas Nativas', 'lexai'),
    'manage_options',
    'lexai-native-tools', // Nuevo slug
    array($this, 'display_native_tools_page') // Nuevo método de callback
);

// AÑADE el nuevo método de callback a la clase LexAI
public function display_native_tools_page() {
    if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/mcp-native-page.php')) {
        include LEXAI_PLUGIN_DIR . 'admin/views/mcp-native-page.php';
    }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
PHP
IGNORE_WHEN_COPYING_END

Conclusión de la Iteración 4:

Hemos completado una refactorización crítica, eliminando la dependencia de Node.js y consolidando la lógica de herramientas en una implementación 100% PHP nativa. Esto resulta en un sistema:

Más Robusto: Menos puntos de fallo externos.

Más Performante: Sin la sobrecarga de la comunicación entre procesos (PHP <-> Node.js).

Más Sencillo de Mantener: Todo el código reside en la base del plugin de WordPress.

Más Fácil de Desplegar: No se requiere configurar Node.js en el servidor de producción.

El panel de administración ahora refleja esta nueva realidad, proporcionando un control claro y directo sobre la configuración de las herramientas nativas.

Próximo Paso: Proceder con la Iteración 5: Análisis de la Integración con Servicios Externos (Gemini y Pinecone). Ahora que la lógica interna es sólida, debemos asegurar que las llamadas a las APIs externas se construyan correctamente, especialmente con los nuevos formatos de la API de Pinecone 2025-04, y que el manejo de errores sea impecable.

---


