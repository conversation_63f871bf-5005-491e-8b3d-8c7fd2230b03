/* LexAI Authentication Styles - Matching Chat Background */

/* ===== CSS VARIABLES (COPIED FROM CHAT) ===== */
:root {
    /* Primary Colors */
    --lexai-primary: #334155;
    --lexai-primary-dark: #1e293b;
    --lexai-primary-light: rgba(71, 85, 105, 0.1);
    --lexai-accent: #64748b;
    --lexai-accent-hover: #94a3b8;

    /* Background Colors - EXACT SAME AS CHAT */
    --lexai-bg-main: #f8fafc;
    --lexai-bg-sidebar: rgba(51, 65, 85, 0.85);
    --lexai-bg-sidebar-collapsed: rgba(30, 41, 59, 0.85);
    --lexai-bg-card: rgba(248, 250, 252, 0.9);
    --lexai-bg-glass: rgba(248, 250, 252, 0.6);
    --lexai-bg-input: #ffffff;

    /* Text Colors */
    --lexai-text-primary: #1e293b;
    --lexai-text-secondary: #64748b;
    --lexai-text-muted: #94a3b8;

    /* Transitions */
    --lexai-transition: 0.3s ease;
    --lexai-transition-fast: 0.15s ease;
}

/* Dark Theme Variables */
.lexai-theme-dark {
    /* Background Colors - EXACT SAME AS CHAT DARK MODE */
    --lexai-bg-main: #0a0f1c;
    --lexai-bg-sidebar: rgba(15, 23, 42, 0.95);
    --lexai-bg-sidebar-collapsed: rgba(10, 15, 28, 0.95);
    --lexai-bg-card: rgba(17, 24, 39, 0.95);
    --lexai-bg-glass: rgba(31, 41, 55, 0.8);
    --lexai-bg-input: #1f2937;

    /* Text Colors */
    --lexai-text-primary: #f1f5f9;
    --lexai-text-secondary: #cbd5e1;
    --lexai-text-muted: #94a3b8;

    /* Accent */
    --lexai-accent: #60a5fa;
    --lexai-accent-hover: #93c5fd;
}

/* Auth Container - EXACT SAME BACKGROUND AS CHAT */
.lexai-auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--lexai-bg-main); /* SAME AS CHAT */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    padding: 20px;
    position: relative;
    overflow: hidden;
    transition: background-color var(--lexai-transition), color var(--lexai-transition);
}

/* ===== ANIMATED BACKGROUND (COPIED EXACTLY FROM CHAT) ===== */
.lexai-auth-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
    pointer-events: none;
}

.lexai-floating-element {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle at 60% 40%, var(--lexai-accent) 0%, var(--lexai-primary) 60%, transparent 100%);
    filter: blur(40px);
    opacity: 0.15;
    animation: lexai-float 20s ease-in-out infinite;
}

.lexai-element-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.lexai-element-2 {
    width: 200px;
    height: 200px;
    top: 60%;
    right: 15%;
    animation-delay: -5s;
}

.lexai-element-3 {
    width: 250px;
    height: 250px;
    bottom: 20%;
    left: 20%;
    animation-delay: -10s;
}

.lexai-element-4 {
    width: 180px;
    height: 180px;
    top: 30%;
    right: 30%;
    animation-delay: -15s;
}

.lexai-element-5 {
    width: 220px;
    height: 220px;
    bottom: 40%;
    right: 10%;
    animation-delay: -20s;
}

@keyframes lexai-float {
    0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.15;
    }
    25% {
        transform: translate(30px, -30px) scale(1.1);
        opacity: 0.25;
    }
    50% {
        transform: translate(-20px, 20px) scale(0.9);
        opacity: 0.1;
    }
    75% {
        transform: translate(20px, -10px) scale(1.05);
        opacity: 0.2;
    }
}

.lexai-auth-card {
    background: var(--lexai-bg-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 400px;
    animation: slideUp 0.6s ease-out;
    position: relative;
    z-index: 1;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header */
.lexai-auth-header {
    background: var(--lexai-bg-card);
    color: var(--lexai-text-primary);
    padding: 30px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.lexai-auth-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.lexai-auth-icon {
    width: 60px;
    height: 60px;
    background: var(--lexai-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.lexai-auth-logo h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--lexai-text-primary);
}

.lexai-auth-logo p {
    margin: 0;
    font-size: 14px;
    color: var(--lexai-text-secondary);
    opacity: 0.9;
}

.lexai-auth-title {
    margin: 0 0 5px;
    font-size: 24px;
    font-weight: 600;
}

.lexai-auth-subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

/* Auth Body */
.lexai-auth-body {
    padding: 30px;
    background: var(--lexai-bg-card);
}

.lexai-auth-body h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--lexai-text-primary);
    text-align: center;
}

.lexai-auth-subtitle {
    margin: 0 0 30px 0;
    font-size: 14px;
    color: var(--lexai-text-secondary);
    text-align: center;
    opacity: 0.9;
}

/* Form */
.lexai-auth-form {
    margin: 0;
}

.lexai-form-group {
    margin-bottom: 20px;
}

.lexai-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--lexai-text-primary);
    font-size: 14px;
}

.lexai-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.lexai-input-icon {
    position: absolute;
    left: 12px;
    color: var(--lexai-text-muted);
    font-size: 16px;
    z-index: 2;
}

.lexai-input {
    width: 100%;
    padding: 12px 16px 12px 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    font-size: 16px;
    transition: all var(--lexai-transition);
    background: var(--lexai-bg-input);
    color: var(--lexai-text-primary);
    box-sizing: border-box;
}

.lexai-input:focus {
    outline: none;
    border-color: var(--lexai-accent);
    box-shadow: 0 0 0 3px rgba(var(--lexai-accent), 0.1);
}

.lexai-input::placeholder {
    color: var(--lexai-text-muted);
    opacity: 0.7;
}

.lexai-password-container {
    position: relative;
}

.lexai-password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--lexai-text-muted);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color var(--lexai-transition);
}

.lexai-password-toggle:hover {
    color: var(--lexai-text-primary);
}

.lexai-checkbox-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.lexai-checkbox {
    width: 16px;
    height: 16px;
    accent-color: var(--lexai-accent);
}

.lexai-checkbox-label {
    font-size: 14px;
    color: var(--lexai-text-secondary);
    margin: 0;
}

.lexai-submit-btn {
    width: 100%;
    background: var(--lexai-accent);
    color: white;
    border: none;
    padding: 14px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--lexai-transition);
    position: relative;
    overflow: hidden;
}

.lexai-submit-btn:hover {
    background: var(--lexai-accent-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.lexai-submit-btn:active {
    transform: translateY(0);
}

.lexai-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.lexai-loading {
    display: none;
    margin-left: 8px;
}

.lexai-loading.active {
    display: inline-block;
}

/* Messages */
.lexai-message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.lexai-message.error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.lexai-message.success {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.lexai-message.info {
    background: #eff6ff;
    color: #2563eb;
    border: 1px solid #bfdbfe;
}

/* Footer */
.lexai-auth-footer {
    padding: 20px 30px;
    background: var(--lexai-bg-card);
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.lexai-auth-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 0;
    list-style: none;
    padding: 0;
}

.lexai-auth-link {
    color: var(--lexai-text-secondary);
    text-decoration: none;
    font-size: 14px;
    transition: color var(--lexai-transition);
}

.lexai-auth-link:hover {
    color: var(--lexai-accent);
}

/* Responsive */
@media (max-width: 480px) {
    .lexai-auth-container {
        padding: 10px;
    }
    
    .lexai-auth-card {
        max-width: none;
    }
    
    .lexai-auth-header,
    .lexai-auth-form {
        padding: 20px;
    }
    
    .lexai-auth-links {
        flex-direction: column;
        gap: 10px;
    }
}

/* Loading spinner */
.lexai-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== FEATURES AND HELP SECTIONS ===== */
.lexai-features-section,
.lexai-help-section {
    margin-top: 40px;
    padding: 30px;
    background: var(--lexai-bg-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    position: relative;
    z-index: 1;
}

.lexai-features-section h3,
.lexai-help-section h3 {
    margin: 0 0 20px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--lexai-text-primary);
    text-align: center;
}

.lexai-features-grid,
.lexai-help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.lexai-feature-item,
.lexai-help-item {
    text-align: center;
    padding: 20px;
    background: var(--lexai-bg-card);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform var(--lexai-transition);
}

.lexai-feature-item:hover,
.lexai-help-item:hover {
    transform: translateY(-2px);
}

.lexai-feature-icon,
.lexai-help-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 15px;
    background: var(--lexai-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
}

.lexai-feature-item h4,
.lexai-help-item h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--lexai-text-primary);
}

.lexai-feature-item p,
.lexai-help-item p {
    margin: 0;
    font-size: 14px;
    color: var(--lexai-text-secondary);
    line-height: 1.5;
}

/* Social Login */
.lexai-social-login {
    margin: 20px 0;
}

.lexai-social-btn {
    width: 100%;
    padding: 12px 16px;
    background: var(--lexai-bg-input);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: var(--lexai-text-primary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--lexai-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.lexai-social-btn:hover {
    background: var(--lexai-bg-card);
    transform: translateY(-1px);
}

.lexai-social-icon {
    font-size: 18px;
}

/* Form Help Text */
.lexai-form-help {
    font-size: 12px;
    color: var(--lexai-text-muted);
    margin-top: 5px;
    display: block;
}

/* Password Strength */
.lexai-password-strength {
    margin-top: 8px;
}

.lexai-strength-meter {
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;
}

.lexai-strength-bar {
    height: 100%;
    background: var(--lexai-accent);
    width: 0%;
    transition: all var(--lexai-transition);
}

.lexai-strength-text {
    font-size: 12px;
    color: var(--lexai-text-muted);
}

/* Checkbox Custom */
.lexai-checkbox-custom {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    display: inline-block;
    position: relative;
    margin-right: 8px;
    transition: all var(--lexai-transition);
}

input[type="checkbox"]:checked + .lexai-checkbox-custom {
    background: var(--lexai-accent);
    border-color: var(--lexai-accent);
}

input[type="checkbox"]:checked + .lexai-checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

input[type="checkbox"] {
    display: none;
}

/* Switch Form Links */
.lexai-switch-form {
    color: var(--lexai-accent);
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: color var(--lexai-transition);
}

.lexai-switch-form:hover {
    color: var(--lexai-accent-hover);
    text-decoration: underline;
}
