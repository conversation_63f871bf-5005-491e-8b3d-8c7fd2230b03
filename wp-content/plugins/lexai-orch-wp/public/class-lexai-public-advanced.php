<?php
/**
 * Public Class - Handles frontend functionality
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Public Class
 */
class LexAI_Public_Advanced {

    /**
     * Usage limiter
     */
    private $usage_limiter;

    /**
     * Database handler
     */
    private $db;

    /**
     * Export handler
     */
    private $export_handler;

    /**
     * Constructor
     */
    public function __construct($usage_limiter = null, $db = null, $export_handler = null) {
        $this->usage_limiter = $usage_limiter ?: new LexAI_Usage_Limiter();
        $this->db = $db ?: new LexAI_DB();
        $this->export_handler = $export_handler ?: new LexAI_Export_Handler();
        $this->init_hooks();
        $this->init_optimization_hooks();
    }
    
    /**
     * Initialize public hooks
     */
    private function init_hooks() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'add_chatbot_html'));
        add_shortcode('lexai_chatbot', array($this, 'chatbot_shortcode'));
        add_shortcode('lexai_chat_interface', array($this, 'fullpage_chat_shortcode'));
        add_shortcode('lexai_login', array($this, 'login_shortcode'));
        add_shortcode('lexai_login_form', array($this, 'login_shortcode')); // Alias for compatibility
        add_shortcode('lexai_register', array($this, 'register_shortcode'));
        add_shortcode('lexai_forgot_password', array($this, 'forgot_password_shortcode'));
        add_action('init', array($this, 'init_session'));
    }

    /**
     * Initialize optimization hooks
     */
    private function init_optimization_hooks() {
        add_action('wp_head', array($this, 'preload_critical_resources'), 1);
        add_action('wp_head', array($this, 'add_dns_prefetch'), 2);
        add_filter('wp_enqueue_scripts', array($this, 'conditional_asset_loading'), 5);
    }
    
    /**
     * Initialize session management
     */
    public function init_session() {
        if (!session_id() && !headers_sent()) {
            session_start();
        }
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        if (!$this->should_load_lexai()) {
            return;
        }

        wp_enqueue_style('lexai-public-css', LEXAI_PLUGIN_URL . 'public/css/lexai-public.css', array(), LEXAI_VERSION);
        wp_enqueue_style('lexai-advanced-css', LEXAI_PLUGIN_URL . 'public/css/lexai-advanced.css', array('lexai-public-css'), LEXAI_VERSION);
        wp_enqueue_script('lexai-advanced-js', LEXAI_PLUGIN_URL . 'public/js/lexai-advanced.js', array('jquery'), LEXAI_VERSION, true);

        $current_user = wp_get_current_user();
        wp_localize_script('lexai-advanced-js', 'lexaiPublic', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => is_user_logged_in() ? wp_create_nonce('lexai_nonce') : wp_create_nonce('lexai_public_nonce'),
            'isUserLoggedIn' => is_user_logged_in(),
            'userId' => get_current_user_id(),
            'userName' => $current_user->display_name,
            'userEmail' => $current_user->user_email,
            'strings' => array(
                'typeMessage' => __('Escribe tu consulta legal...', 'lexai'),
                'send' => __('Enviar', 'lexai'),
                'newChat' => __('Nueva Conversación', 'lexai'),
                'loading' => __('Procesando...', 'lexai'),
                'error' => __('Error al procesar la consulta', 'lexai'),
                'retry' => __('Reintentar', 'lexai'),
                'delete' => __('Eliminar', 'lexai'),
                'confirmDelete' => __('¿Estás seguro de que quieres eliminar esta conversación?', 'lexai'),
                'usageLimitReached' => __('Has alcanzado tu límite de uso', 'lexai'),
                'networkError' => __('Error de conexión. Verifica tu internet.', 'lexai')
            ),
            'settings' => array(
                'autoScroll' => true,
                'showTypingIndicator' => true,
                'maxMessageLength' => 2000
            )
        ));
    }
    
    /**
     * Add chatbot HTML to footer
     */
    public function add_chatbot_html() {
        if (!$this->should_load_lexai()) return;
        include LEXAI_PLUGIN_DIR . 'templates/advanced-chatbot-template.php';
    }
    
    /**
     * Chatbot shortcode
     */
    public function chatbot_shortcode($atts) {
        $atts = shortcode_atts(array(
            'width' => '100%',
            'height' => '600px',
            'theme' => 'default',
            'position' => 'inline'
        ), $atts, 'lexai_chatbot');
        
        if (!is_user_logged_in()) {
            return '<div class="lexai-login-required">' . __('Debes iniciar sesión para usar el chatbot legal.', 'lexai') . '</div>';
        }
        
        ob_start();
        include LEXAI_PLUGIN_DIR . 'templates/advanced-chatbot-template.php';
        return ob_get_clean();
    }
    
    /**
     * Get user session data
     */
    public function get_user_session_data($user_id) {
        $session_key = 'lexai_session_' . $user_id;
        $session_data = get_transient($session_key);
        
        if (!$session_data) {
            $session_data = array(
                'current_conversation_id' => null,
                'last_activity' => time(),
                'preferences' => array(
                    'theme' => 'default',
                    'notifications' => true
                )
            );
            
            $settings = get_option('lexai_settings', array());
            $timeout = $settings['general']['session_timeout'] ?? 3600;
            
            set_transient($session_key, $session_data, $timeout);
        }
        
        return $session_data;
    }
    
    /**
     * Update user session data
     */
    public function update_user_session_data($user_id, $data) {
        $session_key = 'lexai_session_' . $user_id;
        $current_data = $this->get_user_session_data($user_id);
        
        $updated_data = array_merge($current_data, $data);
        $updated_data['last_activity'] = time();
        
        $settings = get_option('lexai_settings', array());
        $timeout = $settings['general']['session_timeout'] ?? 3600;
        
        return set_transient($session_key, $updated_data, $timeout);
    }
    
    /**
     * Clear user session
     */
    public function clear_user_session($user_id) {
        $session_key = 'lexai_session_' . $user_id;
        return delete_transient($session_key);
    }
    
    /**
     * Check if user can access chatbot
     */
    public function can_user_access_chatbot($user_id) {
        if (!$user_id) {
            return false;
        }
        
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }
        
        $allowed_roles = apply_filters('lexai_allowed_roles', array(
            'administrator',
            'editor',
            'author',
            'contributor',
            'subscriber'
        ));
        
        $user_roles = $user->roles;
        $has_access = !empty(array_intersect($user_roles, $allowed_roles));
        
        return apply_filters('lexai_user_can_access_chatbot', $has_access, $user_id);
    }
    
    /**
     * Get chatbot configuration for user
     */
    public function get_chatbot_config($user_id) {
        $usage_summary = $this->usage_limiter->get_user_usage_summary($user_id);
        $remaining_usage = $this->usage_limiter->get_remaining_usage($user_id);
        
        return array(
            'user_id' => $user_id,
            'can_access' => $this->can_user_access_chatbot($user_id),
            'usage_summary' => $usage_summary,
            'remaining_usage' => $remaining_usage,
            'session_data' => $this->get_user_session_data($user_id)
        );
    }
    
    /**
     * Format message for display
     */
    public function format_message($message) {
        $content = wp_kses_post($message->content);
        $content = make_clickable($content);
        $content = $this->format_legal_citations($content);
        $content = nl2br($content);
        return $content;
    }
    
    /**
     * Format legal citations in text
     */
    private function format_legal_citations($content) {
        $content = preg_replace('/\b(artículo|art\.?)\s+(\d+)/i', '<span class="lexai-citation">$1 $2</span>', $content);
        $content = preg_replace('/\b(ley|código)\s+([a-záéíóúñ\s]+)/i', '<span class="lexai-law-reference">$1 $2</span>', $content);
        $content = preg_replace('/\b(tesis|jurisprudencia)\s+([A-Z0-9\/\-]+)/i', '<span class="lexai-jurisprudence">$1 $2</span>', $content);
        return $content;
    }
    
    /**
     * Get conversation preview
     */
    public function get_conversation_preview($conversation) {
        $messages = $this->db->get_conversation_messages($conversation->id, 2, $conversation->user_id);
        
        $preview = '';
        if (!empty($messages)) {
            $first_message = $messages[0];
            $preview = wp_trim_words($first_message->content, 10, '...');
        }
        
        return array(
            'id' => $conversation->id,
            'title' => $conversation->title,
            'preview' => $preview,
            'updated_at' => $conversation->updated_at,
            'message_count' => count($messages)
        );
    }
    
    /**
     * Export conversation
     */
    public function export_conversation($conversation_id, $user_id, $format = 'txt') {
        $conversation = $this->db->get_conversation($conversation_id, $user_id);
        if (!$conversation) {
            throw new Exception(__('Conversación no encontrada', 'lexai'));
        }
        
        $messages = $this->db->get_conversation_messages($conversation_id, 50, $user_id);
        
        switch ($format) {
            case 'json':
                return $this->export_conversation_json($conversation, $messages);
            case 'md':
                return $this->export_conversation_md($conversation, $messages);
            case 'pdf':
                return $this->export_conversation_pdf($conversation, $messages);
            case 'docx':
                return $this->export_conversation_docx($conversation, $messages);
            default:
                return $this->export_conversation_txt($conversation, $messages);
        }
    }
    
    /**
     * Export conversation as text
     */
    private function export_conversation_txt($conversation, $messages) {
        $content = "";
        foreach ($messages as $message) {
            $role = $message->role === 'user' ? 'Consulta' : 'Respuesta';
            $timestamp = date('d/m/Y H:i', strtotime($message->created_at));
            $content .= "[{$timestamp}] {$role}:\n";
            $content .= strip_tags($message->content) . "\n\n";
            $content .= str_repeat('-', 50) . "\n\n";
        }
        return $content;
    }
    
    /**
     * Export conversation as JSON
     */
    private function export_conversation_json($conversation, $messages) {
        $data = array(
            'conversation' => $conversation,
            'messages' => $messages,
            'exported_at' => current_time('mysql'),
            'exported_by' => get_current_user_id()
        );
        return json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Export conversation as Markdown
     */
    private function export_conversation_md($conversation, $messages) {
        $content = "";
        foreach ($messages as $message) {
            $role = $message->role === 'user' ? '**Consulta**' : '**Respuesta**';
            $timestamp = date('d/m/Y H:i', strtotime($message->created_at));
            $content .= "## {$role} ({$timestamp})\n\n";
            $content .= $message->content . "\n\n";
            $content .= "---\n\n";
        }
        return $content;
    }
    
    /**
     * Export conversation as PDF
     */
    private function export_conversation_pdf($conversation, $messages) {
        return $this->export_handler->export_conversation_pdf($conversation, $messages);
    }

    /**
     * Export conversation as DOCX
     */
    private function export_conversation_docx($conversation, $messages) {
        return $this->export_handler->export_conversation_docx($conversation, $messages);
    }
    
    /**
     * Get user statistics
     */
    public function get_user_statistics($user_id) {
        global $wpdb;
        
        $conversations_table = $wpdb->prefix . LEXAI_CONVERSATIONS_TABLE;
        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;
        $usage_logs_table = $wpdb->prefix . LEXAI_USAGE_LOGS_TABLE;
        
        $stats = array();
        
        $stats['total_conversations'] = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $conversations_table WHERE user_id = %d AND status != 'deleted'", $user_id));
        $stats['total_messages'] = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $messages_table m JOIN $conversations_table c ON m.conversation_id = c.id WHERE c.user_id = %d AND m.role = 'user'", $user_id));
        $stats['monthly_usage'] = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM $usage_logs_table WHERE user_id = %d AND YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())", $user_id));
        $stats['member_since'] = $wpdb->get_var($wpdb->prepare("SELECT MIN(created_at) FROM $conversations_table WHERE user_id = %d", $user_id));
        
        return $stats;
    }

    /**
     * Login shortcode
     */
    public function login_shortcode($atts) {
        $atts = shortcode_atts(array('redirect' => home_url(), 'theme' => 'glass'), $atts, 'lexai_login');
        if (is_user_logged_in()) {
            return '<div class="lexai-auth-message">' . __('Ya has iniciado sesión.', 'lexai') . '</div>';
        }
        ob_start();
        include LEXAI_PLUGIN_DIR . 'templates/auth/login-form.php';
        return ob_get_clean();
    }

    /**
     * Register shortcode
     */
    public function register_shortcode($atts) {
        $atts = shortcode_atts(array('redirect' => home_url(), 'theme' => 'glass'), $atts, 'lexai_register');
        if (is_user_logged_in()) {
            return '<div class="lexai-auth-message">' . __('Ya has iniciado sesión.', 'lexai') . '</div>';
        }
        ob_start();
        include LEXAI_PLUGIN_DIR . 'templates/auth/register-form.php';
        return ob_get_clean();
    }

    /**
     * Full page chat interface shortcode
     */
    public function fullpage_chat_shortcode($atts) {
        if (!is_user_logged_in()) {
            return '<div class="lexai-auth-message">' . __('Debes iniciar sesión para acceder al chat.', 'lexai') . '</div>';
        }

        ob_start();
        include LEXAI_PLUGIN_DIR . 'templates/fullpage-chat-template.php';
        return ob_get_clean();
    }

    /**
     * Forgot password shortcode
     */
    public function forgot_password_shortcode($atts) {
        $atts = shortcode_atts(array('theme' => 'glass'), $atts, 'lexai_forgot_password');
        if (is_user_logged_in()) {
            return '<div class="lexai-auth-message">' . __('Ya has iniciado sesión.', 'lexai') . '</div>';
        }
        ob_start();
        include LEXAI_PLUGIN_DIR . 'templates/auth/forgot-password-form.php';
        return ob_get_clean();
    }

    /**
     * Check if LexAI should be loaded on current page (OPTIMIZATION)
     */
    private function should_load_lexai() {
        if (is_admin()) return false;
        if (in_array($GLOBALS['pagenow'], array('wp-login.php', 'wp-register.php'))) return false;

        if (!is_user_logged_in()) {
            $settings = get_option('lexai_settings', array());
            if (!($settings['allow_guest_access'] ?? false)) return false;
        }

        global $post;
        if ($post && (has_shortcode($post->post_content, 'lexai_chatbot') || has_shortcode($post->post_content, 'lexai_chat') || strpos($post->post_content, 'wp:lexai/chatbot') !== false)) {
            return true;
        }

        if (is_active_widget(false, false, 'lexai_chatbot_widget')) return true;

        $settings = get_option('lexai_settings', array());
        if (($settings['always_load_frontend'] ?? true)) return true; // Default to true for simplicity now

        $load_on_pages = $settings['load_on_pages'] ?? array();
        if (!empty($load_on_pages) && in_array(get_queried_object_id(), $load_on_pages)) {
            return true;
        }

        return false;
    }

    /**
     * Check if advanced interface is needed (OPTIMIZATION)
     */
    private function needs_advanced_interface() {
        return true; // Always use advanced interface
    }

    /**
     * Preload critical resources (OPTIMIZATION)
     */
    public function preload_critical_resources() {
        if (!$this->should_load_lexai()) return;

        echo '<link rel="preload" href="' . LEXAI_PLUGIN_URL . 'public/css/lexai-advanced.css" as="style">' . "\n";
        echo '<link rel="preload" href="' . LEXAI_PLUGIN_URL . 'public/js/lexai-advanced.js" as="script">' . "\n";
        echo '<link rel="dns-prefetch" href="//generativelanguage.googleapis.com">' . "\n";
        echo '<link rel="dns-prefetch" href="//api.pinecone.io">' . "\n";
    }

    /**
     * Add DNS prefetch separately (CRITICAL FIX)
     */
    public function add_dns_prefetch() {
        if (!$this->should_load_lexai()) return;

        echo '<link rel="dns-prefetch" href="//generativelanguage.googleapis.com">' . "\n";
        echo '<link rel="dns-prefetch" href="//api.pinecone.io">' . "\n";
        echo '<link rel="preconnect" href="//fonts.googleapis.com">' . "\n";
        echo '<link rel="preconnect" href="//fonts.gstatic.com" crossorigin>' . "\n";
    }

    /**
     * Conditional asset loading optimization (CRITICAL FIX)
     */
    public function conditional_asset_loading() {
        global $wp_query;
        if (is_admin() || !$this->should_load_lexai()) {
            remove_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        }
    }
}