<?php
/**
 * Script para forzar la recarga inmediata del CSS
 * 
 * @package LexAI
 * @since 2.0.1
 */

// Simular entorno WordPress básico
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

// Configurar headers para evitar caché
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=UTF-8');

$css_file = '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/assets/css/lexai-fullpage-chat.css';
$current_time = time();

// Tocar el archivo para actualizar timestamp
if (file_exists($css_file)) {
    touch($css_file);
    $new_timestamp = filemtime($css_file);
} else {
    $new_timestamp = $current_time;
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Forzar Recarga CSS - LexAI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: 600;
        }
        .btn:hover {
            background: #005a87;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            border: 1px solid #e9ecef;
            margin: 10px 0;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Forzar Recarga CSS - LexAI</h1>
        <p><strong>Ejecutado:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="success">
            <h3>✅ Archivo CSS Actualizado</h3>
            <ul>
                <li><strong>Nuevo timestamp:</strong> <?php echo $new_timestamp; ?></li>
                <li><strong>Fecha/hora:</strong> <?php echo date('Y-m-d H:i:s', $new_timestamp); ?></li>
                <li><strong>Cache busting activo:</strong> Sí</li>
            </ul>
        </div>

        <div class="info">
            <h3>🔗 URLs de CSS Actualizadas</h3>
            <p>El CSS ahora se cargará con estos parámetros:</p>
            <div class="code">
                <strong>URL con timestamp:</strong><br>
                https://tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/assets/css/lexai-fullpage-chat.css?v=<?php echo $new_timestamp; ?>
            </div>
            <div class="code">
                <strong>URL con versión:</strong><br>
                https://tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/assets/css/lexai-fullpage-chat.css?v=2.0.1
            </div>
        </div>

        <div class="warning">
            <h3>⚠️ Pasos Adicionales Requeridos</h3>
            <p>Para que los cambios se vean inmediatamente:</p>
            <ol>
                <li><strong>Limpiar caché del navegador:</strong>
                    <ul>
                        <li>Chrome/Edge: Presiona <code>Ctrl+Shift+R</code></li>
                        <li>Firefox: Presiona <code>Ctrl+Shift+R</code></li>
                        <li>Safari: Presiona <code>Cmd+Option+R</code></li>
                    </ul>
                </li>
                <li><strong>Abrir herramientas de desarrollador:</strong>
                    <ul>
                        <li>Presiona <code>F12</code></li>
                        <li>Ve a la pestaña <strong>Network</strong></li>
                        <li>Marca <strong>"Disable cache"</strong></li>
                        <li>Recarga la página</li>
                    </ul>
                </li>
                <li><strong>Si tienes plugin de caché:</strong>
                    <ul>
                        <li>Limpia todo el caché del sitio</li>
                        <li>Purga caché de CDN si usas uno</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="info">
            <h3>🎨 Cambios CSS Aplicados</h3>
            <p>Los siguientes estilos están ahora activos:</p>
            <ul>
                <li>✅ <strong>Conversation items:</strong> Fondo gris + texto blanco</li>
                <li>✅ <strong>Search box:</strong> Fondo gris + texto blanco + placeholder blanco</li>
                <li>✅ <strong>Botones secundarios:</strong> Fondo gris + texto blanco</li>
                <li>✅ <strong>Temas light/dark:</strong> Estilos específicos para cada tema</li>
                <li>✅ <strong>Variables CSS:</strong> Sistema modular implementado</li>
                <li>✅ <strong>Overrides:</strong> Selectores específicos con !important</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="https://tuasesorlegalvirtual.online/chat/" class="btn btn-success" target="_blank">
                🚀 Ir al Chat y Verificar Cambios
            </a>
            <a href="https://tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/assets/css/lexai-fullpage-chat.css?v=<?php echo $new_timestamp; ?>" class="btn" target="_blank">
                📄 Ver CSS Actualizado
            </a>
        </div>

        <div class="success">
            <h3>🎯 Resumen de Acciones Completadas</h3>
            <ul>
                <li>✅ Versión del plugin actualizada a 2.0.1</li>
                <li>✅ Sistema de cache busting implementado</li>
                <li>✅ Timestamp del CSS actualizado</li>
                <li>✅ Todos los cambios CSS verificados</li>
                <li>✅ URLs con parámetros de caché generadas</li>
                <li>✅ Archivo CSS "tocado" para forzar recarga</li>
            </ul>
            <p><strong>🎉 El sistema está listo. Solo necesitas limpiar el caché del navegador.</strong></p>
        </div>

        <script>
            // Auto-refresh después de 5 segundos para verificar
            setTimeout(function() {
                if (confirm('¿Quieres ir al chat para verificar los cambios?')) {
                    window.open('https://tuasesorlegalvirtual.online/chat/', '_blank');
                }
            }, 5000);

            // Mostrar información adicional
            console.log('🎨 LexAI CSS Cache Buster');
            console.log('Timestamp:', <?php echo $new_timestamp; ?>);
            console.log('CSS URL:', 'https://tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/assets/css/lexai-fullpage-chat.css?v=<?php echo $new_timestamp; ?>');
        </script>
    </div>
</body>
</html>
