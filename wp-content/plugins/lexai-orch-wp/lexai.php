<?php

// Require Composer Autoloader with error handling
$autoload_path = __DIR__ . '/vendor/autoload.php';
if (file_exists($autoload_path)) {
    try {
        require_once $autoload_path;
    } catch (Exception $e) {
        error_log('LexAI: Autoload failed: ' . $e->getMessage());
        // Fallback: manually include critical classes
        $critical_classes = [
            'includes/class-lexai-activator.php',
            'includes/class-lexai-db.php',
            'includes/class-lexai-ajax.php',
            'includes/class-lexai-api-handler.php'
        ];

        foreach ($critical_classes as $class_file) {
            $class_path = __DIR__ . '/' . $class_file;
            if (file_exists($class_path)) {
                require_once $class_path;
            }
        }
    }
} else {
    error_log('LexAI: Autoload file not found at: ' . $autoload_path);
    // Manual fallback loading
    $critical_classes = [
        'includes/class-lexai-activator.php',
        'includes/class-lexai-db.php',
        'includes/class-lexai-ajax.php',
        'includes/class-lexai-api-handler.php'
    ];

    foreach ($critical_classes as $class_file) {
        $class_path = __DIR__ . '/' . $class_file;
        if (file_exists($class_path)) {
            require_once $class_path;
        }
    }
}

/**
 * Plugin Name: LexAI - Dynamic Legal AI Agents for WordPress
 * Plugin URI: https://11-11studios.tech
 * Description: Advanced AI orchestration platform with dynamic legal agents specialized in Mexican law, powered by Gemini API and Pinecone vector database.
 * Version: 1.3.0
 * Author: Luis Agreda
 * Author URI: https://11-11studios.tech
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: lexai
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('LEXAI_VERSION', '2.0.0');
define('LEXAI_PLUGIN_FILE', __FILE__);
define('LEXAI_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('LEXAI_PLUGIN_URL', plugin_dir_url(__FILE__));
define('LEXAI_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Define database table names
define('LEXAI_AGENTS_TABLE', 'lexai_agents');
define('LEXAI_CONVERSATIONS_TABLE', 'lexai_conversations');
define('LEXAI_MESSAGES_TABLE', 'lexai_messages');
define('LEXAI_API_KEYS_TABLE', 'lexai_api_keys');
define('LEXAI_USAGE_LOGS_TABLE', 'lexai_usage_logs');
define('LEXAI_TASKS_TABLE', 'lexai_tasks');
define('LEXAI_TASK_EXECUTIONS_TABLE', 'lexai_task_executions');
define('LEXAI_CHAT_TASKS_TABLE', 'lexai_chat_tasks');
define('LEXAI_FILES_TABLE', 'lexai_files');
define('LEXAI_TOOLS_TABLE', 'lexai_tools');
define('LEXAI_AGENT_TOOLS_TABLE', 'lexai_agent_tools');

/**
 * Main LexAI Plugin Class
 */
class LexAI {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;

    /**
     * Autoloader instance
     */
    private $autoloader;

    /**
     * Plugin components
     */
    public $admin;
    public $public;
    public $public_pages;
    public $public_fullpage;
    public $ajax;
    public $api_handler;
    public $orchestrator;
    public $agent_factory;
    public $db;
    public $pinecone_handler;
    public $usage_limiter;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->load_dependencies();
        $this->check_for_updates();
        $this->set_locale();
        $this->define_admin_hooks();
        $this->define_public_hooks();
        $this->init_components();
        $this->setup_file_cleanup();

        // Add script conflict prevention for fullpage chat
        add_action('wp_enqueue_scripts', array($this, 'dequeue_conflicting_scripts'), 999);
    }
    
    /**
     * Load required dependencies using Composer autoloader
     */
    private function load_dependencies() {
        // Composer autoloader is already loaded at the top of this file
        // All classes will be loaded automatically via classmap
    }
    
    /**
     * Set plugin locale
     */
    private function set_locale() {
        add_action('plugins_loaded', array($this, 'load_plugin_textdomain'));
    }
    
    /**
     * Load plugin text domain
     */
    public function load_plugin_textdomain() {
        load_plugin_textdomain(
            'lexai',
            false,
            dirname(LEXAI_PLUGIN_BASENAME) . '/languages/'
        );
    }
    
    /**
     * Initialize plugin components
     */
    private function init_components() {
        // Initialize only core components immediately
        $this->db = new LexAI_DB();
        $this->ajax = new LexAI_Ajax();

        // Initialize public pages (for login, register, chat pages)
        // Add fallback loading in case autoloader fails
        if (!class_exists('LexAI_Public_Pages')) {
            require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-public-pages.php';
        }

        // Initialize full page chat handler
        if (!class_exists('LexAI_Public_FullPage')) {
            require_once LEXAI_PLUGIN_DIR . 'public/class-lexai-public-fullpage.php';
        }
        $this->public_pages = new LexAI_Public_Pages();

        // Always initialize full page handler for AJAX endpoints (not just on chat pages)
        add_action('init', array($this, 'init_fullpage_handler_ajax'), 1);

        // Initialize full page chat handler with dependencies (multiple hooks for safety)
        add_action('template_redirect', array($this, 'init_fullpage_handler'), 1);
        add_action('wp', array($this, 'init_fullpage_handler'), 1);

        // Add cron schedules
        add_filter('cron_schedules', array($this, 'add_cron_schedules'));

        // Schedule background processing
        add_action('lexai_process_vector_queue', array($this, 'process_vector_queue_cron'));
        add_action('lexai_process_chat_task', array($this, 'process_chat_task_background'), 10, 1);

        // Lazy load admin components
        if (is_admin()) {
            add_action('admin_init', array($this, 'init_admin_components'));
        }

        // Lazy load public components
        add_action('wp', array($this, 'init_public_components'));

        // Initialize API components only when needed
        add_action('wp_ajax_lexai_chat', array($this, 'init_api_components'), 1);
        add_action('wp_ajax_nopriv_lexai_chat', array($this, 'init_api_components'), 1);
    }

    /**
     * Initialize admin components (lazy loading)
     */
    public function init_admin_components() {
        if (!$this->api_handler) {
            $this->api_handler = new LexAI_API_Handler();
        }

        if (!$this->pinecone_handler) {
            $this->pinecone_handler = new LexAI_Pinecone_Handler();
        }

        if (!$this->usage_limiter) {
            $this->usage_limiter = new LexAI_Usage_Limiter($this->db);
        }

        $export_handler = new LexAI_Export_Handler();
        $vector_manager = new LexAI_Vector_Manager($this->pinecone_handler, $this->api_handler);

        $this->admin = new LexAI_Admin($this->api_handler, $this->pinecone_handler);
        $this->vector_admin = new LexAI_Vector_Admin($vector_manager, $this->pinecone_handler);
    }

    /**
     * Initialize public components (lazy loading)
     */
    public function init_public_components() {
        // Only initialize if we're on a page that needs the chatbot
        if (!$this->is_chatbot_page()) {
            return;
        }

        if (!$this->usage_limiter) {
            $this->usage_limiter = new LexAI_Usage_Limiter($this->db);
        }

        $export_handler = new LexAI_Export_Handler();
        $this->public = new LexAI_Public_Advanced($this->usage_limiter, $this->db, $export_handler);
    }

    /**
     * Initialize API components (lazy loading)
     */
    public function init_api_components() {
        if (!$this->api_handler) {
            $this->api_handler = new LexAI_API_Handler();
        }

        if (!$this->pinecone_handler) {
            $this->pinecone_handler = new LexAI_Pinecone_Handler();
        }

        if (!$this->usage_limiter) {
            $this->usage_limiter = new LexAI_Usage_Limiter($this->db);
        }

        if (!$this->agent_factory) {
            $this->agent_factory = new LexAI_Agent_Factory();
        }

        if (!$this->orchestrator) {
            $tools_manager = new LexAI_Tools_Manager();
            $tool_executor = new LexAI_Tool_Executor();
            $this->orchestrator = new LexAI_Orchestrator(
                $this->agent_factory,
                $this->db,
                $this->api_handler,
                $tools_manager,
                $tool_executor
            );
        }

        // Ensure TTS handler is available
        if (!class_exists('LexAI_TTS_Handler')) {
            require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-tts-handler.php';
        }

        // Ensure Export handler is available
        if (!class_exists('LexAI_Export_Handler')) {
            require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-export-handler.php';
        }

        // Ensure STT handler is available
        if (!class_exists('LexAI_STT_Handler')) {
            require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-stt-handler.php';
        }
    }

    /**
     * Initialize full page handler
     */
    public function init_fullpage_handler() {
        global $post;

        // Skip if already initialized
        if ($this->public_fullpage) {
            return;
        }

        // Check if we're on a chat page
        $is_chat_page = false;

        if ($post) {
            $page_type = get_post_meta($post->ID, '_lexai_page_type', true);
            $is_chat_page = ($page_type === 'chat' || has_shortcode($post->post_content, 'lexai_chat_interface'));
        }

        // Also check URL for chat pages
        if (!$is_chat_page) {
            $current_url = $_SERVER['REQUEST_URI'] ?? '';
            $is_chat_page = (strpos($current_url, '/lexai-chat') !== false || strpos($current_url, '/chat') !== false);
        }

        // Initialize full page handler for chat pages
        if ($is_chat_page) {
            if (!$this->usage_limiter) {
                $this->usage_limiter = new LexAI_Usage_Limiter($this->db);
            }

            $export_handler = new LexAI_Export_Handler();
            $this->public_fullpage = new LexAI_Public_FullPage($this->usage_limiter, $this->db, $export_handler);
        }
    }

    /**
     * Initialize full page handler for AJAX endpoints only
     * This ensures AJAX handlers are always available regardless of page type
     */
    public function init_fullpage_handler_ajax() {
        // Skip if already initialized
        if ($this->public_fullpage) {
            return;
        }

        // Always initialize to ensure AJAX handlers are available
        if (!$this->usage_limiter) {
            $this->usage_limiter = new LexAI_Usage_Limiter($this->db);
        }

        $export_handler = new LexAI_Export_Handler();
        $this->public_fullpage = new LexAI_Public_FullPage($this->usage_limiter, $this->db, $export_handler);
    }

    /**
     * Check if current page needs chatbot functionality
     */
    private function is_chatbot_page() {
        // Check if shortcode is present in content
        global $post;

        if (!$post) {
            return false;
        }

        // Check for LexAI shortcodes
        if (has_shortcode($post->post_content, 'lexai_chat') ||
            has_shortcode($post->post_content, 'lexai_assistant') ||
            has_shortcode($post->post_content, 'lexai_legal_chat')) {
            return true;
        }

        // Check if it's a page with chatbot widget
        $chatbot_pages = get_option('lexai_chatbot_pages', array());
        if (in_array($post->ID, $chatbot_pages)) {
            return true;
        }

        return false;
    }

    /**
     * Setup file cleanup
     */
    private function setup_file_cleanup() {
        add_action('lexai_cleanup_files', array($this, 'cleanup_expired_files'));
    }

    /**
     * Cleanup expired files
     */
    public function cleanup_expired_files() {
        $file_handler = new LexAI_File_Handler();
        $file_handler->cleanup_expired_files();
    }

    /**
     * Define admin hooks
     */
    private function define_admin_hooks() {
        if (is_admin()) {
            // Register admin menu immediately, not in lazy loading
            add_action('admin_menu', array($this, 'add_admin_menu'));
            add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
            add_action('admin_init', array($this, 'admin_init'));
            add_action('admin_notices', array($this, 'add_admin_notices'));
        }
    }
    
    /**
     * Define public hooks
     */
    private function define_public_hooks() {
        // Public hooks will be defined in the public class
    }

    /**
     * Add custom cron schedules
     */
    public function add_cron_schedules($schedules) {
        $schedules['every_minute'] = array(
            'interval' => 60,
            'display' => __('Cada minuto', 'lexai')
        );

        $schedules['every_five_minutes'] = array(
            'interval' => 300,
            'display' => __('Cada 5 minutos', 'lexai')
        );

        return $schedules;
    }

    /**
     * Process vector queue via cron
     */
    public function process_vector_queue_cron() {
        try {
            if (class_exists('LexAI_Vector_Admin')) {
                $vector_admin = new LexAI_Vector_Admin();
                $vector_admin->background_queue_processor();
            }
        } catch (Exception $e) {
            error_log('LexAI Cron Queue Processing Error: ' . $e->getMessage());
        }
    }

    /**
     * Check for plugin updates and run migrations
     * LEX-LOGIC-001: Critical migration logic moved from activation hook
     */
    public function check_for_updates() {
        $current_version = LEXAI_VERSION;
        $stored_version = get_option('lexai_version', '0.0.0');

        if (version_compare($stored_version, $current_version, '<')) {
            // Si la versión almacenada es menor, ejecutar migraciones
            if (version_compare($stored_version, '1.0.0', '<')) {
                // Migración crítica de claves API a formato encriptado
                if (class_exists('LexAI_Activator')) {
                    try {
                        LexAI_Activator::migrate_api_keys_to_encrypted();
                        error_log('LexAI: API keys migration completed successfully.');
                    } catch (Exception $e) {
                        error_log('LexAI: API keys migration failed: ' . $e->getMessage());
                    }
                }
            }
            
            // Run database migrations
            if (class_exists('LexAI_Database_Migration')) {
                try {
                    LexAI_Database_Migration::run_migrations();
                    error_log('LexAI: Database migrations completed successfully.');
                } catch (Exception $e) {
                    error_log('LexAI: Database migration failed: ' . $e->getMessage());
                }
            }

            // MCP database initialization removed - now using native implementation only

            // Force creation of specialized agents if they don't exist
            $this->ensure_specialized_agents_exist();

            // Actualizar la versión en la base de datos
            update_option('lexai_version', $current_version);
        }
    }

    /**
     * Ensure specialized agents exist
     */
    private function ensure_specialized_agents_exist() {
        global $wpdb;

        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;

        // Check if we have specialized agents (more than just orchestrator and validator)
        $agent_count = $wpdb->get_var("SELECT COUNT(*) FROM $agents_table WHERE status = 'active'");

        if ($agent_count < 5) { // If we have less than 5 agents, create the specialized ones
            require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-activator.php';
            LexAI_Activator::force_create_specialized_agents();
            error_log('LexAI: Automatically created specialized agents on plugin load.');
        }
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Main menu page
        add_menu_page(
            __('LexAI', 'lexai'),
            __('LexAI', 'lexai'),
            'manage_options',
            'lexai',
            array($this, 'display_main_page'),
            'dashicons-admin-comments',
            30
        );
        
        // Submenu pages
        add_submenu_page(
            'lexai',
            __('Panel Principal', 'lexai'),
            __('Panel Principal', 'lexai'),
            'manage_options',
            'lexai',
            array($this, 'display_main_page')
        );
        
        add_submenu_page(
            'lexai',
            __('Gestión de Agentes', 'lexai'),
            __('Agentes', 'lexai'),
            'manage_options',
            'lexai-agents',
            array($this, 'display_agents_page')
        );
        
        add_submenu_page(
            'lexai',
            __('Claves API', 'lexai'),
            __('Claves API', 'lexai'),
            'manage_options',
            'lexai-api-keys',
            array($this, 'display_api_keys_page')
        );
        
        add_submenu_page(
            'lexai',
            __('Base de Conocimientos', 'lexai'),
            __('Conocimientos', 'lexai'),
            'manage_options',
            'lexai-knowledge',
            array($this, 'display_knowledge_page')
        );
        
        add_submenu_page(
            'lexai',
            __('Configuración', 'lexai'),
            __('Configuración', 'lexai'),
            'manage_options',
            'lexai-settings',
            array($this, 'display_settings_page')
        );
        
        add_submenu_page(
            'lexai',
            __('Configuración de Estilos', 'lexai'),
            __('Estilos', 'lexai'),
            'manage_options',
            'lexai-styles',
            array($this, 'display_styles_page')
        );
        
        add_submenu_page(
            'lexai',
            __('Estadísticas', 'lexai'),
            __('Estadísticas', 'lexai'),
            'manage_options',
            'lexai-statistics',
            array($this, 'display_statistics_page')
        );

        add_submenu_page(
            'lexai',
            __('Pruebas Google Search', 'lexai'),
            __('Google Search', 'lexai'),
            'manage_options',
            'lexai-google-search-test',
            array($this, 'display_google_search_test_page')
        );

        add_submenu_page(
            'lexai',
            __('Herramientas Nativas', 'lexai'),
            __('Herramientas', 'lexai'),
            'manage_options',
            'lexai-native-tools',
            array($this, 'display_native_tools_page')
        );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on LexAI admin pages
        if (strpos($hook, 'lexai') === false) {
            return;
        }
        
        // Enqueue CSS
        wp_enqueue_style(
            'lexai-admin-css',
            LEXAI_PLUGIN_URL . 'admin/css/lexai-admin.css',
            array(),
            LEXAI_VERSION
        );
        
        // Enqueue JavaScript
        wp_enqueue_script(
            'lexai-admin-js',
            LEXAI_PLUGIN_URL . 'admin/js/lexai-admin.js',
            array('jquery', 'wp-util'),
            LEXAI_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('lexai-admin-js', 'lexaiAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('lexai_admin_nonce'),
            'strings' => array(
                'confirmDelete' => __('¿Estás seguro de que quieres eliminar este elemento?', 'lexai'),
                'saving' => __('Guardando...', 'lexai'),
                'saved' => __('Guardado', 'lexai'),
                'error' => __('Error', 'lexai'),
                'success' => __('Éxito', 'lexai')
            )
        ));
        
        // Enqueue WordPress media uploader
        wp_enqueue_media();
    }

    /**
     * Admin initialization
     */
    public function admin_init() {
        // Register settings if needed
        register_setting('lexai_settings', 'lexai_settings');
    }

    /**
     * Display main admin page
     */
    public function display_main_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/main-page.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/main-page.php';
        } else {
            echo '<div class="wrap"><h1>LexAI</h1><p>Panel principal en desarrollo...</p></div>';
        }
    }
    
    /**
     * Display agents management page
     */
    public function display_agents_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/agents-page.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/agents-page.php';
        } else {
            echo '<div class="wrap"><h1>Gestión de Agentes</h1><p>Página en desarrollo...</p></div>';
        }
    }
    
    /**
     * Display API keys management page
     */
    public function display_api_keys_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/api-keys-page.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/api-keys-page.php';
        } else {
            echo '<div class="wrap"><h1>Claves API</h1><p>Página en desarrollo...</p></div>';
        }
    }
    
    /**
     * Display knowledge base management page
     */
    public function display_knowledge_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/knowledge-page.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/knowledge-page.php';
        } else {
            echo '<div class="wrap"><h1>Base de Conocimientos</h1><p>Página en desarrollo...</p></div>';
        }
    }
    
    /**
     * Display settings page
     */
    public function display_settings_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/settings-page.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/settings-page.php';
        } else {
            echo '<div class="wrap"><h1>Configuración</h1><p>Página en desarrollo...</p></div>';
        }
    }
    
    /**
     * Display styles configuration page
     */
    public function display_styles_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/styles-page.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/styles-page.php';
        } else {
            echo '<div class="wrap"><h1>Configuración de Estilos</h1><p>Página en desarrollo...</p></div>';
        }
    }
    
    /**
     * Display statistics page
     */
    public function display_statistics_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/statistics-page.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/statistics-page.php';
        } else {
            echo '<div class="wrap"><h1>Estadísticas</h1><p>Página en desarrollo...</p></div>';
        }
    }

    /**
     * Display Google Search test page
     */
    public function display_google_search_test_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/google-search-test.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/google-search-test.php';
        } else {
            echo '<div class="wrap"><h1>Pruebas Google Search</h1><p>Página en desarrollo...</p></div>';
        }
    }

    /**
     * Display native tools management page
     */
    public function display_native_tools_page() {
        if (file_exists(LEXAI_PLUGIN_DIR . 'admin/views/mcp-native-page.php')) {
            include LEXAI_PLUGIN_DIR . 'admin/views/mcp-native-page.php';
        } else {
            echo '<div class="wrap"><h1>Herramientas Nativas</h1><p>Página en desarrollo...</p></div>';
        }
    }

    /**
     * Add admin notices
     */
    public function add_admin_notices() {
        // Check for database issues
        global $wpdb;
        $required_tables = array(
            LEXAI_AGENTS_TABLE,
            LEXAI_CONVERSATIONS_TABLE,
            LEXAI_MESSAGES_TABLE,
            LEXAI_API_KEYS_TABLE,
            LEXAI_USAGE_LOGS_TABLE,
            LEXAI_TASKS_TABLE,
            LEXAI_TASK_EXECUTIONS_TABLE
        );
        
        $missing_tables = array();
        foreach ($required_tables as $table) {
            $table_name = $wpdb->prefix . $table;
            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
            if (!$exists) {
                $missing_tables[] = $table;
            }
        }
        
        if (!empty($missing_tables)) {
            echo '<div class="notice notice-error"><p>';
            echo __('LexAI: Faltan tablas de base de datos. Desactiva y reactiva el plugin para crearlas.', 'lexai');
            echo '</p></div>';
        }

        // Display activation errors
        if ($error_message = get_transient('lexai_activation_error')) {
            echo '<div class="notice notice-error is-dismissible"><p>' . esc_html($error_message) . '</p></div>';
            delete_transient('lexai_activation_error');
        }
    }

    /**
     * Process chat task in the background.
     */
    public function process_chat_task_background($task_id) {
        try {
            // Initialize components if they haven't been already
            if (!$this->db) $this->db = new LexAI_DB();
            $this->init_api_components(); // Ensures orchestrator is ready

            $task = $this->db->get_chat_task($task_id);

            if (!$task || $task->status !== 'pending') {
                error_log("LexAI Background: Task #$task_id not found or not pending.");
                return;
            }

            // Mark task as in-progress
            $this->db->update_chat_task_status($task_id, 'in_progress');

            // Get user_id from the conversation
            $conversation = $this->db->get_conversation($task->conversation_id);
            if (!$conversation) {
                throw new Exception("Conversation not found for task #$task_id");
            }
            $user_id = $conversation->user_id;

            // Execute the orchestrator
            $result = $this->orchestrator->process_query(
                $user_id,
                $task->conversation_id,
                $task->initial_prompt
            );

            // Mark task as completed or failed
            $final_status = $result['success'] ? 'completed' : 'failed';
            $this->db->update_chat_task_status($task_id, $final_status);

            // If failed, save an error message
            if (!$result['success']) {
                $error_message = $result['error'] ?? __('An unknown error occurred during processing.', 'lexai');
                $this->db->add_message($task->conversation_id, 'assistant', "Error: " . $error_message, ['type' => 'error']);
            }

        } catch (Exception $e) {
            error_log("LexAI Background Processing Error for task #$task_id: " . $e->getMessage());
            if (isset($this->db) && isset($task_id)) {
                $this->db->update_chat_task_status($task_id, 'failed');
                if(isset($task) && isset($task->conversation_id)) {
                    $this->db->add_message($task->conversation_id, 'assistant', "Error: " . $e->getMessage(), ['type' => 'error']);
                }
            }
        }
    }

    /**
     * Dequeue conflicting scripts to prevent JavaScript conflicts
     * Fixes JS-CONFLICT-001: Identifier 'lazyloadRunObserver' has already been declared
     */
    public function dequeue_conflicting_scripts() {
        // Only execute on the fullpage chat template
        if (is_page_template('templates/fullpage-chat-template.php')) {
            global $wp_scripts;
            $handles_to_dequeue = array(
                'lazyload',
                'lazy-load',
                'theme-lazyload',
                'wp-lazyload',
                'elementor-frontend',
                'elementor-webpack-runtime'
            );

            // Recorrer todos los scripts encolados y eliminar los conflictivos
            if (isset($wp_scripts->queue)) {
                foreach ($wp_scripts->queue as $handle) {
                    foreach ($handles_to_dequeue as $conflict) {
                        if (strpos($handle, $conflict) !== false) {
                            wp_dequeue_script($handle);
                            wp_deregister_script($handle);
                            error_log("LexAI: Dequeued conflicting script: $handle");
                        }
                    }
                }
            }
        }
    }
}

/**
 * Plugin activation hook
 */
function activate_lexai() {
    require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-activator.php';
    LexAI_Activator::activate();
}

/**
 * Plugin deactivation hook
 */
function deactivate_lexai() {
    require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-deactivator.php';
    LexAI_Deactivator::deactivate();
}

// Register activation and deactivation hooks
register_activation_hook(__FILE__, 'activate_lexai');
register_deactivation_hook(__FILE__, 'deactivate_lexai');

/**
 * Initialize the plugin
 */
function run_lexai() {
    return LexAI::get_instance();
}

// Start the plugin
run_lexai();
