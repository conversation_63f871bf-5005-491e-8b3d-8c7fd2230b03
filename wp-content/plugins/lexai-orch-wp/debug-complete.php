<?php
/**
 * Script de Diagnóstico Completo - LexAI
 * 
 * Verifica CSS, JavaScript, autenticación y configuración
 *
 * @package LexAI
 * @since 2.0.1
 */

// Simular entorno WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/');
}

// Configurar headers
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnóstico Completo - LexAI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; border: 1px solid #e9ecef; margin: 10px 0; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .btn { display: inline-block; padding: 12px 24px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; font-weight: 600; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        ul { padding-left: 20px; }
        li { margin: 8px 0; }
        h1, h2, h3 { color: #333; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnóstico Completo - LexAI</h1>
        <p><strong>Ejecutado:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php
        // 1. Verificar archivos CSS
        echo "<h2>🎨 1. Verificación de Archivos CSS</h2>\n";
        
        $css_file = LEXAI_PLUGIN_DIR . 'assets/css/lexai-fullpage-chat.css';
        if (file_exists($css_file)) {
            $css_timestamp = filemtime($css_file);
            $css_size = filesize($css_file);
            $css_content = file_get_contents($css_file);
            
            echo "<div class='success'>\n";
            echo "<h3>✅ Archivo CSS Encontrado</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>Archivo:</strong> lexai-fullpage-chat.css</li>\n";
            echo "<li><strong>Tamaño:</strong> " . number_format($css_size) . " bytes</li>\n";
            echo "<li><strong>Última modificación:</strong> " . date('Y-m-d H:i:s', $css_timestamp) . "</li>\n";
            echo "<li><strong>Timestamp:</strong> {$css_timestamp}</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
            
            // Verificar cambios específicos
            $changes_to_check = array(
                'rgb(145 153 163)' => 'Conversation items - fondo gris',
                'rgb(98 102 107 / 27%)' => 'Search box - fondo gris',
                '--lexai-conversation-bg' => 'Variables de conversation items',
                '--lexai-search-bg' => 'Variables de search box',
                'body:not(.lexai-theme-dark) .lexai-conversation-item' => 'Override light theme',
                '.lexai-theme-dark .lexai-conversation-item' => 'Override dark theme'
            );
            
            echo "<h3>🔍 Verificación de Cambios CSS:</h3>\n";
            echo "<ul>\n";
            $all_changes_found = true;
            foreach ($changes_to_check as $search => $description) {
                if (strpos($css_content, $search) !== false) {
                    echo "<li class='status-ok'>✅ {$description}: Encontrado</li>\n";
                } else {
                    echo "<li class='status-error'>❌ {$description}: NO encontrado</li>\n";
                    $all_changes_found = false;
                }
            }
            echo "</ul>\n";
            
            if ($all_changes_found) {
                echo "<div class='success'><p><strong>🎉 Todos los cambios CSS están aplicados correctamente</strong></p></div>\n";
            } else {
                echo "<div class='error'><p><strong>⚠️ Algunos cambios CSS faltan</strong></p></div>\n";
            }
            
        } else {
            echo "<div class='error'>\n";
            echo "<h3>❌ Archivo CSS No Encontrado</h3>\n";
            echo "<p>No se pudo encontrar: {$css_file}</p>\n";
            echo "</div>\n";
        }
        
        // 2. Verificar archivos JavaScript
        echo "<h2>📜 2. Verificación de Archivos JavaScript</h2>\n";
        
        $js_file = LEXAI_PLUGIN_DIR . 'assets/js/lexai-fullpage-chat.js';
        if (file_exists($js_file)) {
            $js_timestamp = filemtime($js_file);
            $js_size = filesize($js_file);
            $js_content = file_get_contents($js_file);
            
            echo "<div class='success'>\n";
            echo "<h3>✅ Archivo JavaScript Encontrado</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>Archivo:</strong> lexai-fullpage-chat.js</li>\n";
            echo "<li><strong>Tamaño:</strong> " . number_format($js_size) . " bytes</li>\n";
            echo "<li><strong>Última modificación:</strong> " . date('Y-m-d H:i:s', $js_timestamp) . "</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
            
            // Verificar correcciones JavaScript
            $js_checks = array(
                'loadConfiguration()' => 'Función de carga de configuración',
                'window.lexaiConfig' => 'Referencia a configuración global',
                'setTimeout(function() {' => 'Delay para carga de configuración',
                'lazyloadRunObserver' => 'Prevención de conflictos lazyload'
            );
            
            echo "<h3>🔍 Verificación de Correcciones JavaScript:</h3>\n";
            echo "<ul>\n";
            foreach ($js_checks as $search => $description) {
                if (strpos($js_content, $search) !== false) {
                    echo "<li class='status-ok'>✅ {$description}: Encontrado</li>\n";
                } else {
                    echo "<li class='status-warning'>⚠️ {$description}: No encontrado</li>\n";
                }
            }
            echo "</ul>\n";
            
        } else {
            echo "<div class='error'>\n";
            echo "<h3>❌ Archivo JavaScript No Encontrado</h3>\n";
            echo "<p>No se pudo encontrar: {$js_file}</p>\n";
            echo "</div>\n";
        }
        
        // 3. Verificar template
        echo "<h2>📄 3. Verificación de Template</h2>\n";
        
        $template_file = LEXAI_PLUGIN_DIR . 'templates/fullpage-chat-template.php';
        if (file_exists($template_file)) {
            $template_content = file_get_contents($template_file);
            
            echo "<div class='success'>\n";
            echo "<h3>✅ Template Encontrado</h3>\n";
            echo "</div>\n";
            
            $template_checks = array(
                'wp_localize_script' => 'Localización de script',
                'window.lexaiConfig' => 'Configuración inline',
                'lazyloadRunObserver' => 'Prevención de conflictos',
                'filemtime($css_file)' => 'Cache busting CSS'
            );
            
            echo "<h3>🔍 Verificación de Template:</h3>\n";
            echo "<ul>\n";
            foreach ($template_checks as $search => $description) {
                if (strpos($template_content, $search) !== false) {
                    echo "<li class='status-ok'>✅ {$description}: Implementado</li>\n";
                } else {
                    echo "<li class='status-error'>❌ {$description}: Faltante</li>\n";
                }
            }
            echo "</ul>\n";
            
        } else {
            echo "<div class='error'>\n";
            echo "<h3>❌ Template No Encontrado</h3>\n";
            echo "</div>\n";
        }
        
        // 4. URLs de prueba
        echo "<h2>🔗 4. URLs de Prueba</h2>\n";
        
        $base_url = 'https://tuasesorlegalvirtual.online';
        $css_url = $base_url . '/wp-content/plugins/lexai-orch-wp/assets/css/lexai-fullpage-chat.css';
        $chat_url = $base_url . '/chat/';
        
        echo "<div class='info'>\n";
        echo "<h3>🌐 Enlaces de Verificación</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Chat:</strong> <a href='{$chat_url}' target='_blank'>{$chat_url}</a></li>\n";
        echo "<li><strong>CSS Original:</strong> <a href='{$css_url}' target='_blank'>{$css_url}</a></li>\n";
        echo "<li><strong>CSS con Cache Bust:</strong> <a href='{$css_url}?v={$css_timestamp}' target='_blank'>{$css_url}?v={$css_timestamp}</a></li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // 5. Instrucciones de solución
        echo "<h2>🛠️ 5. Instrucciones de Solución</h2>\n";
        
        echo "<div class='warning'>\n";
        echo "<h3>📋 Pasos para Resolver Problemas</h3>\n";
        echo "<ol>\n";
        echo "<li><strong>Limpiar caché del navegador:</strong>\n";
        echo "<ul>\n";
        echo "<li>Presiona <code>Ctrl+Shift+R</code> (Chrome/Firefox)</li>\n";
        echo "<li>O abre DevTools (F12) → Network → Disable cache</li>\n";
        echo "</ul></li>\n";
        echo "<li><strong>Verificar autenticación:</strong>\n";
        echo "<ul>\n";
        echo "<li>Asegúrate de estar logueado en WordPress</li>\n";
        echo "<li>Ve a <a href='{$base_url}/wp-admin/' target='_blank'>wp-admin</a> para verificar</li>\n";
        echo "</ul></li>\n";
        echo "<li><strong>Desactivar/Reactivar plugin:</strong>\n";
        echo "<ul>\n";
        echo "<li>Ve a Plugins → Desactivar LexAI → Reactivar</li>\n";
        echo "</ul></li>\n";
        echo "<li><strong>Verificar conflictos:</strong>\n";
        echo "<ul>\n";
        echo "<li>Desactiva temporalmente otros plugins</li>\n";
        echo "<li>Especialmente Elementor, WP Rocket, etc.</li>\n";
        echo "</ul></li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        // 6. Resumen final
        echo "<h2>📊 6. Resumen Final</h2>\n";
        
        $css_ok = file_exists($css_file) && $all_changes_found;
        $js_ok = file_exists($js_file);
        $template_ok = file_exists($template_file);
        
        if ($css_ok && $js_ok && $template_ok) {
            echo "<div class='success'>\n";
            echo "<h3>🎉 Sistema Completamente Funcional</h3>\n";
            echo "<p>Todos los archivos están presentes y las correcciones aplicadas.</p>\n";
            echo "<p><strong>Próximo paso:</strong> Limpiar caché del navegador y probar el chat.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div class='error'>\n";
            echo "<h3>⚠️ Problemas Detectados</h3>\n";
            echo "<ul>\n";
            if (!$css_ok) echo "<li>❌ Problemas con CSS</li>\n";
            if (!$js_ok) echo "<li>❌ Problemas con JavaScript</li>\n";
            if (!$template_ok) echo "<li>❌ Problemas con Template</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        }
        ?>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="<?php echo $chat_url; ?>" class="btn btn-success" target="_blank">
                🚀 Ir al Chat
            </a>
            <a href="<?php echo $css_url; ?>?v=<?php echo $css_timestamp; ?>" class="btn" target="_blank">
                📄 Ver CSS
            </a>
            <a href="javascript:location.reload();" class="btn btn-warning">
                🔄 Recargar Diagnóstico
            </a>
        </div>
        
        <script>
            console.log('🔍 LexAI Diagnóstico Completo');
            console.log('CSS Timestamp:', <?php echo $css_timestamp; ?>);
            console.log('Cambios aplicados:', <?php echo $all_changes_found ? 'true' : 'false'; ?>);
            
            // Auto-refresh cada 30 segundos para monitoreo
            setTimeout(function() {
                if (confirm('¿Recargar diagnóstico para verificar cambios?')) {
                    location.reload();
                }
            }, 30000);
        </script>
    </div>
</body>
</html>
