<?php
/**
 * API Handler Class - Manages Gemini API interactions and key rotation
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI API Handler Class
 */
class LexAI_API_Handler {

    /**
     * Gemini API base URL
     */
    const GEMINI_API_BASE = 'https://generativelanguage.googleapis.com/v1beta/models/';

    /**
     * Default model
     */
    const DEFAULT_MODEL = 'gemini-2.5-flash';

    /**
     * Default embedding model optimized for legal documents
     */
    const DEFAULT_EMBEDDING_MODEL = 'text-embedding-004';

    /**
     * Embedding configuration for legal documents
     */
    const EMBEDDING_CONFIG = array(
        'model' => 'text-embedding-004',
        'dimensions' => 768,
        'task_type' => 'RETRIEVAL_DOCUMENT',
        'metric' => 'cosine',
        'max_tokens' => 2048
    );

    /**
     * Circuit breaker configuration
     */
    private $circuit_breaker_config = array(
        'failure_threshold' => 5,      // Number of failures before opening circuit
        'timeout' => 60,               // Seconds to wait before trying again
        'success_threshold' => 3       // Successful calls needed to close circuit
    );

    /**
     * Constructor
     */
    public function __construct() {
        // Initialize circuit breaker
        $this->init_circuit_breaker();
    }

    /**
     * Initialize circuit breaker
     */
    private function init_circuit_breaker() {
        // Set default circuit state if not exists
        if (get_transient('lexai_circuit_breaker_state') === false) {
            set_transient('lexai_circuit_breaker_state', 'closed', HOUR_IN_SECONDS);
            set_transient('lexai_circuit_breaker_failures', 0, HOUR_IN_SECONDS);
            set_transient('lexai_circuit_breaker_successes', 0, HOUR_IN_SECONDS);
        }
    }
    
    /**
     * Get available API key from pool (SECURE VERSION)
     */
    public function get_available_api_key($provider = 'gemini') {
        global $wpdb;

        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;

        // Get active API keys metadata only (NO RAW API KEY)
        $api_key_data = $wpdb->get_row($wpdb->prepare(
            "SELECT id, provider, usage_count, rate_limit_remaining, last_used
             FROM $api_keys_table
             WHERE status = 'active'
             AND provider = %s
             ORDER BY usage_count ASC, last_used ASC
             LIMIT 1",
            $provider
        ));

        if (!$api_key_data) {
            throw new Exception(sprintf(__('No hay claves API de %s disponibles', 'lexai'), $provider));
        }

        // Get decrypted API key securely
        $decrypted_key = $this->get_decrypted_api_key($api_key_data->id);

        // Update usage count and last used timestamp
        $wpdb->update(
            $api_keys_table,
            array(
                'usage_count' => $api_key_data->usage_count + 1,
                'last_used' => current_time('mysql')
            ),
            array('id' => $api_key_data->id),
            array('%d', '%s'),
            array('%d')
        );

        // Return secure object with decrypted key
        return (object) array(
            'id' => $api_key_data->id,
            'api_key' => $decrypted_key,
            'provider' => $api_key_data->provider,
            'usage_count' => $api_key_data->usage_count + 1,
            'rate_limit_remaining' => $api_key_data->rate_limit_remaining
        );
    }

    /**
     * Get active API key by provider (alias for backward compatibility)
     */
    public function get_active_api_key($provider = 'gemini') {
        return $this->get_available_api_key($provider);
    }

    /**
     * Get decrypted API key by ID
     */
    private function get_decrypted_api_key($key_id) {
        global $wpdb;

        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;

        $encrypted_key = $wpdb->get_var($wpdb->prepare(
            "SELECT encrypted_api_key FROM $api_keys_table WHERE id = %d",
            $key_id
        ));

        if (!$encrypted_key) {
            throw new Exception(__('Clave API no encontrada', 'lexai'));
        }

        return $this->decrypt_api_key($encrypted_key);
    }

    /**
     * Encrypt API key using WordPress salts
     */
    private function encrypt_api_key($api_key) {
        $encryption_key = $this->get_encryption_key();
        $iv = openssl_random_pseudo_bytes(16);

        $encrypted = openssl_encrypt(
            $api_key,
            'AES-256-CBC',
            $encryption_key,
            OPENSSL_RAW_DATA,
            $iv
        );

        if ($encrypted === false) {
            throw new Exception(__('Error al encriptar la clave API', 'lexai'));
        }

        // Combine IV and encrypted data
        return base64_encode($iv . $encrypted);
    }

    /**
     * Decrypt API key using WordPress salts
     */
    private function decrypt_api_key($encrypted_data) {
        $encryption_key = $this->get_encryption_key();
        $data = base64_decode($encrypted_data);

        if ($data === false || strlen($data) < 16) {
            throw new Exception(__('Datos de encriptación inválidos', 'lexai'));
        }

        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);

        $decrypted = openssl_decrypt(
            $encrypted,
            'AES-256-CBC',
            $encryption_key,
            OPENSSL_RAW_DATA,
            $iv
        );

        if ($decrypted === false) {
            throw new Exception(__('Error al desencriptar la clave API', 'lexai'));
        }

        return $decrypted;
    }

    /**
     * Generate encryption key from WordPress salts using PBKDF2
     */
    private function get_encryption_key() {
        return self::generate_encryption_key();
    }

    /**
     * Static method to generate encryption key for use by other classes
     */
    public static function generate_encryption_key() {
        $salt_data = wp_salt('auth') . wp_salt('secure_auth') . wp_salt('logged_in') . wp_salt('nonce');
        $salt = hash('sha256', $salt_data, true);

        // Use PBKDF2 for more robust key derivation
        return hash_pbkdf2('sha256', $salt_data, $salt, 10000, 32, true);
    }

    /**
     * Static method to encrypt API keys for use by other classes
     */
    public static function encrypt_api_key_static($api_key) {
        $encryption_key = self::generate_encryption_key();
        $iv = openssl_random_pseudo_bytes(16);

        $encrypted = openssl_encrypt(
            $api_key,
            'AES-256-CBC',
            $encryption_key,
            OPENSSL_RAW_DATA,
            $iv
        );

        if ($encrypted === false) {
            throw new Exception(__('Error al encriptar la clave API', 'lexai'));
        }

        return base64_encode($iv . $encrypted);
    }

    /**
     * Static method to decrypt API keys for use by other classes
     */
    public static function decrypt_api_key_static($encrypted_data) {
        $encryption_key = self::generate_encryption_key();
        $data = base64_decode($encrypted_data);

        if ($data === false || strlen($data) < 16) {
            throw new Exception(__('Datos de encriptación inválidos', 'lexai'));
        }

        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);

        $decrypted = openssl_decrypt(
            $encrypted,
            'AES-256-CBC',
            $encryption_key,
            OPENSSL_RAW_DATA,
            $iv
        );

        if ($decrypted === false) {
            throw new Exception(__('Error al desencriptar la clave API', 'lexai'));
        }

        return $decrypted;
    }

    /**
     * Save API key securely (encrypted)
     */
    public function save_api_key_secure($api_key, $provider = 'gemini', $name = '') {
        global $wpdb;

        if (empty($api_key)) {
            throw new Exception(__('La clave API no puede estar vacía', 'lexai'));
        }

        // Validate API key format
        if (!$this->validate_api_key_format($api_key, $provider)) {
            throw new Exception(__('Formato de clave API inválido', 'lexai'));
        }

        // Test API key before saving
        if (!$this->test_api_key_connection($api_key, $provider)) {
            throw new Exception(__('La clave API no es válida o no tiene acceso', 'lexai'));
        }

        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;

        // Encrypt the API key
        $encrypted_key = $this->encrypt_api_key($api_key);

        // Insert encrypted key
        $result = $wpdb->insert(
            $api_keys_table,
            array(
                'name' => sanitize_text_field($name),
                'provider' => sanitize_text_field($provider),
                'encrypted_api_key' => $encrypted_key,
                'status' => 'active',
                'usage_count' => 0,
                'rate_limit_remaining' => 1000, // Default limit
                'created_at' => current_time('mysql'),
                'last_used' => null
            ),
            array('%s', '%s', '%s', '%s', '%d', '%d', '%s', '%s')
        );

        if ($result === false) {
            throw new Exception(__('Error al guardar la clave API', 'lexai'));
        }

        return $wpdb->insert_id;
    }

    /**
     * Validate API key format
     */
    private function validate_api_key_format($api_key, $provider) {
        switch ($provider) {
            case 'gemini':
                // Gemini API keys typically start with 'AIza' and are 39 characters
                return preg_match('/^AIza[A-Za-z0-9_-]{35}$/', $api_key);
            default:
                return strlen($api_key) >= 20; // Generic validation
        }
    }

    /**
     * Test API key connection
     */
    private function test_api_key_connection($api_key, $provider) {
        try {
            if ($provider === 'gemini') {
                $response = wp_remote_get(
                    'https://generativelanguage.googleapis.com/v1beta/models?key=' . $api_key,
                    array(
                        'timeout' => 10,
                        'headers' => array(
                            'Content-Type' => 'application/json'
                        )
                    )
                );

                if (is_wp_error($response)) {
                    return false;
                }

                $status_code = wp_remote_retrieve_response_code($response);
                return $status_code === 200;
            }

            return false;
        } catch (Exception $e) {
            error_log('API Key test error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Upload file to Gemini
     */
    public function upload_file_to_gemini($file_path, $mime_type) {
        $api_key_obj = $this->get_available_api_key();
        if (!$api_key_obj) {
            throw new Exception(__('No hay claves API disponibles', 'lexai'));
        }
        $api_key = $api_key_obj->api_key;

        // First, upload the file
        $upload_url = 'https://generativelanguage.googleapis.com/upload/v1beta/files?key=' . $api_key;

        $file_data = file_get_contents($file_path);
        $filename = basename($file_path);

        $headers = array(
            'X-Goog-Upload-Protocol' => 'resumable',
            'X-Goog-Upload-Command' => 'start',
            'X-Goog-Upload-Header-Content-Length' => strlen($file_data),
            'X-Goog-Upload-Header-Content-Type' => $mime_type,
            'Content-Type' => 'application/json'
        );

        $metadata = array(
            'file' => array(
                'display_name' => $filename
            )
        );

        $response = wp_remote_post($upload_url, array(
            'headers' => $headers,
            'body' => json_encode($metadata),
            'timeout' => 60
        ));

        if (is_wp_error($response)) {
            throw new Exception('Error uploading file: ' . $response->get_error_message());
        }

        $upload_url = wp_remote_retrieve_header($response, 'x-goog-upload-url');
        if (!$upload_url) {
            throw new Exception('No upload URL received');
        }

        // Upload the actual file data
        $upload_response = wp_remote_post($upload_url, array(
            'headers' => array(
                'Content-Length' => strlen($file_data),
                'X-Goog-Upload-Offset' => '0',
                'X-Goog-Upload-Command' => 'upload, finalize'
            ),
            'body' => $file_data,
            'timeout' => 120
        ));

        if (is_wp_error($upload_response)) {
            throw new Exception('Error uploading file data: ' . $upload_response->get_error_message());
        }

        $upload_result = json_decode(wp_remote_retrieve_body($upload_response), true);

        if (isset($upload_result['file']['uri'])) {
            return $upload_result['file']['uri'];
        }

        throw new Exception('File upload failed');
    }

    /**
     * Make API request to Gemini with dynamic configuration
     */
    public function make_gemini_request($messages, $system_instruction = null, $tools = null, $model = null, $files = array(), $task_type = 'default', $custom_config = array()) {
        $model = $model ?: self::DEFAULT_MODEL;

        // Check cache first for cacheable requests (no files, no tools)
        $cache_key = null;
        if (empty($files) && empty($tools)) {
            $cache_key = $this->generate_cache_key($messages, $system_instruction, $model);
            $cached_response = $this->get_cached_response($cache_key);

            if ($cached_response !== false) {
                return $cached_response;
            }
        }

        $api_key_obj = $this->get_available_api_key();
        $api_key = $api_key_obj->api_key;
        
        $url = self::GEMINI_API_BASE . $model . ':generateContent';
        
        $payload = array(
            'contents' => $this->format_messages_for_gemini($messages, $files)
        );
        
        // Add system instruction if provided
        if ($system_instruction) {
            $payload['system_instruction'] = array(
                'parts' => array(
                    array('text' => $system_instruction)
                )
            );
        }
        
        // Add tools if provided
        if ($tools && is_array($tools)) {
            $payload['tools'] = $this->format_tools_for_gemini($tools);
        }
        
        // Add dynamic generation config based on model and task
        if (class_exists('LexAI_Model_Config')) {
            $payload['generationConfig'] = LexAI_Model_Config::get_generation_config($model, $task_type, $custom_config);
        } else {
            // Fallback to static config
            $payload['generationConfig'] = array(
                'temperature' => $custom_config['temperature'] ?? 0.7,
                'topP' => $custom_config['topP'] ?? 0.8,
                'topK' => $custom_config['topK'] ?? 40,
                'maxOutputTokens' => $custom_config['maxOutputTokens'] ?? 8192
            );
        }
        
        try {
            // LEX-SYNERGY-001: ENVOLVER LA LLAMADA CON EL CIRCUIT BREAKER
            $response = $this->execute_with_circuit_breaker(
                array($this, 'send_http_request'), // Función a llamar
                array($url, $payload, $api_key)    // Parámetros para la función
            );

            if (is_wp_error($response)) {
                // El Circuit Breaker registrará esto como un fallo
                throw new Exception($response->get_error_message());
            }

            $status_code = wp_remote_retrieve_response_code($response);
            if ($status_code >= 400) {
                $body = wp_remote_retrieve_body($response);
                $error_data = json_decode($body, true);
                $error_message = $error_data['error']['message'] ?? 'API Error';
                $this->handle_api_error($api_key_obj->id, $error_message, $status_code);
                throw new Exception($error_message);
            }

            // Cachear la respuesta si es exitosa
            if ($cache_key) {
                $this->cache_response($cache_key, $this->parse_gemini_response($response));
            }

            return $this->parse_gemini_response($response);

        } catch (Exception $e) {
            $this->handle_api_error($api_key_obj->id, $e->getMessage());
            throw $e; // Re-lanzar la excepción para que el orquestador la maneje
        }
    }
    
    /**
     * Make streaming API request to Gemini
     */
    public function make_gemini_streaming_request($messages, $system_instruction = null, $tools = null, $model = null, $files = array()) {
        $api_key_obj = $this->get_available_api_key();
        $api_key = $api_key_obj->api_key;
        $model = $model ?: self::DEFAULT_MODEL;
        
        $url = self::GEMINI_API_BASE . $model . ':streamGenerateContent?alt=sse';
        
        $payload = array(
            'contents' => $this->format_messages_for_gemini($messages, $files)
        );

        if ($system_instruction) {
            $payload['system_instruction'] = array(
                'parts' => array(
                    array('text' => $system_instruction)
                )
            );
        }
        
        if ($tools && is_array($tools)) {
            $payload['tools'] = $this->format_tools_for_gemini($tools);
        }
        
        $payload['generationConfig'] = array(
            'temperature' => 0.7,
            'topP' => 0.8,
            'topK' => 40,
            'maxOutputTokens' => 8192
        );
        
        try {
            // LEX-SYNERGY-001: ENVOLVER LA LLAMADA CON EL CIRCUIT BREAKER
            $response = $this->execute_with_circuit_breaker(
                array($this, 'send_streaming_request'), // Función a llamar
                array($url, $payload, $api_key)       // Parámetros para la función
            );

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            // LEX-LOGIC-003: NO HAY LÓGICA DE CACHÉ PARA STREAMING
            
            return $response; // Devolver la respuesta de streaming directamente

        } catch (Exception $e) {
            $this->handle_api_error($api_key_obj->id, $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Format messages for Gemini API - CORRECTED according to official documentation
     */
    private function format_messages_for_gemini($messages, $files = array()) {
        $formatted_messages = array();
        $last_user_message_index = -1;

        // Find the last user message to attach files to it
        foreach ($messages as $index => $message) {
            // Handle both array and object formats
            $role = is_array($message) ? $message['role'] : $message->role;
            if ($role === 'user') {
                $last_user_message_index = $index;
            }
        }

        foreach ($messages as $index => $message) {
            // Handle both array and object formats
            $role = is_array($message) ? $message['role'] : $message->role;
            $role = $role === 'assistant' ? 'model' : $role;

            $content = is_array($message) ? ($message['content'] ?? null) : ($message->content ?? null);

            $parts = [];
            if (isset($content) && $content !== null) {
                $parts[] = ['text' => $content];
            }

            // CRITICAL FIX: Handle function calls according to Gemini documentation
            $function_calls = is_array($message) ? ($message['function_calls'] ?? null) : ($message->function_calls ?? null);
            $function_responses = is_array($message) ? ($message['function_responses'] ?? null) : ($message->function_responses ?? null);

            if (isset($function_calls)) {
                // Model's response with function calls
                foreach ($function_calls as $function_call) {
                    $parts[] = ['functionCall' => $function_call['functionCall']];
                }
            } elseif (isset($function_responses)) {
                // User's message with function execution results
                foreach ($function_responses as $function_response) {
                    $parts[] = [
                        'functionResponse' => [
                            'name' => $function_response['name'],
                            'response' => $function_response['response']
                        ]
                    ];
                }
            }

            // LEGACY SUPPORT: Handle old format for backward compatibility
            $tool_calls = is_array($message) ? ($message['tool_calls'] ?? null) : ($message->tool_calls ?? null);
            $tool_outputs = is_array($message) ? ($message['tool_outputs'] ?? null) : ($message->tool_outputs ?? null);

            if (isset($tool_calls)) {
                foreach ($tool_calls as $tool_call) {
                    $parts[] = ['functionCall' => $tool_call['functionCall']];
                }
            } elseif (isset($tool_outputs)) {
                foreach ($tool_outputs as $tool_output) {
                    $response_data = $tool_output['output']['data'] ?? '{}';
                    $decoded_response = is_string($response_data) ? json_decode($response_data, true) : $response_data;

                    $parts[] = [
                        'functionResponse' => [
                            'name' => $tool_output['tool_call']['functionCall']['name'],
                            'response' => $decoded_response ?: array()
                        ]
                    ];
                }
            }

            // Attach files to the last user message or the current one if it's the only user message
            if ($role === 'user' && !empty($files) && ($index === $last_user_message_index || $last_user_message_index === -1)) {
                foreach ($files as $file) {
                    if (!empty($file['gemini_uri']) && $file['processing_status'] === 'completed') {
                        $parts[] = [
                            'file_data' => [
                                'mime_type' => $file['mime_type'],
                                'file_uri' => $file['gemini_uri']
                            ]
                        ];
                    }
                }
            }

            $formatted_message = [
                'role' => $role,
                'parts' => $parts
            ];

            $formatted_messages[] = $formatted_message;
        }

        return $formatted_messages;
    }
    
    /**
     * Format tools for Gemini API according to documentation
     */
    private function format_tools_for_gemini($tools) {
        if (empty($tools)) {
            return [];
        }

        $function_declarations = [];
        $agent_factory = new LexAI_Agent_Factory();

        foreach ($tools as $tool_item) {
            // Handle both tool names (strings) and tool schemas (arrays)
            if (is_string($tool_item)) {
                // This is a tool name
                $tool_name = $tool_item;

                if ($tool_name === 'google_search') {
                    // Google Search is a built-in tool, not a function declaration
                    continue;
                }

                $schema = $agent_factory->get_tool_schema($tool_name);
                if ($schema) {
                    // Ensure correct structure according to Gemini documentation
                    $function_declarations[] = $this->normalize_function_declaration($schema);
                }
            } elseif (is_array($tool_item)) {
                // This is already a tool schema (from agent selection tools)
                $function_declarations[] = $this->normalize_function_declaration($tool_item);
            }
        }

        $formatted_tools = [];
        
        // Add function declarations if any exist
        if (!empty($function_declarations)) {
            $formatted_tools[] = ['functionDeclarations' => $function_declarations];
        }

        // Add the built-in Google Search tool if it was requested
        if (in_array('google_search', $tools)) {
            $formatted_tools[] = ['google_search' => new stdClass()];
        }

        return $formatted_tools;
    }

    /**
     * Normalize function declaration to match Gemini API schema
     */
    private function normalize_function_declaration($schema) {
        // Ensure the schema follows the correct OpenAPI subset format
        $normalized = [
            'name' => $schema['name'] ?? '',
            'description' => $schema['description'] ?? '',
        ];

        if (isset($schema['parameters'])) {
            $normalized['parameters'] = $this->normalize_parameters($schema['parameters']);
        }

        return $normalized;
    }

    /**
     * Normalize parameters to match Gemini API requirements
     */
    private function normalize_parameters($parameters) {
        $normalized = [
            'type' => 'OBJECT',
            'properties' => [],
        ];

        if (isset($parameters['properties'])) {
            foreach ($parameters['properties'] as $prop_name => $prop_def) {
                $normalized['properties'][$prop_name] = [
                    'type' => strtoupper($prop_def['type'] ?? 'STRING'),
                    'description' => $prop_def['description'] ?? '',
                ];

                // Add enum if present
                if (isset($prop_def['enum'])) {
                    $normalized['properties'][$prop_name]['enum'] = $prop_def['enum'];
                }

                // Add items for arrays
                if (isset($prop_def['items'])) {
                    $normalized['properties'][$prop_name]['items'] = [
                        'type' => strtoupper($prop_def['items']['type'] ?? 'STRING')
                    ];
                }
            }
        }

        if (isset($parameters['required'])) {
            $normalized['required'] = $parameters['required'];
        }

        return $normalized;
    }
    
    /**
     * Send HTTP request
     */
    private function send_http_request($url, $payload, $api_key) {
        // Validate payload
        if (!is_array($payload)) {
            error_log("LexAI API Error: Payload is not an array. Type: " . gettype($payload) . ", Value: " . var_export($payload, true));
            throw new Exception("Invalid payload type: expected array, got " . gettype($payload));
        }

        $headers = array(
            'Content-Type' => 'application/json',
            'x-goog-api-key' => $api_key
        );

        $json_payload = json_encode($payload);
        if ($json_payload === false) {
            error_log("LexAI API Error: Failed to encode payload to JSON. Payload: " . var_export($payload, true));
            throw new Exception("Failed to encode payload to JSON");
        }

        $args = array(
            'method' => 'POST',
            'headers' => $headers,
            'body' => $json_payload,
            'timeout' => 60,
            'sslverify' => true
        );

        return wp_remote_request($url, $args);
    }
    
    /**
     * Send streaming request
     */
    private function send_streaming_request($url, $payload, $api_key) {
        $headers = array(
            'Content-Type' => 'application/json',
            'x-goog-api-key' => $api_key,
            'Accept' => 'text/event-stream'
        );
        
        $args = array(
            'method' => 'POST',
            'headers' => $headers,
            'body' => json_encode($payload),
            'timeout' => 120,
            'sslverify' => true,
            'stream' => true
        );
        
        return wp_remote_request($url, $args);
    }
    
    /**
     * Parse Gemini API response - CORRECTED according to Gemini documentation
     */
    private function parse_gemini_response($response) {
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception(__('Error al decodificar la respuesta de la API', 'lexai'));
        }
        
        if (isset($data['error'])) {
            throw new Exception($data['error']['message'] ?? __('Error desconocido de la API', 'lexai'));
        }
        
        if (!isset($data['candidates'][0]['content']['parts'])) {
            throw new Exception(__('Respuesta inválida de la API - no hay partes de contenido', 'lexai'));
        }
        
        $parts = $data['candidates'][0]['content']['parts'];
        $content = '';
        $function_calls = array();
        
        // Process all parts according to Gemini documentation
        foreach ($parts as $part) {
            if (isset($part['text'])) {
                $content .= $part['text'];
            } elseif (isset($part['functionCall'])) {
                // Function call format according to Gemini docs
                $function_calls[] = array(
                    'functionCall' => $part['functionCall']
                );
            }
        }
        
        return array(
            'content' => $content,
            'function_calls' => $function_calls,
            'metadata' => $data['candidates'][0]['groundingMetadata'] ?? null,
            'usage' => $data['usageMetadata'] ?? null
        );
    }
    
    /**
     * Handle API errors - LEX-LOGIC-002: Improved error handling
     */
    private function handle_api_error($api_key_id, $error_message, $response_code = null) {
        global $wpdb;
        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;

        $disable_key = false;
        $error_type = 'transient'; // 'transient' o 'permanent'

        // Errores que indican que la clave es permanentemente inválida
        $permanent_error_strings = ['API key not valid', 'permission denied', 'API_KEY_INVALID'];
        
        // Errores de cuota o límite
        $quota_error_strings = ['quota', 'rate limit', 'userRateLimitExceeded'];

        // Analizar el código de respuesta HTTP
        if ($response_code) {
            if ($response_code === 401 || $response_code === 403) {
                $disable_key = true;
                $error_type = 'permanent';
            } elseif ($response_code === 429) {
                $disable_key = true;
                $error_type = 'quota';
            }
        }

        // Analizar el mensaje de error
        foreach ($permanent_error_strings as $str) {
            if (stripos($error_message, $str) !== false) {
                $disable_key = true;
                $error_type = 'permanent';
                break;
            }
        }
        if (!$disable_key) {
            foreach ($quota_error_strings as $str) {
                if (stripos($error_message, $str) !== false) {
                    $disable_key = true;
                    $error_type = 'quota';
                    break;
                }
            }
        }

        if ($disable_key) {
            $wpdb->update(
                $api_keys_table,
                array('status' => 'error'),
                array('id' => $api_key_id),
                array('%s'),
                array('%d')
            );
            error_log("LexAI: API Key ID $api_key_id disabled due to a $error_type error.");
        }
        
        error_log("LexAI API Error (Key ID: $api_key_id, Code: $response_code): $error_message");
    }
    
    /**
     * Test API key
     */
    public function test_api_key($api_key) {
        $url = self::GEMINI_API_BASE . self::DEFAULT_MODEL . ':generateContent';
        
        $payload = array(
            'contents' => array(
                array(
                    'parts' => array(
                        array('text' => 'Test message')
                    )
                )
            )
        );
        
        $headers = array(
            'Content-Type' => 'application/json',
            'x-goog-api-key' => $api_key
        );
        
        $args = array(
            'method' => 'POST',
            'headers' => $headers,
            'body' => json_encode($payload),
            'timeout' => 30,
            'sslverify' => true
        );
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            return array('success' => false, 'message' => $response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            return array('success' => true, 'message' => __('Clave API válida', 'lexai'));
        } else {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            $error_message = $data['error']['message'] ?? __('Error desconocido', 'lexai');
            
            return array('success' => false, 'message' => $error_message);
        }
    }
    
    /**
     * Add API key to pool
     */
    public function add_api_key($name, $api_key, $provider = 'gemini') {
        global $wpdb;

        // Test the API key first (only for Gemini)
        if ($provider === 'gemini') {
            $test_result = $this->test_api_key($api_key);

            if (!$test_result['success']) {
                return $test_result;
            }
        }
        
        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;
        
        $encrypted_key = $this->encrypt_api_key($api_key);

        $result = $wpdb->insert(
            $api_keys_table,
            array(
                'name' => $name,
                'encrypted_api_key' => $encrypted_key,
                'provider' => $provider,
                'status' => 'active'
            ),
            array('%s', '%s', '%s', '%s')
        );
        
        if ($result) {
            return array('success' => true, 'message' => __('Clave API agregada exitosamente', 'lexai'));
        } else {
            return array('success' => false, 'message' => __('Error al guardar la clave API', 'lexai'));
        }
    }
    
    /**
     * Get all API keys
     */
    public function get_api_keys() {
        global $wpdb;
        
        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;
        
        return $wpdb->get_results(
            "SELECT id, name, provider, status, usage_count, last_used, created_at 
             FROM $api_keys_table 
             ORDER BY created_at DESC"
        );
    }
    
    /**
     * Delete API key
     */
    public function delete_api_key($api_key_id) {
        global $wpdb;
        
        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;
        
        return $wpdb->delete(
            $api_keys_table,
            array('id' => $api_key_id),
            array('%d')
        );
    }

    /**
     * Circuit Breaker: Check if circuit is open
     */
    private function is_circuit_open() {
        $state = get_transient('lexai_circuit_breaker_state');
        return $state === 'open';
    }

    /**
     * Circuit Breaker: Check if circuit is half-open
     */
    private function is_circuit_half_open() {
        $state = get_transient('lexai_circuit_breaker_state');
        return $state === 'half-open';
    }

    /**
     * Circuit Breaker: Record API call success
     */
    private function record_success() {
        $current_state = get_transient('lexai_circuit_breaker_state');
        $successes = intval(get_transient('lexai_circuit_breaker_successes'));

        // Reset failure count
        set_transient('lexai_circuit_breaker_failures', 0, HOUR_IN_SECONDS);

        if ($current_state === 'half-open') {
            $successes++;
            set_transient('lexai_circuit_breaker_successes', $successes, HOUR_IN_SECONDS);

            // Close circuit if enough successes
            if ($successes >= $this->circuit_breaker_config['success_threshold']) {
                set_transient('lexai_circuit_breaker_state', 'closed', HOUR_IN_SECONDS);
                set_transient('lexai_circuit_breaker_successes', 0, HOUR_IN_SECONDS);
                error_log('LexAI Circuit Breaker: Circuit CLOSED after ' . $successes . ' successes');
            }
        } elseif ($current_state === 'open') {
            // Transition to half-open
            set_transient('lexai_circuit_breaker_state', 'half-open', HOUR_IN_SECONDS);
            set_transient('lexai_circuit_breaker_successes', 1, HOUR_IN_SECONDS);
            error_log('LexAI Circuit Breaker: Circuit HALF-OPEN after success');
        }
    }

    /**
     * Circuit Breaker: Record API call failure
     */
    private function record_failure() {
        $failures = intval(get_transient('lexai_circuit_breaker_failures'));
        $failures++;

        set_transient('lexai_circuit_breaker_failures', $failures, HOUR_IN_SECONDS);
        set_transient('lexai_circuit_breaker_successes', 0, HOUR_IN_SECONDS);

        // Open circuit if threshold reached
        if ($failures >= $this->circuit_breaker_config['failure_threshold']) {
            set_transient('lexai_circuit_breaker_state', 'open', HOUR_IN_SECONDS);

            // Set timeout for when to try half-open
            set_transient('lexai_circuit_breaker_timeout',
                         time() + $this->circuit_breaker_config['timeout'],
                         HOUR_IN_SECONDS);

            error_log('LexAI Circuit Breaker: Circuit OPENED after ' . $failures . ' failures');
        }
    }

    /**
     * Circuit Breaker: Check if timeout has passed for half-open attempt
     */
    private function should_attempt_reset() {
        if (!$this->is_circuit_open()) {
            return false;
        }

        $timeout = intval(get_transient('lexai_circuit_breaker_timeout'));
        return time() >= $timeout;
    }

    /**
     * Circuit Breaker: Execute API call with protection
     */
    public function execute_with_circuit_breaker($api_call_function, $params = array()) {
        // Check circuit state
        if ($this->is_circuit_open()) {
            if ($this->should_attempt_reset()) {
                // Try half-open
                set_transient('lexai_circuit_breaker_state', 'half-open', HOUR_IN_SECONDS);
                error_log('LexAI Circuit Breaker: Attempting HALF-OPEN');
            } else {
                throw new Exception(__('Servicio temporalmente no disponible (Circuit Breaker abierto)', 'lexai'));
            }
        }

        try {
            // Execute the API call
            $result = call_user_func_array($api_call_function, $params);

            // Record success
            $this->record_success();

            return $result;

        } catch (Exception $e) {
            // Record failure
            $this->record_failure();

            // Re-throw the exception
            throw $e;
        }
    }

    /**
     * Get circuit breaker status for monitoring
     */
    public function get_circuit_breaker_status() {
        return array(
            'state' => get_transient('lexai_circuit_breaker_state'),
            'failures' => intval(get_transient('lexai_circuit_breaker_failures')),
            'successes' => intval(get_transient('lexai_circuit_breaker_successes')),
            'timeout' => intval(get_transient('lexai_circuit_breaker_timeout')),
            'config' => $this->circuit_breaker_config
        );
    }

    /**
     * Update API key status
     */
    public function update_api_key_status($api_key_id, $status) {
        global $wpdb;
        
        $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;
        
        return $wpdb->update(
            $api_keys_table,
            array('status' => $status),
            array('id' => $api_key_id),
            array('%s'),
            array('%d')
        );
    }

    /**
     * Generate cache key for API requests
     */
    private function generate_cache_key($messages, $system_instruction, $model) {
        $cache_data = array(
            'messages' => $messages,
            'system_instruction' => $system_instruction,
            'model' => $model
        );

        return 'lexai_api_cache_' . md5(json_encode($cache_data));
    }

    /**
     * Get cached response
     */
    private function get_cached_response($cache_key) {
        return get_transient($cache_key);
    }

    /**
     * Cache API response
     */
    private function cache_response($cache_key, $response) {
        // Cache for 1 hour
        set_transient($cache_key, $response, HOUR_IN_SECONDS);
    }
}