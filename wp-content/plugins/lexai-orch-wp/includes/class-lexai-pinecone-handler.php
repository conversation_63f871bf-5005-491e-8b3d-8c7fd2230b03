<?php
/**
 * Pinecone Vector Database Handler
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Pinecone Handler Class
 */
class LexAI_Pinecone_Handler {
    
    /**
     * Pinecone API base URL
     */
    private $api_base_url;
    
    /**
     * API Handler instance for unified key management
     */
    private $api_handler;

    /**
     * API key
     */
    private $api_key;

    /**
     * Index name
     */
    private $index_name;
    
    /**
     * Constructor
     */
    public function __construct() {
        // CRITICAL SECURITY FIX: Use unified API key management system
        $this->api_handler = new LexAI_API_Handler();

        // Try to get Pinecone API key from secure unified storage first
        try {
            $pinecone_key_obj = $this->api_handler->get_active_api_key('pinecone');
            if ($pinecone_key_obj) {
                $this->api_key = $pinecone_key_obj->api_key;
                error_log('LexAI Pinecone: Using unified API key management');
            } else {
                // Fallback to legacy storage for migration
                $this->migrate_legacy_api_key();
            }
        } catch (Exception $e) {
            error_log('LexAI Pinecone: Error accessing unified API key storage: ' . $e->getMessage());
            // Fallback to legacy storage
            $this->migrate_legacy_api_key();
        }

        // Get other settings from wp_options (non-sensitive data)
        $settings = get_option('lexai_settings', array());
        $pinecone_settings = $settings['pinecone'] ?? array();

        $environment = $pinecone_settings['environment'] ?? '';
        $this->index_name = $pinecone_settings['index_name'] ?? 'lexai-knowledge-base';

        // Updated Pinecone URL format
        if ($environment) {
            // Check if it's the new serverless format (contains dots)
            if (strpos($environment, '.') !== false) {
                // Serverless format: https://index-name-project-id.svc.environment.pinecone.io
                $this->api_base_url = "https://{$this->index_name}.svc.{$environment}.pinecone.io";
            } else {
                // Legacy pod-based format
                $this->api_base_url = "https://{$this->index_name}-{$environment}.svc.pinecone.io";
            }
        }
    }

    /**
     * Migrate legacy API key to unified system
     */
    private function migrate_legacy_api_key() {
        $settings = get_option('lexai_settings', array());
        $pinecone_settings = $settings['pinecone'] ?? array();

        // Try to decrypt legacy API key
        $encrypted_api_key = $pinecone_settings['api_key'] ?? '';
        $this->api_key = '';

        if (!empty($encrypted_api_key)) {
            try {
                $this->api_key = LexAI_API_Handler::decrypt_api_key_static($encrypted_api_key);

                // Migrate to unified system
                if (!empty($this->api_key)) {
                    $this->api_handler->save_api_key('pinecone', $this->api_key, 'Pinecone API Key (migrated)');

                    // Remove from legacy storage
                    unset($settings['pinecone']['api_key']);
                    update_option('lexai_settings', $settings);

                    error_log('LexAI Pinecone: Successfully migrated API key to unified system');
                }
            } catch (Exception $e) {
                error_log('LexAI Pinecone: Error migrating legacy API key: ' . $e->getMessage());
                // Fallback: assume it's not encrypted (for backward compatibility)
                $this->api_key = $encrypted_api_key;
            }
        }
    }
    
    /**
     * Check if Pinecone is configured
     */
    public function is_configured() {
        return !empty($this->api_key) && !empty($this->api_base_url);
    }
    
    /**
     * Optimized embedding configuration for legal documents
     */
    const EMBEDDING_CONFIG = array(
        'model' => 'text-embedding-004',
        'dimensions' => 768,
        'task_type' => 'RETRIEVAL_DOCUMENT',
        'metric' => 'cosine',
        'max_tokens' => 2048
    );

    /**
     * Generate embeddings using Gemini - OPTIMIZED for legal documents
     */
    public function generate_embeddings($text, $task_type = 'RETRIEVAL_DOCUMENT') {
        if (!$this->is_configured()) {
            throw new Exception(__('Pinecone no está configurado', 'lexai'));
        }

        // Validate input
        if (empty(trim($text))) {
            throw new Exception(__('El texto para generar embeddings no puede estar vacío', 'lexai'));
        }

        // Validate task type
        $valid_task_types = array('RETRIEVAL_DOCUMENT', 'RETRIEVAL_QUERY', 'SEMANTIC_SIMILARITY', 'CLASSIFICATION', 'CLUSTERING');
        if (!in_array($task_type, $valid_task_types)) {
            error_log("LexAI: Invalid task_type '{$task_type}', falling back to RETRIEVAL_DOCUMENT");
            $task_type = 'RETRIEVAL_DOCUMENT';
        }

        // Use optimized embedding configuration
        $model = self::EMBEDDING_CONFIG['model'];

        // Use Gemini's embedding model with optimized configuration
        $api_handler = new LexAI_API_Handler();
        $api_key = $api_handler->get_available_api_key();

        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:embedContent";

        // CRITICAL FIX: Dynamic payload based on task type for optimal RAG performance
        $payload = array(
            'model' => "models/{$model}",
            'content' => array(
                'parts' => array(
                    array('text' => $text)
                )
            ),
            'taskType' => $task_type, // Dynamic task type for better performance
            'outputDimensionality' => self::EMBEDDING_CONFIG['dimensions']
        );

        $headers = array(
            'Content-Type' => 'application/json',
            'x-goog-api-key' => $api_key->api_key
        );

        $args = array(
            'method' => 'POST',
            'headers' => $headers,
            'body' => json_encode($payload),
            'timeout' => 30,
            'sslverify' => true
        );

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            throw new Exception($response->get_error_message());
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if ($status_code !== 200 || isset($data['error'])) {
            $error_message = $data['error']['message'] ?? 'Error generating embeddings';
            throw new Exception($error_message);
        }

        $embedding = $data['embedding']['values'] ?? null;
        
        // Validate embedding dimensions
        if ($embedding && count($embedding) !== self::EMBEDDING_CONFIG['dimensions']) {
            error_log("LexAI: Warning - Embedding dimension mismatch. Expected: " . self::EMBEDDING_CONFIG['dimensions'] . ", Got: " . count($embedding));
        }

        return $embedding;
    }
    
    /**
     * Rate limiting configuration
     */
    private $rate_limit_config = array(
        'max_retries' => 5,
        'base_delay' => 1, // seconds
        'max_delay' => 60, // seconds
        'backoff_multiplier' => 2
    );

    /**
     * Upsert vectors to Pinecone with retry logic and rate limiting
     */
    public function upsert_vectors($vectors) {
        if (!$this->is_configured()) {
            throw new Exception(__('Pinecone no está configurado', 'lexai'));
        }

        // Optimize batch size according to Pinecone limits
        $optimized_batches = $this->optimize_batch_size($vectors);
        
        foreach ($optimized_batches as $batch) {
            $this->upsert_batch_with_retry($batch);
        }
        
        return true;
    }

    /**
     * Optimize batch size according to Pinecone official limits
     */
    private function optimize_batch_size($vectors) {
        $max_records = 1000; // Pinecone official limit
        $max_size_bytes = 2 * 1024 * 1024; // 2MB official limit
        
        $batches = array();
        $current_batch = array();
        $current_size = 0;
        
        foreach ($vectors as $vector) {
            $vector_size = strlen(json_encode($vector));
            
            // Check if adding this vector would exceed limits
            if (count($current_batch) >= $max_records || 
                ($current_size + $vector_size) > $max_size_bytes) {
                
                if (!empty($current_batch)) {
                    $batches[] = $current_batch;
                    $current_batch = array();
                    $current_size = 0;
                }
            }
            
            $current_batch[] = $vector;
            $current_size += $vector_size;
        }
        
        // Add remaining vectors
        if (!empty($current_batch)) {
            $batches[] = $current_batch;
        }
        
        return $batches;
    }

    /**
     * Upsert single batch with exponential backoff retry
     */
    private function upsert_batch_with_retry($vectors) {
        $retry_count = 0;
        $max_retries = $this->rate_limit_config['max_retries'];
        
        while ($retry_count <= $max_retries) {
            try {
                return $this->execute_upsert_request($vectors);
                
            } catch (Exception $e) {
                $error_message = $e->getMessage();
                $status_code = $this->extract_status_code($error_message);
                
                // Check if it's a rate limit error
                if ($this->is_rate_limit_error($status_code, $error_message)) {
                    $retry_count++;
                    
                    if ($retry_count > $max_retries) {
                        throw new Exception("Rate limit exceeded after {$max_retries} retries: " . $error_message);
                    }
                    
                    $delay = $this->calculate_backoff_delay($retry_count);
                    
                    error_log("LexAI Pinecone: Rate limit hit, retrying in {$delay} seconds (attempt {$retry_count}/{$max_retries})");
                    
                    // Use WordPress wp_schedule_single_event for non-blocking delay in production
                    // For immediate retry, use sleep (not recommended for web requests)
                    if (defined('WP_CLI') && WP_CLI) {
                        sleep($delay);
                    } else {
                        // In web context, throw exception to be handled by queue system
                        throw new Exception("Rate limit hit, retry needed in {$delay} seconds");
                    }
                    
                } else {
                    // Non-rate-limit error, don't retry
                    throw $e;
                }
            }
        }
    }

    /**
     * Execute the actual upsert request
     */
    private function execute_upsert_request($vectors) {
        $url = $this->api_base_url . '/vectors/upsert';
        
        $payload = array(
            'vectors' => $vectors
        );
        
        $headers = array(
            'Content-Type' => 'application/json',
            'Api-Key' => $this->api_key
        );
        
        $args = array(
            'method' => 'POST',
            'headers' => $headers,
            'body' => json_encode($payload),
            'timeout' => 60,
            'sslverify' => true
        );
        
        $start_time = microtime(true);
        $response = wp_remote_request($url, $args);
        $response_time = microtime(true) - $start_time;
        
        // Track usage metrics
        $this->track_usage('upsert', count($vectors), $response_time);
        
        if (is_wp_error($response)) {
            throw new Exception($response->get_error_message());
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code !== 200) {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            $error_message = $data['message'] ?? __('Error desconocido al insertar vectores', 'lexai');
            throw new Exception("HTTP {$status_code}: " . $error_message);
        }
        
        return true;
    }

    /**
     * Check if error is rate limit related
     */
    private function is_rate_limit_error($status_code, $error_message) {
        $rate_limit_indicators = array(
            '429',
            'TOO_MANY_REQUESTS',
            'rate limit',
            'quota exceeded',
            'throttled'
        );
        
        foreach ($rate_limit_indicators as $indicator) {
            if (stripos($error_message, $indicator) !== false || $status_code == 429) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Calculate exponential backoff delay
     */
    private function calculate_backoff_delay($retry_count) {
        $base_delay = $this->rate_limit_config['base_delay'];
        $multiplier = $this->rate_limit_config['backoff_multiplier'];
        $max_delay = $this->rate_limit_config['max_delay'];
        
        $delay = $base_delay * pow($multiplier, $retry_count - 1);
        
        // Add jitter to prevent thundering herd
        $jitter = rand(0, 1000) / 1000; // 0-1 second
        $delay += $jitter;
        
        return min($delay, $max_delay);
    }

    /**
     * Extract status code from error message
     */
    private function extract_status_code($error_message) {
        if (preg_match('/HTTP (\d+):/', $error_message, $matches)) {
            return intval($matches[1]);
        }
        return null;
    }

    /**
     * PARALLEL QUERIES - Execute multiple queries simultaneously
     */
    public function parallel_search($queries, $top_k = 5, $filters = null) {
        if (!$this->is_configured()) {
            throw new Exception(__('Pinecone no está configurado', 'lexai'));
        }

        if (empty($queries)) {
            return array();
        }

        // Generate embeddings for all queries first
        $query_vectors = array();
        foreach ($queries as $index => $query) {
            try {
                $query_vectors[$index] = $this->generate_embeddings_cached($query);
            } catch (Exception $e) {
                error_log("LexAI: Error generating embedding for query {$index}: " . $e->getMessage());
                $query_vectors[$index] = null;
            }
        }

        // Execute parallel queries using cURL multi-handle
        return $this->execute_parallel_queries($query_vectors, $top_k, $filters);
    }

    /**
     * Execute parallel queries using cURL multi-handle
     */
    private function execute_parallel_queries($query_vectors, $top_k, $filters) {
        $multi_handle = curl_multi_init();
        $curl_handles = array();
        $results = array();

        // Prepare all cURL handles
        foreach ($query_vectors as $index => $vector) {
            if ($vector === null) {
                $results[$index] = array('error' => 'Failed to generate embedding');
                continue;
            }

            $ch = curl_init();
            
            $payload = array(
                'vector' => $vector,
                'topK' => $top_k,
                'includeMetadata' => true,
                'includeValues' => false
            );

            if ($filters && isset($filters[$index])) {
                $payload['filter'] = $filters[$index];
            } elseif ($filters && !is_array($filters[0])) {
                // Single filter for all queries
                $payload['filter'] = $filters;
            }

            curl_setopt_array($ch, array(
                CURLOPT_URL => $this->api_base_url . '/query',
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => json_encode($payload),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Api-Key: ' . $this->api_key
                ),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_SSL_VERIFYPEER => true
            ));

            curl_multi_add_handle($multi_handle, $ch);
            $curl_handles[$index] = $ch;
        }

        // Execute all queries in parallel
        $running = null;
        do {
            $status = curl_multi_exec($multi_handle, $running);
            if ($running > 0) {
                curl_multi_select($multi_handle);
            }
        } while ($running > 0 && $status === CURLM_OK);

        // Collect results
        foreach ($curl_handles as $index => $ch) {
            $response = curl_multi_getcontent($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if ($response && $http_code === 200) {
                $data = json_decode($response, true);
                $results[$index] = $data['matches'] ?? array();
            } else {
                $error = curl_error($ch) ?: "HTTP {$http_code}";
                $results[$index] = array('error' => $error);
                error_log("LexAI Parallel Query Error for index {$index}: {$error}");
            }

            curl_multi_remove_handle($multi_handle, $ch);
            curl_close($ch);
        }

        curl_multi_close($multi_handle);

        // Track usage
        $this->track_usage('parallel_query', count($query_vectors), 0);

        return $results;
    }

    /**
     * EMBEDDING CACHE SYSTEM - Cache embeddings to improve performance
     */
    private $embedding_cache = array();
    private $cache_ttl = 3600; // 1 hour
    private $max_cache_size = 1000; // Maximum cached embeddings

    /**
     * Generate embeddings with intelligent caching
     */
    public function generate_embeddings_cached($text) {
        // Create cache key
        $cache_key = 'lexai_embedding_' . md5($text);
        
        // Check memory cache first
        if (isset($this->embedding_cache[$cache_key])) {
            return $this->embedding_cache[$cache_key];
        }

        // Check WordPress transient cache
        $cached_embedding = get_transient($cache_key);
        if ($cached_embedding !== false) {
            // Store in memory cache for faster access
            $this->embedding_cache[$cache_key] = $cached_embedding;
            return $cached_embedding;
        }

        // Generate new embedding
        $embedding = $this->generate_embeddings($text);

        if ($embedding) {
            // Store in both caches
            $this->store_embedding_in_cache($cache_key, $embedding);
        }

        return $embedding;
    }

    /**
     * Store embedding in cache with size management
     */
    private function store_embedding_in_cache($cache_key, $embedding) {
        // Manage memory cache size
        if (count($this->embedding_cache) >= $this->max_cache_size) {
            // Remove oldest entries (simple FIFO)
            $this->embedding_cache = array_slice($this->embedding_cache, -($this->max_cache_size - 100), null, true);
        }

        // Store in memory cache
        $this->embedding_cache[$cache_key] = $embedding;

        // Store in WordPress transient cache
        set_transient($cache_key, $embedding, $this->cache_ttl);
    }

    /**
     * Clear embedding cache
     */
    public function clear_embedding_cache() {
        // Clear memory cache
        $this->embedding_cache = array();

        // Clear WordPress transients (this is more complex, so we'll use a prefix-based approach)
        global $wpdb;
        $wpdb->query(
            "DELETE FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_lexai_embedding_%' 
             OR option_name LIKE '_transient_timeout_lexai_embedding_%'"
        );

        return true;
    }

    /**
     * Get cache statistics
     */
    public function get_cache_stats() {
        global $wpdb;
        
        $transient_count = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_lexai_embedding_%'"
        );

        return array(
            'memory_cache_size' => count($this->embedding_cache),
            'persistent_cache_size' => intval($transient_count),
            'cache_ttl' => $this->cache_ttl,
            'max_cache_size' => $this->max_cache_size
        );
    }

    /**
     * USAGE MONITORING SYSTEM - Track and analyze Pinecone usage
     */
    private $usage_stats = array();

    /**
     * Track usage metrics for monitoring and optimization
     */
    private function track_usage($operation, $record_count = 0, $response_time = 0) {
        $usage_data = array(
            'operation' => $operation,
            'record_count' => $record_count,
            'response_time' => round($response_time, 4),
            'timestamp' => current_time('mysql'),
            'api_key_hash' => substr(md5($this->api_key), 0, 8),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        );

        // Store in memory for current session
        $this->usage_stats[] = $usage_data;

        // Log for analysis (can be disabled in production)
        if (defined('LEXAI_DEBUG') && LEXAI_DEBUG) {
            error_log('LexAI Pinecone Usage: ' . json_encode($usage_data));
        }

        // Store in database for persistent tracking
        $this->store_usage_in_database($usage_data);

        // Check for performance alerts
        $this->check_performance_alerts($usage_data);
    }

    /**
     * Store usage data in WordPress database
     */
    private function store_usage_in_database($usage_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'lexai_pinecone_usage';
        
        // Create table if it doesn't exist
        $this->create_usage_table();

        // Insert usage data
        $wpdb->insert(
            $table_name,
            array(
                'operation' => $usage_data['operation'],
                'record_count' => $usage_data['record_count'],
                'response_time' => $usage_data['response_time'],
                'timestamp' => $usage_data['timestamp'],
                'api_key_hash' => $usage_data['api_key_hash'],
                'memory_usage' => $usage_data['memory_usage'],
                'peak_memory' => $usage_data['peak_memory']
            ),
            array('%s', '%d', '%f', '%s', '%s', '%d', '%d')
        );
    }

    /**
     * Create usage tracking table
     */
    private function create_usage_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'lexai_pinecone_usage';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            operation varchar(50) NOT NULL,
            record_count int(11) DEFAULT 0,
            response_time decimal(10,4) DEFAULT 0,
            timestamp datetime NOT NULL,
            api_key_hash varchar(8) NOT NULL,
            memory_usage bigint(20) DEFAULT 0,
            peak_memory bigint(20) DEFAULT 0,
            PRIMARY KEY (id),
            KEY operation (operation),
            KEY timestamp (timestamp),
            KEY api_key_hash (api_key_hash)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Check for performance alerts
     */
    private function check_performance_alerts($usage_data) {
        $alerts = array();

        // High response time alert
        if ($usage_data['response_time'] > 5.0) {
            $alerts[] = "High response time: {$usage_data['response_time']}s for {$usage_data['operation']}";
        }

        // High memory usage alert
        if ($usage_data['memory_usage'] > 128 * 1024 * 1024) { // 128MB
            $memory_mb = round($usage_data['memory_usage'] / 1024 / 1024, 2);
            $alerts[] = "High memory usage: {$memory_mb}MB for {$usage_data['operation']}";
        }

        // Large batch size alert
        if ($usage_data['operation'] === 'upsert' && $usage_data['record_count'] > 500) {
            $alerts[] = "Large batch size: {$usage_data['record_count']} records in upsert";
        }

        // Log alerts
        foreach ($alerts as $alert) {
            error_log("LexAI Performance Alert: " . $alert);
        }
    }

    /**
     * Get usage statistics and analytics
     */
    public function get_usage_analytics($days = 7) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'lexai_pinecone_usage';
        $since_date = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        // Basic statistics
        $stats = $wpdb->get_row($wpdb->prepare(
            "SELECT 
                COUNT(*) as total_operations,
                SUM(record_count) as total_records,
                AVG(response_time) as avg_response_time,
                MAX(response_time) as max_response_time,
                AVG(memory_usage) as avg_memory_usage,
                MAX(peak_memory) as max_memory_usage
             FROM $table_name 
             WHERE timestamp >= %s",
            $since_date
        ), ARRAY_A);

        // Operations breakdown
        $operations = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                operation,
                COUNT(*) as count,
                AVG(response_time) as avg_time,
                SUM(record_count) as total_records
             FROM $table_name 
             WHERE timestamp >= %s 
             GROUP BY operation 
             ORDER BY count DESC",
            $since_date
        ), ARRAY_A);

        // Hourly distribution
        $hourly = $wpdb->get_results($wpdb->prepare(
            "SELECT 
                HOUR(timestamp) as hour,
                COUNT(*) as operations,
                AVG(response_time) as avg_time
             FROM $table_name 
             WHERE timestamp >= %s 
             GROUP BY HOUR(timestamp) 
             ORDER BY hour",
            $since_date
        ), ARRAY_A);

        return array(
            'period_days' => $days,
            'summary' => $stats,
            'operations_breakdown' => $operations,
            'hourly_distribution' => $hourly,
            'cache_stats' => $this->get_cache_stats(),
            'current_session_operations' => count($this->usage_stats)
        );
    }

    /**
     * Optimize performance based on usage patterns
     */
    public function optimize_performance() {
        $analytics = $this->get_usage_analytics(7);
        $recommendations = array();

        // Analyze response times
        if ($analytics['summary']['avg_response_time'] > 2.0) {
            $recommendations[] = array(
                'type' => 'performance',
                'issue' => 'High average response time',
                'recommendation' => 'Consider using parallel queries or reducing batch sizes',
                'priority' => 'high'
            );
        }

        // Analyze cache efficiency
        $cache_stats = $analytics['cache_stats'];
        if ($cache_stats['memory_cache_size'] < 100) {
            $recommendations[] = array(
                'type' => 'cache',
                'issue' => 'Low cache utilization',
                'recommendation' => 'Increase cache size or TTL for better performance',
                'priority' => 'medium'
            );
        }

        // Analyze operation patterns
        foreach ($analytics['operations_breakdown'] as $op) {
            if ($op['operation'] === 'upsert' && $op['avg_time'] > 3.0) {
                $recommendations[] = array(
                    'type' => 'batch_optimization',
                    'issue' => 'Slow upsert operations',
                    'recommendation' => 'Optimize batch sizes or implement retry logic',
                    'priority' => 'medium'
                );
            }
        }

        return array(
            'analytics' => $analytics,
            'recommendations' => $recommendations,
            'optimization_score' => $this->calculate_optimization_score($analytics)
        );
    }

    /**
     * Calculate optimization score (0-100)
     */
    private function calculate_optimization_score($analytics) {
        $score = 100;

        // Deduct points for performance issues
        if ($analytics['summary']['avg_response_time'] > 2.0) {
            $score -= 20;
        }

        if ($analytics['summary']['max_response_time'] > 10.0) {
            $score -= 15;
        }

        // Deduct points for cache inefficiency
        $cache_stats = $analytics['cache_stats'];
        if ($cache_stats['memory_cache_size'] < 50) {
            $score -= 10;
        }

        // Deduct points for memory issues
        if ($analytics['summary']['max_memory_usage'] > 256 * 1024 * 1024) { // 256MB
            $score -= 15;
        }

        return max(0, $score);
    }
    
    /**
     * Query vectors from Pinecone with improved error handling
     * Fixed API-PINECONE-001: Enhanced error handling and retry logic
     */
    public function query_vectors($query_vector, $top_k = 5, $filter = null) {
        if (!$this->is_configured()) {
            throw new Exception(__('Pinecone no está configurado', 'lexai'));
        }

        // Validate input parameters
        if (!is_array($query_vector) || empty($query_vector)) {
            throw new Exception(__('Vector de consulta inválido', 'lexai'));
        }

        $top_k = max(1, min(10000, intval($top_k))); // Pinecone limits

        $url = $this->api_base_url . '/query';

        $payload = array(
            'vector' => $query_vector,
            'topK' => $top_k,
            'includeMetadata' => true,
            'includeValues' => false
        );

        if ($filter) {
            $payload['filter'] = $filter;
        }

        $headers = array(
            'Content-Type' => 'application/json',
            'Api-Key' => $this->api_key,
            'X-Pinecone-API-Version' => '2024-07'
        );

        $args = array(
            'method' => 'POST',
            'headers' => $headers,
            'body' => json_encode($payload),
            'timeout' => 30,
            'sslverify' => true
        );

        // Implement retry logic with exponential backoff
        $max_retries = 3;
        $retry_count = 0;

        while ($retry_count <= $max_retries) {
            $response = wp_remote_request($url, $args);

            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();

                // Check if it's a retryable error
                if ($this->is_retryable_error($error_message) && $retry_count < $max_retries) {
                    $retry_count++;
                    $delay = pow(2, $retry_count); // Exponential backoff
                    error_log("LexAI Pinecone: Retrying query in {$delay} seconds (attempt {$retry_count}/{$max_retries})");
                    sleep($delay);
                    continue;
                }

                throw new Exception(__('Error de red al consultar vectores: ', 'lexai') . $error_message);
            }

            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            // Handle specific HTTP status codes
            if ($status_code === 429) {
                $retry_after = wp_remote_retrieve_header($response, 'retry-after') ?: 60;
                if ($retry_count < $max_retries) {
                    $retry_count++;
                    error_log("LexAI Pinecone: Rate limited, retrying in {$retry_after} seconds");
                    sleep($retry_after);
                    continue;
                }
                throw new Exception(__('Límite de velocidad de Pinecone excedido', 'lexai'));
            } elseif ($status_code === 401 || $status_code === 403) {
                throw new Exception(__('Error de autenticación con Pinecone. Verifica tu API key.', 'lexai'));
            } elseif ($status_code >= 500 && $retry_count < $max_retries) {
                $retry_count++;
                $delay = pow(2, $retry_count);
                error_log("LexAI Pinecone: Server error {$status_code}, retrying in {$delay} seconds");
                sleep($delay);
                continue;
            } elseif ($status_code !== 200) {
                $data = json_decode($body, true);
                $error_message = isset($data['error']) ? $data['error']['message'] : $body;
                throw new Exception(__('Error en la consulta de vectores: ', 'lexai') . "HTTP {$status_code}: {$error_message}");
            }

            // Success - parse response
            $data = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception(__('Respuesta JSON inválida de Pinecone', 'lexai'));
            }

            if (isset($data['error'])) {
                throw new Exception($data['error']['message']);
            }

            // Track successful query
            $this->track_usage('query', 1, count($data['matches'] ?? array()));

            return $data['matches'] ?? array();
        }

        throw new Exception(__('Consulta de vectores falló después de múltiples intentos', 'lexai'));
    }
    
    /**
     * LEX-SECURITY-004: Search legal knowledge base with secure filtering
     */
    public function search_knowledge_base($query, $top_k = 5, $user_filter = null) {
        try {
            // Validate and sanitize inputs
            $query = sanitize_text_field($query);
            $top_k = max(1, min(50, intval($top_k))); // Limit between 1-50

            if (empty($query)) {
                throw new Exception(__('La consulta no puede estar vacía', 'lexai'));
            }

            // CRITICAL FIX: Generate embedding for the query using RETRIEVAL_QUERY for optimal RAG performance
            $query_vector = $this->generate_embeddings($query, 'RETRIEVAL_QUERY');

            if (!$query_vector) {
                throw new Exception(__('No se pudo generar el embedding para la consulta', 'lexai'));
            }

            // LEX-SECURITY-004: Build secure filter with validation
            $secure_filter = $this->build_secure_filter($user_filter);

            // Query Pinecone for legal documents only
            $matches = $this->query_vectors($query_vector, $top_k, $secure_filter);

            // Format and sanitize results
            $results = array();
            foreach ($matches as $match) {
                $results[] = array(
                    'id' => sanitize_text_field($match['id']),
                    'score' => floatval($match['score']),
                    'metadata' => $this->sanitize_metadata($match['metadata'] ?? array()),
                    'content' => wp_kses_post($match['metadata']['content'] ?? ''),
                    'source' => sanitize_text_field($match['metadata']['source'] ?? ''),
                    'document_type' => sanitize_text_field($match['metadata']['document_type'] ?? 'legal'),
                    'article' => sanitize_text_field($match['metadata']['article'] ?? ''),
                    'law_name' => sanitize_text_field($match['metadata']['law_name'] ?? ''),
                    'category' => sanitize_text_field($match['metadata']['category'] ?? '')
                );
            }

            return $results;

        } catch (Exception $e) {
            error_log('LexAI Pinecone Search Error: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * LEX-SECURITY-004: Build secure filter with whitelist validation
     */
    private function build_secure_filter($user_filter = null) {
        // Base secure filter - always applied to ensure only legal documents
        $secure_filter = array(
            'document_type' => array('$eq' => 'legal'),
            'language' => array('$eq' => 'es')
        );

        // LEX-SECURITY-004: Whitelist of allowed filter keys and their valid values
        $allowed_filters = array(
            'jurisdiction' => array('mexico', 'federal', 'local', 'municipal', 'estatal'),
            'category' => array('civil', 'penal', 'laboral', 'fiscal', 'constitucional', 'administrativo', 'mercantil', 'familiar'),
            'source' => array('codigo_civil', 'codigo_penal', 'ley_federal_trabajo', 'constitucion', 'scjn', 'codigo_comercio'),
            'document_subtype' => array('ley', 'reglamento', 'jurisprudencia', 'tesis', 'criterio', 'acuerdo'),
            'tags' => array('contrato', 'responsabilidad', 'derechos', 'obligaciones', 'procedimiento')
        );

        // LEX-SECURITY-004: Process user filters securely
        if (is_array($user_filter)) {
            foreach ($user_filter as $key => $value) {
                // Sanitize key
                $key = sanitize_key($key);

                // Check if key is in whitelist
                if (!array_key_exists($key, $allowed_filters)) {
                    error_log("LexAI Security: Unauthorized filter key attempted: {$key}");
                    continue; // Skip unauthorized filters
                }

                // Extract and sanitize value
                if (is_array($value) && isset($value['$eq'])) {
                    $filter_value = sanitize_text_field($value['$eq']);
                } elseif (is_string($value)) {
                    $filter_value = sanitize_text_field($value);
                } else {
                    error_log("LexAI Security: Invalid filter value type for key: {$key}");
                    continue; // Skip invalid value types
                }

                // Validate value against whitelist
                if (in_array($filter_value, $allowed_filters[$key], true)) {
                    $secure_filter[$key] = array('$eq' => $filter_value);
                } else {
                    error_log("LexAI Security: Unauthorized filter value attempted: {$key} = {$filter_value}");
                }
            }
        }

        return $secure_filter;
    }

    /**
     * Sanitize metadata array
     */
    private function sanitize_metadata($metadata) {
        if (!is_array($metadata)) {
            return array();
        }

        $sanitized = array();
        foreach ($metadata as $key => $value) {
            $key = sanitize_key($key);

            if (is_string($value)) {
                $sanitized[$key] = sanitize_text_field($value);
            } elseif (is_numeric($value)) {
                $sanitized[$key] = floatval($value);
            } elseif (is_bool($value)) {
                $sanitized[$key] = (bool) $value;
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitize_metadata($value); // Recursive
            }
        }

        return $sanitized;
    }
    
    /**
     * Process and upload document with optimized metadata structure
     */
    public function process_document($file_path, $metadata = array()) {
        if (!$this->is_configured()) {
            throw new Exception(__('Pinecone no está configurado', 'lexai'));
        }
        
        // Extract text from document
        $text_content = $this->extract_text_from_file($file_path);
        
        if (empty($text_content)) {
            throw new Exception(__('No se pudo extraer texto del documento', 'lexai'));
        }
        
        // Split text into chunks
        $chunks = $this->split_text_into_chunks($text_content);
        
        // Generate vectors for each chunk
        $vectors = array();
        $document_id = $this->generate_structured_document_id($metadata);
        
        foreach ($chunks as $index => $chunk) {
            try {
                $embedding = $this->generate_embeddings($chunk);
                
                if ($embedding) {
                    // Create optimized metadata structure following Pinecone best practices
                    $optimized_metadata = $this->create_optimized_metadata($chunk, $document_id, $index, $metadata);
                    
                    $vectors[] = array(
                        'id' => $this->generate_structured_chunk_id($document_id, $index),
                        'values' => $embedding,
                        'metadata' => $optimized_metadata
                    );
                }
                
                // Process in batches to avoid memory issues
                if (count($vectors) >= 100) {
                    $this->upsert_vectors($vectors);
                    $vectors = array();
                }
                
            } catch (Exception $e) {
                error_log('Error processing chunk ' . $index . ': ' . $e->getMessage());
                continue;
            }
        }
        
        // Upload remaining vectors
        if (!empty($vectors)) {
            $this->upsert_vectors($vectors);
        }
        
        return $document_id;
    }
    
    /**
     * Extract text from file
     */
    private function extract_text_from_file($file_path) {
        $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
        
        switch ($file_extension) {
            case 'txt':
                return file_get_contents($file_path);
                
            case 'pdf':
                return $this->extract_text_from_pdf($file_path);
                
            case 'docx':
                return $this->extract_text_from_docx($file_path);
                
            default:
                throw new Exception(__('Tipo de archivo no soportado', 'lexai'));
        }
    }
    
    /**
     * Extract text from PDF
     */
    private function extract_text_from_pdf($file_path) {
        // Dependency: composer require smalot/pdfparser
        if (!class_exists('\\Smalot\\PdfParser\\Parser')) {
            throw new Exception('La librería PdfParser no está instalada. Ejecuta `composer require smalot/pdfparser`.');
        }

        try {
            $parser = new \Smalot\PdfParser\Parser();
            $pdf = $parser->parseFile($file_path);
            $text = $pdf->getText();

            // Clean and normalize text
            $text = preg_replace('/\s+/', ' ', $text);
            $text = trim($text);

            if (empty($text)) {
                throw new Exception('No se pudo extraer texto del PDF');
            }

            return $text;

        } catch (Exception $e) {
            error_log('LexAI PDF extraction error: ' . $e->getMessage());
            throw new Exception('Error al extraer texto del PDF: ' . $e->getMessage());
        }
    }

    /**
     * Extract text from DOCX
     */
    private function extract_text_from_docx($file_path) {
        // Dependency: composer require phpoffice/phpword
        if (!class_exists('\\PhpOffice\\PhpWord\\IOFactory')) {
            throw new Exception('La librería PhpWord no está instalada. Ejecuta `composer require phpoffice/phpword`.');
        }

        try {
            $phpWord = \PhpOffice\PhpWord\IOFactory::load($file_path);
            $text = '';

            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    if (method_exists($element, 'getText')) {
                        $text .= $element->getText() . "\n";
                    } elseif (method_exists($element, 'getElements')) {
                        // Handle nested elements like tables
                        $text .= $this->extract_nested_docx_elements($element) . "\n";
                    }
                }
            }

            // Clean and normalize text
            $text = preg_replace('/\s+/', ' ', $text);
            $text = trim($text);

            if (empty($text)) {
                throw new Exception('No se pudo extraer texto del DOCX');
            }

            return $text;

        } catch (Exception $e) {
            error_log('LexAI DOCX extraction error: ' . $e->getMessage());
            throw new Exception('Error al extraer texto del DOCX: ' . $e->getMessage());
        }
    }

    /**
     * Extract text from nested DOCX elements
     */
    private function extract_nested_docx_elements($element) {
        $text = '';

        if (method_exists($element, 'getElements')) {
            foreach ($element->getElements() as $nested_element) {
                if (method_exists($nested_element, 'getText')) {
                    $text .= $nested_element->getText() . ' ';
                } elseif (method_exists($nested_element, 'getElements')) {
                    $text .= $this->extract_nested_docx_elements($nested_element) . ' ';
                }
            }
        }

        return $text;
    }
    
    /**
     * Split text into chunks
     */
    private function split_text_into_chunks($text, $chunk_size = 1000, $overlap = 200) {
        $chunks = array();
        $text_length = strlen($text);
        
        for ($i = 0; $i < $text_length; $i += ($chunk_size - $overlap)) {
            $chunk = substr($text, $i, $chunk_size);
            
            // Try to break at sentence boundaries
            if ($i + $chunk_size < $text_length) {
                $last_period = strrpos($chunk, '.');
                $last_question = strrpos($chunk, '?');
                $last_exclamation = strrpos($chunk, '!');
                
                $break_point = max($last_period, $last_question, $last_exclamation);
                
                if ($break_point !== false && $break_point > $chunk_size * 0.7) {
                    $chunk = substr($chunk, 0, $break_point + 1);
                }
            }
            
            $chunks[] = trim($chunk);
        }
        
        return array_filter($chunks, function($chunk) {
            return strlen(trim($chunk)) > 50; // Only keep chunks with meaningful content
        });
    }
    
    /**
     * Test Pinecone connection and validate index configuration
     */
    public function test_connection() {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => __('Pinecone no está configurado', 'lexai'));
        }
        
        try {
            // First, validate index configuration
            $validation_result = $this->validate_index_configuration();
            if (!$validation_result['success']) {
                return $validation_result;
            }
            
            // Try to query with a simple test vector
            $test_vector = array_fill(0, self::EMBEDDING_CONFIG['dimensions'], 0.1);
            $this->query_vectors($test_vector, 1);
            
            return array(
                'success' => true, 
                'message' => __('Conexión exitosa con Pinecone', 'lexai'),
                'index_config' => $validation_result['config']
            );
            
        } catch (Exception $e) {
            return array('success' => false, 'message' => $e->getMessage());
        }
    }

    /**
     * Validate Pinecone index configuration
     */
    public function validate_index_configuration() {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => __('Pinecone no está configurado', 'lexai'));
        }

        try {
            $stats = $this->get_index_stats();
            
            if (!$stats['success']) {
                return array('success' => false, 'message' => 'No se pudieron obtener las estadísticas del índice');
            }

            $expected_dimension = self::EMBEDDING_CONFIG['dimensions'];
            $actual_dimension = $stats['dimension'];

            // Validate dimensions
            if ($actual_dimension !== $expected_dimension) {
                return array(
                    'success' => false,
                    'message' => "Configuración de dimensiones incorrecta. Esperado: {$expected_dimension}, Actual: {$actual_dimension}",
                    'config' => array(
                        'expected_dimension' => $expected_dimension,
                        'actual_dimension' => $actual_dimension,
                        'metric' => self::EMBEDDING_CONFIG['metric']
                    )
                );
            }

            return array(
                'success' => true,
                'message' => 'Configuración del índice validada correctamente',
                'config' => array(
                    'dimension' => $actual_dimension,
                    'metric' => self::EMBEDDING_CONFIG['metric'],
                    'total_vectors' => $stats['total_vector_count'],
                    'index_fullness' => $stats['index_fullness']
                )
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Error validando configuración: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * Delete vectors by filter
     */
    public function delete_vectors($filter) {
        if (!$this->is_configured()) {
            throw new Exception(__('Pinecone no está configurado', 'lexai'));
        }
        
        $url = $this->api_base_url . '/vectors/delete';
        
        $payload = array(
            'filter' => $filter
        );
        
        $headers = array(
            'Content-Type' => 'application/json',
            'Api-Key' => $this->api_key
        );
        
        $args = array(
            'method' => 'POST',
            'headers' => $headers,
            'body' => json_encode($payload),
            'timeout' => 30,
            'sslverify' => true
        );
        
        $response = wp_remote_request($url, $args);
        
        if (is_wp_error($response)) {
            throw new Exception($response->get_error_message());
        }
        
        return true;
    }

    /**
     * Index legal document for RAG
     */
    public function index_legal_document($content, $metadata = array()) {
        try {
            if (!$this->is_configured()) {
                throw new Exception(__('Pinecone no está configurado', 'lexai'));
            }

            // Split content into chunks for better retrieval
            $chunks = $this->split_legal_text($content);

            $vectors = array();
            foreach ($chunks as $index => $chunk) {
                // Generate embedding for the chunk
                $vector = $this->generate_embeddings($chunk);

                if (!$vector) {
                    continue;
                }

                // Prepare metadata for legal document
                $document_metadata = array_merge(array(
                    'content' => $chunk,
                    'document_type' => 'legal',
                    'language' => 'es',
                    'jurisdiction' => 'mexico',
                    'indexed_at' => current_time('mysql'),
                    'chunk_index' => $index,
                    'source' => 'legal_database'
                ), $metadata);

                // Generate unique ID for the chunk
                $chunk_id = 'legal_' . md5($content) . '_chunk_' . $index;

                $vectors[] = array(
                    'id' => $chunk_id,
                    'values' => $vector,
                    'metadata' => $document_metadata
                );
            }

            // Upsert vectors to Pinecone
            if (!empty($vectors)) {
                return $this->upsert_vectors($vectors);
            }

            return false;

        } catch (Exception $e) {
            error_log('LexAI Pinecone Index Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Split legal text into meaningful chunks
     */
    private function split_legal_text($text, $max_chunk_size = 1000) {
        $chunks = array();

        // First, try to split by articles or sections
        $article_pattern = '/(?=Art[íi]culo\s+\d+|Secci[óo]n\s+\d+|Cap[íi]tulo\s+\d+)/i';
        $sections = preg_split($article_pattern, $text, -1, PREG_SPLIT_NO_EMPTY);

        foreach ($sections as $section) {
            $section = trim($section);
            if (empty($section)) continue;

            // If section is too long, split by sentences
            if (strlen($section) > $max_chunk_size) {
                $sentences = preg_split('/(?<=[.!?])\s+/', $section);
                $current_chunk = '';

                foreach ($sentences as $sentence) {
                    if (strlen($current_chunk . $sentence) > $max_chunk_size && !empty($current_chunk)) {
                        $chunks[] = trim($current_chunk);
                        $current_chunk = $sentence;
                    } else {
                        $current_chunk .= ' ' . $sentence;
                    }
                }

                if (!empty($current_chunk)) {
                    $chunks[] = trim($current_chunk);
                }
            } else {
                $chunks[] = $section;
            }
        }

        return array_filter($chunks, function($chunk) {
            return strlen(trim($chunk)) > 50; // Only keep meaningful chunks
        });
    }

    /**
     * Bulk index legal documents
     */
    public function bulk_index_legal_documents($documents) {
        $success_count = 0;
        $total_count = count($documents);

        foreach ($documents as $document) {
            $content = $document['content'] ?? '';
            $metadata = $document['metadata'] ?? array();

            if (empty($content)) continue;

            if ($this->index_legal_document($content, $metadata)) {
                $success_count++;
            }

            // Add small delay to avoid rate limiting
            usleep(100000); // 0.1 seconds
        }

        return array(
            'success' => $success_count,
            'total' => $total_count,
            'success_rate' => $total_count > 0 ? ($success_count / $total_count) * 100 : 0
        );
    }

    /**
     * Get legal context for query
     */
    public function get_legal_context($query, $max_results = 3) {
        $results = $this->search_knowledge_base($query, $max_results);

        if (empty($results)) {
            return '';
        }

        $context = "Contexto legal relevante:\n\n";
        foreach ($results as $result) {
            $source = $result['law_name'] ?? $result['source'] ?? 'Documento legal';
            $article = $result['article'] ? " - {$result['article']}" : '';

            $context .= "**{$source}{$article}**\n";
            $context .= $result['content'] . "\n\n";
        }

        return $context;
    }

    /**
     * Get index statistics
     */
    public function get_index_stats() {
        if (!$this->is_configured()) {
            throw new Exception(__('Pinecone no está configurado', 'lexai'));
        }

        $url = $this->api_base_url . '/describe_index_stats';

        $response = wp_remote_post($url, array(
            'headers' => array(
                'Api-Key' => $this->api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode(array()),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            throw new Exception('Error de conexión con Pinecone: ' . $response->get_error_message());
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        if ($status_code !== 200) {
            $error_data = json_decode($body, true);
            $error_message = $error_data['message'] ?? 'Error desconocido';
            throw new Exception('Error de Pinecone: ' . $error_message);
        }

        $data = json_decode($body, true);

        return array(
            'success' => true,
            'total_vector_count' => $data['totalVectorCount'] ?? 0,
            'dimension' => $data['dimension'] ?? 0,
            'index_fullness' => $data['indexFullness'] ?? 0,
            'namespaces' => $data['namespaces'] ?? array()
        );
    }

    /**
     * Generate structured document ID following Pinecone best practices
     * Format: {document_type}#{source}#{document_id}
     */
    private function generate_structured_document_id($metadata = array()) {
        $document_type = $metadata['document_type'] ?? 'legal';
        $source = $metadata['source'] ?? 'unknown';
        $timestamp = time();
        $random = substr(md5(uniqid()), 0, 8);
        
        // Clean source for ID usage
        $clean_source = preg_replace('/[^a-zA-Z0-9_-]/', '_', $source);
        
        return "{$document_type}#{$clean_source}#{$timestamp}_{$random}";
    }

    /**
     * Generate structured chunk ID following Pinecone best practices
     * Format: {document_id}#chunk{chunk_number}
     */
    private function generate_structured_chunk_id($document_id, $chunk_index) {
        return "{$document_id}#chunk" . str_pad($chunk_index, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Create optimized metadata structure following Pinecone best practices
     */
    private function create_optimized_metadata($chunk_text, $document_id, $chunk_index, $original_metadata = array()) {
        // Base metadata structure following Pinecone recommendations
        $optimized_metadata = array(
            // Core content fields
            'chunk_text' => $this->truncate_text($chunk_text, 1000), // Limit for metadata size
            'document_id' => $document_id,
            'chunk_number' => $chunk_index,
            
            // Document classification
            'document_type' => $original_metadata['document_type'] ?? 'legal',
            'document_subtype' => $original_metadata['document_subtype'] ?? 'documento',
            'language' => 'es',
            'jurisdiction' => $original_metadata['jurisdiction'] ?? 'mexico',
            
            // Legal categorization
            'category' => $this->normalize_legal_category($original_metadata['category'] ?? 'general'),
            'law_area' => $this->normalize_law_area($original_metadata['law_area'] ?? 'general'),
            'source' => $this->normalize_source($original_metadata['source'] ?? 'unknown'),
            
            // Document details
            'document_title' => $this->truncate_text($original_metadata['document_title'] ?? '', 200),
            'article' => $original_metadata['article'] ?? '',
            'section' => $original_metadata['section'] ?? '',
            'chapter' => $original_metadata['chapter'] ?? '',
            
            // Temporal information
            'created_at' => current_time('Y-m-d H:i:s'),
            'document_date' => $original_metadata['document_date'] ?? '',
            'last_updated' => $original_metadata['last_updated'] ?? '',
            
            // Quality and relevance metrics
            'confidence_score' => floatval($original_metadata['confidence_score'] ?? 1.0),
            'relevance_score' => $this->calculate_relevance_score($chunk_text),
            'chunk_quality' => $this->assess_chunk_quality($chunk_text),
            
            // Searchability enhancements
            'tags' => $this->extract_legal_tags($chunk_text, $original_metadata),
            'keywords' => $this->extract_keywords($chunk_text),
            'entities' => $this->extract_legal_entities($chunk_text),
            
            // Traceability
            'document_url' => $original_metadata['document_url'] ?? '',
            'file_path' => $original_metadata['file_path'] ?? '',
            'processing_version' => '1.0',
            
            // Access control
            'access_level' => $original_metadata['access_level'] ?? 'public',
            'user_id' => $original_metadata['user_id'] ?? 0,
            'tenant_id' => $original_metadata['tenant_id'] ?? 'default'
        );

        // Add custom metadata while respecting Pinecone's 40KB limit
        if (isset($original_metadata['custom'])) {
            $optimized_metadata['custom'] = $this->sanitize_custom_metadata($original_metadata['custom']);
        }

        // Ensure metadata doesn't exceed Pinecone's 40KB limit
        return $this->enforce_metadata_size_limit($optimized_metadata);
    }

    /**
     * Normalize legal category to standard values
     */
    private function normalize_legal_category($category) {
        $category = strtolower(trim($category));
        
        $category_map = array(
            'civil' => 'civil',
            'penal' => 'penal',
            'laboral' => 'laboral',
            'fiscal' => 'fiscal',
            'constitucional' => 'constitucional',
            'administrativo' => 'administrativo',
            'mercantil' => 'mercantil',
            'familiar' => 'familiar',
            'ambiental' => 'ambiental',
            'internacional' => 'internacional'
        );

        return $category_map[$category] ?? 'general';
    }

    /**
     * Normalize law area to standard values
     */
    private function normalize_law_area($law_area) {
        $law_area = strtolower(trim($law_area));
        
        $area_map = array(
            'contratos' => 'contratos',
            'responsabilidad' => 'responsabilidad',
            'derechos_humanos' => 'derechos_humanos',
            'procedimientos' => 'procedimientos',
            'sanciones' => 'sanciones',
            'obligaciones' => 'obligaciones',
            'garantias' => 'garantias',
            'jurisdiccion' => 'jurisdiccion'
        );

        return $area_map[$law_area] ?? 'general';
    }

    /**
     * Normalize source to standard values
     */
    private function normalize_source($source) {
        $source = strtolower(trim($source));
        
        $source_map = array(
            'codigo_civil' => 'codigo_civil',
            'codigo_penal' => 'codigo_penal',
            'ley_federal_trabajo' => 'ley_federal_trabajo',
            'constitucion' => 'constitucion',
            'scjn' => 'scjn',
            'codigo_comercio' => 'codigo_comercio',
            'ley_amparo' => 'ley_amparo',
            'codigo_fiscal' => 'codigo_fiscal'
        );

        return $source_map[$source] ?? 'documento_legal';
    }

    /**
     * Calculate relevance score based on content analysis
     */
    private function calculate_relevance_score($text) {
        $score = 0.5; // Base score
        
        // Increase score for legal keywords
        $legal_keywords = array('artículo', 'ley', 'código', 'derecho', 'obligación', 'responsabilidad', 'procedimiento');
        foreach ($legal_keywords as $keyword) {
            if (stripos($text, $keyword) !== false) {
                $score += 0.1;
            }
        }
        
        // Increase score for structured content (articles, sections)
        if (preg_match('/art[íi]culo\s+\d+/i', $text)) {
            $score += 0.2;
        }
        
        // Decrease score for very short or very long chunks
        $length = strlen($text);
        if ($length < 100 || $length > 2000) {
            $score -= 0.1;
        }
        
        return max(0.0, min(1.0, $score));
    }

    /**
     * Assess chunk quality
     */
    private function assess_chunk_quality($text) {
        $quality = 'medium';
        
        $length = strlen($text);
        $word_count = str_word_count($text);
        
        // High quality: well-structured, appropriate length
        if ($length >= 200 && $length <= 1500 && $word_count >= 30) {
            if (preg_match('/art[íi]culo|secci[óo]n|cap[íi]tulo/i', $text)) {
                $quality = 'high';
            }
        }
        
        // Low quality: too short, too long, or poor structure
        if ($length < 100 || $length > 2500 || $word_count < 15) {
            $quality = 'low';
        }
        
        return $quality;
    }

    /**
     * Extract legal tags from text
     */
    private function extract_legal_tags($text, $metadata = array()) {
        $tags = array();
        
        // Extract from metadata if available
        if (isset($metadata['tags']) && is_array($metadata['tags'])) {
            $tags = array_merge($tags, $metadata['tags']);
        }
        
        // Extract from content
        $legal_patterns = array(
            'contrato' => '/contrato|convenio|acuerdo/i',
            'responsabilidad' => '/responsabilidad|culpa|negligencia/i',
            'derechos' => '/derecho|facultad|prerrogativa/i',
            'obligaciones' => '/obligaci[óo]n|deber|compromiso/i',
            'procedimiento' => '/procedimiento|tr[áa]mite|proceso/i',
            'sancion' => '/sanci[óo]n|multa|penalidad/i'
        );
        
        foreach ($legal_patterns as $tag => $pattern) {
            if (preg_match($pattern, $text)) {
                $tags[] = $tag;
            }
        }
        
        return array_unique($tags);
    }

    /**
     * Extract keywords from text
     */
    private function extract_keywords($text) {
        // Simple keyword extraction - can be enhanced with NLP
        $words = str_word_count(strtolower($text), 1, 'áéíóúñü');
        $stopwords = array('el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'como', 'pero', 'sus', 'han', 'fue', 'ser', 'está', 'todo', 'más', 'muy', 'sin', 'sobre', 'también', 'hasta', 'hay', 'donde', 'quien', 'desde', 'todos', 'durante', 'tanto', 'menos', 'según', 'entre');
        
        $keywords = array_diff($words, $stopwords);
        $word_freq = array_count_values($keywords);
        arsort($word_freq);
        
        return array_slice(array_keys($word_freq), 0, 10); // Top 10 keywords
    }

    /**
     * Extract legal entities from text
     */
    private function extract_legal_entities($text) {
        $entities = array();
        
        // Extract article references
        if (preg_match_all('/art[íi]culo\s+(\d+)/i', $text, $matches)) {
            foreach ($matches[1] as $article) {
                $entities[] = "articulo_{$article}";
            }
        }
        
        // Extract law references
        if (preg_match_all('/ley\s+([a-záéíóúñ\s]+)/i', $text, $matches)) {
            foreach ($matches[1] as $law) {
                $clean_law = preg_replace('/[^a-záéíóúñ\s]/i', '', trim($law));
                if (strlen($clean_law) > 3) {
                    $entities[] = "ley_" . str_replace(' ', '_', strtolower($clean_law));
                }
            }
        }
        
        return array_unique($entities);
    }

    /**
     * Sanitize custom metadata
     */
    private function sanitize_custom_metadata($custom_metadata) {
        if (!is_array($custom_metadata)) {
            return array();
        }
        
        $sanitized = array();
        foreach ($custom_metadata as $key => $value) {
            $key = sanitize_key($key);
            
            if (is_string($value)) {
                $sanitized[$key] = sanitize_text_field($value);
            } elseif (is_numeric($value)) {
                $sanitized[$key] = floatval($value);
            } elseif (is_bool($value)) {
                $sanitized[$key] = (bool) $value;
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitize_custom_metadata($value);
            }
        }
        
        return $sanitized;
    }

    /**
     * Enforce Pinecone's 40KB metadata size limit
     */
    private function enforce_metadata_size_limit($metadata) {
        $json_size = strlen(json_encode($metadata));
        $max_size = 40 * 1024; // 40KB
        
        if ($json_size <= $max_size) {
            return $metadata;
        }
        
        // Progressively reduce content to fit within limit
        $reductions = array(
            'chunk_text' => 500,
            'document_title' => 100,
            'keywords' => 5,
            'tags' => 5,
            'entities' => 5
        );
        
        foreach ($reductions as $field => $max_length) {
            if (isset($metadata[$field])) {
                if (is_string($metadata[$field])) {
                    $metadata[$field] = $this->truncate_text($metadata[$field], $max_length);
                } elseif (is_array($metadata[$field])) {
                    $metadata[$field] = array_slice($metadata[$field], 0, $max_length);
                }
                
                $json_size = strlen(json_encode($metadata));
                if ($json_size <= $max_size) {
                    break;
                }
            }
        }
        
        return $metadata;
    }

    /**
     * Truncate text to specified length
     */
    private function truncate_text($text, $max_length) {
        if (strlen($text) <= $max_length) {
            return $text;
        }
        
        return substr($text, 0, $max_length - 3) . '...';
    }

    /**
     * Update document metadata
     */
    public function update_document_metadata($document_id, $new_metadata) {
        try {
            // Fetch existing chunks for this document
            $filter = array(
                'document_id' => array('$eq' => $document_id)
            );
            
            // Query to get all chunks for this document
            $test_vector = array_fill(0, 768, 0.0);
            $matches = $this->query_vectors($test_vector, 1000, $filter);
            
            $update_vectors = array();
            foreach ($matches as $match) {
                $chunk_id = $match['id'];
                $existing_metadata = $match['metadata'] ?? array();
                
                // Merge new metadata with existing
                $updated_metadata = array_merge($existing_metadata, $new_metadata);
                $updated_metadata['last_updated'] = current_time('Y-m-d H:i:s');
                
                // Enforce size limits
                $updated_metadata = $this->enforce_metadata_size_limit($updated_metadata);
                
                $update_vectors[] = array(
                    'id' => $chunk_id,
                    'metadata' => $updated_metadata
                );
            }
            
            // Update vectors in batches
            if (!empty($update_vectors)) {
                $batches = array_chunk($update_vectors, 100);
                foreach ($batches as $batch) {
                    $this->upsert_vectors($batch);
                }
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('LexAI: Error updating document metadata: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get document metadata by document ID
     */
    public function get_document_metadata($document_id) {
        try {
            $filter = array(
                'document_id' => array('$eq' => $document_id)
            );
            
            $test_vector = array_fill(0, 768, 0.0);
            $matches = $this->query_vectors($test_vector, 1, $filter);
            
            if (!empty($matches)) {
                return $matches[0]['metadata'] ?? array();
            }
            
            return array();
            
        } catch (Exception $e) {
            error_log('LexAI: Error getting document metadata: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * Check if an error is retryable
     */
    private function is_retryable_error($error_message) {
        $retryable_patterns = array(
            'timeout',
            'connection',
            'network',
            'temporary',
            'service unavailable',
            'internal server error',
            'bad gateway',
            'gateway timeout'
        );

        $error_lower = strtolower($error_message);

        foreach ($retryable_patterns as $pattern) {
            if (strpos($error_lower, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}
