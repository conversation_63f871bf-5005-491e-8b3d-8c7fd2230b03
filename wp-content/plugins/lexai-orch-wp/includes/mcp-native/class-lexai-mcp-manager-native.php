<?php
/**
 * LexAI MCP Manager Native - Pure PHP Implementation
 *
 * 100% Native PHP implementation of MCP management.
 * All Node.js dependencies have been removed for better performance and reliability.
 *
 * @package LexAI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI MCP Manager Native Class
 *
 * 100% Native PHP implementation of MCP management.
 * All Node.js dependencies have been removed.
 */
class LexAI_MCP_Manager_Native {

    /**
     * Native MCP server instance
     */
    private $native_server;

    /**
     * Registered tools
     */
    private $tools = array();

    /**
     * Configuration
     */
    private $config;

    /**
     * Constructor
     */
    public function __construct(array $config = array()) {
        $this->config = array_merge($this->get_default_config(), $config);
        $this->initialize_native();
    }

    /**
     * Get default configuration
     */
    private function get_default_config(): array {
        return array(
            'debug' => false,
            'cache_enabled' => true
        );
    }





    /**
     * Initialize native implementation
     */
    private function initialize_native(): void {
        // Load native classes
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/interfaces/interface-mcp-tool.php';
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/class-lexai-mcp-server-native.php';
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-pinecone-tool-native.php';
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-web-scraper-tool-native.php';

        // Initialize native server
        $this->native_server = new LexAI_MCP_Server_Native('lexai-native', $this->config);

        // Register native tools
        $this->register_native_tools();
        
        error_log("LexAI MCP Manager Native: Native implementation initialized");
    }



    /**
     * Register native tools
     */
    private function register_native_tools(): void {
        $tools = array(
            new LexAI_Pinecone_Tool_Native(),
            new LexAI_Web_Scraper_Tool_Native()
        );

        foreach ($tools as $tool) {
            if ($tool->is_available()) {
                $this->native_server->register_tool($tool);
                $this->tools[$tool->get_name()] = $tool;
                error_log("LexAI MCP Manager Native: Registered native tool - {$tool->get_name()}");
            } else {
                error_log("LexAI MCP Manager Native: Tool not available - {$tool->get_name()}");
            }
        }
    }

    /**
     * Execute tool using native implementation only
     */
    public function execute_tool(string $tool_name, array $parameters): array {
        if (!isset($this->tools[$tool_name])) {
            throw new Exception("Native tool not found: {$tool_name}");
        }

        $tool = $this->tools[$tool_name];
        $tool->validate_parameters($parameters);

        return $tool->execute($parameters);
    }



    /**
     * Get available tools (native only)
     */
    public function get_available_tools(): array {
        $tools = array();

        foreach ($this->tools as $tool) {
            $tools[] = array(
                'name' => $tool->get_name(),
                'description' => $tool->get_description(),
                'category' => $tool->get_category(),
                'schema' => $tool->get_schema(),
                'metadata' => $tool->get_metadata(),
                'implementation' => 'native'
            );
        }

        return $tools;
    }

    /**
     * Get implementation status
     */
    public function get_status(): array {
        return array(
            'implementation_mode' => 'native',
            'native_ready' => isset($this->native_server) && $this->native_server->is_ready(),
            'tools_count' => count($this->tools),
            'config' => $this->config
        );
    }
}
