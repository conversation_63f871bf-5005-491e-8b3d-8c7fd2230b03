<?php
/**
 * LexAI Web Scraper Tool Native - Pure PHP Implementation
 * 
 * Native PHP implementation of web scraping functionality
 * Replaces Puppeteer/Node.js dependency with cURL and DOMDocument
 *
 * @package LexAI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Web Scraper Tool Native Class
 * 
 * Pure PHP implementation of web scraping using cURL and DOMDocument
 */
class LexAI_Web_Scraper_Tool_Native implements LexAI_MCP_Tool_Interface {

    /**
     * User agent for requests
     */
    private $user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

    /**
     * Allowed domains for scraping
     */
    private $allowed_domains = array(
        'scjn.gob.mx',
        'dof.gob.mx',
        'ordenjuridico.gob.mx',
        'tribunales.gob.mx',
        'cjf.gob.mx',
        'juridicas.unam.mx'
    );

    /**
     * Get tool name
     */
    public function get_name(): string {
        return 'web_scraper_native';
    }

    /**
     * Get tool description
     */
    public function get_description(): string {
        return 'Extracción de contenido web de sitios legales mexicanos usando PHP nativo';
    }

    /**
     * Get tool schema
     */
    public function get_schema(): array {
        return array(
            'type' => 'object',
            'properties' => array(
                'url' => array(
                    'type' => 'string',
                    'description' => 'URL del sitio web a extraer',
                    'format' => 'uri'
                ),
                'selector' => array(
                    'type' => 'string',
                    'description' => 'Selector CSS para extraer contenido específico (opcional)'
                ),
                'extract_type' => array(
                    'type' => 'string',
                    'description' => 'Tipo de extracción',
                    'enum' => array('text', 'html', 'links', 'metadata'),
                    'default' => 'text'
                ),
                'max_length' => array(
                    'type' => 'integer',
                    'description' => 'Longitud máxima del contenido extraído',
                    'default' => 10000,
                    'minimum' => 100,
                    'maximum' => 50000
                )
            ),
            'required' => array('url')
        );
    }

    /**
     * Execute the tool
     */
    public function execute(array $parameters): array {
        $url = $parameters['url'];
        $selector = $parameters['selector'] ?? null;
        $extract_type = $parameters['extract_type'] ?? 'text';
        $max_length = $parameters['max_length'] ?? 10000;

        try {
            // Validate URL
            $this->validate_url($url);
            
            // Fetch content
            $html = $this->fetch_content($url);
            
            // Parse and extract
            $extracted = $this->extract_content($html, $selector, $extract_type, $max_length);
            
            return array(
                'url' => $url,
                'extract_type' => $extract_type,
                'content' => $extracted,
                'length' => strlen($extracted),
                'timestamp' => current_time('mysql')
            );
            
        } catch (Exception $e) {
            throw new Exception("Web scraping failed: " . $e->getMessage());
        }
    }

    /**
     * Validate URL
     */
    private function validate_url(string $url): void {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException("Invalid URL format");
        }

        $parsed_url = parse_url($url);
        $domain = $parsed_url['host'] ?? '';
        
        $allowed = false;
        foreach ($this->allowed_domains as $allowed_domain) {
            if (strpos($domain, $allowed_domain) !== false) {
                $allowed = true;
                break;
            }
        }
        
        if (!$allowed) {
            throw new InvalidArgumentException("Domain not allowed for scraping: {$domain}");
        }
    }

    /**
     * Fetch content using cURL with improved error handling and retry logic
     * Fixed API-WEBSCRAPER-001: Enhanced error handling and timeout management
     */
    private function fetch_content(string $url): string {
        $max_retries = 3;
        $retry_count = 0;

        while ($retry_count <= $max_retries) {
            $ch = curl_init();

            curl_setopt_array($ch, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_USERAGENT => $this->user_agent,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_HTTPHEADER => array(
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language: es-MX,es;q=0.8,en;q=0.5',
                    'Accept-Encoding: gzip, deflate',
                    'Connection: keep-alive',
                    'Cache-Control: no-cache'
                ),
                CURLOPT_ENCODING => '', // Enable automatic decompression
                CURLOPT_FAILONERROR => false // Don't fail on HTTP errors, handle them manually
            ));

            $content = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            $errno = curl_errno($ch);

            curl_close($ch);

            // Handle cURL errors
            if ($content === false) {
                if ($this->is_retryable_curl_error($errno) && $retry_count < $max_retries) {
                    $retry_count++;
                    $delay = pow(2, $retry_count); // Exponential backoff
                    error_log("LexAI Web Scraper: cURL error {$errno}, retrying in {$delay} seconds (attempt {$retry_count}/{$max_retries})");
                    sleep($delay);
                    continue;
                }
                throw new Exception("cURL error ({$errno}): {$error}");
            }

            // Handle HTTP status codes
            if ($http_code === 429) {
                if ($retry_count < $max_retries) {
                    $retry_count++;
                    $delay = 60; // Wait 1 minute for rate limits
                    error_log("LexAI Web Scraper: Rate limited (429), retrying in {$delay} seconds");
                    sleep($delay);
                    continue;
                }
                throw new Exception("Rate limit exceeded (429) for URL: {$url}");
            } elseif ($http_code === 403) {
                throw new Exception("Access forbidden (403) for URL: {$url}. The site may be blocking automated requests.");
            } elseif ($http_code === 404) {
                throw new Exception("Page not found (404) for URL: {$url}");
            } elseif ($http_code >= 500 && $retry_count < $max_retries) {
                $retry_count++;
                $delay = pow(2, $retry_count);
                error_log("LexAI Web Scraper: Server error {$http_code}, retrying in {$delay} seconds");
                sleep($delay);
                continue;
            } elseif ($http_code >= 400) {
                throw new Exception("HTTP error {$http_code} for URL: {$url}");
            }

            // Validate content
            if (empty($content)) {
                if ($retry_count < $max_retries) {
                    $retry_count++;
                    $delay = 2;
                    error_log("LexAI Web Scraper: Empty response, retrying in {$delay} seconds");
                    sleep($delay);
                    continue;
                }
                throw new Exception("Empty response from URL: {$url}");
            }

            return $content;
        }

        throw new Exception("Failed to fetch URL after {$max_retries} attempts: {$url}");
    }

    /**
     * Extract content from HTML
     */
    private function extract_content(string $html, ?string $selector, string $extract_type, int $max_length): string {
        // Create DOMDocument
        $dom = new DOMDocument();
        
        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);
        $dom->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        libxml_clear_errors();
        
        $xpath = new DOMXPath($dom);
        
        switch ($extract_type) {
            case 'text':
                return $this->extract_text($dom, $xpath, $selector, $max_length);
            case 'html':
                return $this->extract_html($dom, $xpath, $selector, $max_length);
            case 'links':
                return $this->extract_links($dom, $xpath, $selector);
            case 'metadata':
                return $this->extract_metadata($dom, $xpath);
            default:
                throw new InvalidArgumentException("Invalid extract_type: {$extract_type}");
        }
    }

    /**
     * Extract text content
     */
    private function extract_text(DOMDocument $dom, DOMXPath $xpath, ?string $selector, int $max_length): string {
        if ($selector) {
            // Convert CSS selector to XPath (basic implementation)
            $xpath_query = $this->css_to_xpath($selector);
            $nodes = $xpath->query($xpath_query);
        } else {
            // Extract from body or entire document
            $nodes = $xpath->query('//body') ?: $xpath->query('//*');
        }
        
        $text = '';
        foreach ($nodes as $node) {
            $text .= $node->textContent . "\n";
        }
        
        // Clean and limit text
        $text = $this->clean_text($text);
        
        return substr($text, 0, $max_length);
    }

    /**
     * Extract HTML content
     */
    private function extract_html(DOMDocument $dom, DOMXPath $xpath, ?string $selector, int $max_length): string {
        if ($selector) {
            $xpath_query = $this->css_to_xpath($selector);
            $nodes = $xpath->query($xpath_query);
            
            $html = '';
            foreach ($nodes as $node) {
                $html .= $dom->saveHTML($node);
            }
        } else {
            $html = $dom->saveHTML();
        }
        
        return substr($html, 0, $max_length);
    }

    /**
     * Extract links
     */
    private function extract_links(DOMDocument $dom, DOMXPath $xpath, ?string $selector): string {
        $links = array();
        $nodes = $xpath->query('//a[@href]');
        
        foreach ($nodes as $node) {
            $href = $node->getAttribute('href');
            $text = trim($node->textContent);
            
            if (!empty($href)) {
                $links[] = array(
                    'url' => $href,
                    'text' => $text
                );
            }
        }
        
        return json_encode($links, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Extract metadata
     */
    private function extract_metadata(DOMDocument $dom, DOMXPath $xpath): string {
        $metadata = array();
        
        // Title
        $title_nodes = $xpath->query('//title');
        if ($title_nodes->length > 0) {
            $metadata['title'] = trim($title_nodes->item(0)->textContent);
        }
        
        // Meta tags
        $meta_nodes = $xpath->query('//meta[@name or @property]');
        foreach ($meta_nodes as $meta) {
            $name = $meta->getAttribute('name') ?: $meta->getAttribute('property');
            $content = $meta->getAttribute('content');
            
            if (!empty($name) && !empty($content)) {
                $metadata['meta'][$name] = $content;
            }
        }
        
        return json_encode($metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }

    /**
     * Convert CSS selector to XPath (basic implementation)
     */
    private function css_to_xpath(string $selector): string {
        // Basic CSS to XPath conversion
        $selector = trim($selector);
        
        // Simple conversions
        $conversions = array(
            '/^#([a-zA-Z0-9_-]+)$/' => '//*[@id="$1"]',  // #id
            '/^\.([a-zA-Z0-9_-]+)$/' => '//*[contains(@class,"$1")]',  // .class
            '/^([a-zA-Z0-9]+)$/' => '//$1'  // tag
        );
        
        foreach ($conversions as $pattern => $replacement) {
            if (preg_match($pattern, $selector, $matches)) {
                return str_replace('$1', $matches[1], $replacement);
            }
        }
        
        // Fallback: treat as tag name
        return '//' . $selector;
    }

    /**
     * Clean extracted text
     */
    private function clean_text(string $text): string {
        // Remove extra whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        
        // Remove common unwanted elements
        $text = preg_replace('/\s*(Menú|Menu|Navegación|Navigation)\s*/i', '', $text);
        
        return trim($text);
    }

    /**
     * Validate tool parameters
     */
    public function validate_parameters(array $parameters): bool {
        if (empty($parameters['url'])) {
            throw new InvalidArgumentException("URL parameter is required");
        }

        if (isset($parameters['extract_type'])) {
            $valid_types = array('text', 'html', 'links', 'metadata');
            if (!in_array($parameters['extract_type'], $valid_types)) {
                throw new InvalidArgumentException("Invalid extract_type");
            }
        }

        if (isset($parameters['max_length'])) {
            if ($parameters['max_length'] < 100 || $parameters['max_length'] > 50000) {
                throw new InvalidArgumentException("max_length must be between 100 and 50000");
            }
        }

        return true;
    }

    /**
     * Get tool category
     */
    public function get_category(): string {
        return 'web';
    }

    /**
     * Check if tool is available
     */
    public function is_available(): bool {
        return function_exists('curl_init') && class_exists('DOMDocument');
    }

    /**
     * Get tool metadata
     */
    public function get_metadata(): array {
        return array(
            'version' => '2.0.0',
            'type' => 'native_php',
            'allowed_domains' => $this->allowed_domains,
            'available' => $this->is_available(),
            'dependencies' => array('curl', 'dom')
        );
    }

    /**
     * Check if a cURL error is retryable
     */
    private function is_retryable_curl_error($errno) {
        $retryable_errors = array(
            CURLE_OPERATION_TIMEDOUT,    // 28 - Timeout
            CURLE_COULDNT_CONNECT,       // 7 - Couldn't connect
            CURLE_COULDNT_RESOLVE_HOST,  // 6 - Couldn't resolve host (sometimes temporary)
            CURLE_RECV_ERROR,            // 56 - Receive error
            CURLE_SEND_ERROR,            // 55 - Send error
            CURLE_GOT_NOTHING,           // 52 - Got nothing
            CURLE_PARTIAL_FILE,          // 18 - Partial file
            CURLE_HTTP_POST_ERROR        // 34 - HTTP post error
        );

        return in_array($errno, $retryable_errors);
    }

    /**
     * Check if a web error message indicates a retryable condition
     */
    private function is_retryable_web_error($error_message) {
        $retryable_patterns = array(
            'timeout',
            'connection',
            'network',
            'temporary',
            'service unavailable',
            'internal server error',
            'bad gateway',
            'gateway timeout',
            'connection reset',
            'connection refused'
        );

        $error_lower = strtolower($error_message);

        foreach ($retryable_patterns as $pattern) {
            if (strpos($error_lower, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }
}
