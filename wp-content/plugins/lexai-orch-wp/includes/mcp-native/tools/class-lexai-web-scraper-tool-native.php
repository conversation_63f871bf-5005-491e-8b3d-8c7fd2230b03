<?php
/**
 * LexAI Web Scraper Tool Native - PHPScraper Implementation
 *
 * Advanced web scraping using PHPScraper library
 * Professional-grade scraping with comprehensive extraction capabilities
 *
 * @package LexAI
 * @since 2.1.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load PHPScraper
require_once LEXAI_PLUGIN_DIR . 'vendor/autoload.php';

use Spe<PERSON>latius\PHPScraper\PHPScraper;

/**
 * LexAI Web Scraper Tool Native Class
 *
 * Advanced web scraping using PHPScraper library with comprehensive extraction capabilities
 */
class LexAI_Web_Scraper_Tool_Native implements LexAI_MCP_Tool_Interface {

    /**
     * PHPScraper instance
     */
    private $scraper;

    /**
     * Allowed domains for scraping (Mexican legal sites)
     */
    private $allowed_domains = array(
        'scjn.gob.mx',
        'dof.gob.mx',
        'ordenjuridico.gob.mx',
        'tribunales.gob.mx',
        'cjf.gob.mx',
        'juridicas.unam.mx',
        'diputados.gob.mx',
        'senado.gob.mx',
        'infonavit.org.mx',
        'imss.gob.mx'
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->scraper = new PHPScraper();
        $this->configure_scraper();
    }

    /**
     * Configure PHPScraper with optimal settings
     */
    private function configure_scraper(): void {
        $this->scraper->setConfig([
            'agent' => 'Mozilla/5.0 (compatible; LexAI-Legal-Bot/2.1; +https://tuasesorlegalvirtual.online)',
            'timeout' => 30,
            'disable_ssl' => true,  // Disable SSL verification for compatibility
            'follow_redirects' => true,
            'max_redirects' => 3
        ]);
    }

    /**
     * Get tool name
     */
    public function get_name(): string {
        return 'web_scraper_advanced';
    }

    /**
     * Get tool description
     */
    public function get_description(): string {
        return 'Extracción avanzada de contenido web usando PHPScraper - Sitios legales mexicanos con capacidades completas de análisis';
    }

    /**
     * Get tool schema
     */
    public function get_schema(): array {
        return array(
            'type' => 'object',
            'properties' => array(
                'url' => array(
                    'type' => 'string',
                    'description' => 'URL del sitio web a extraer',
                    'format' => 'uri'
                ),
                'extract_type' => array(
                    'type' => 'string',
                    'description' => 'Tipo de extracción avanzada',
                    'enum' => array(
                        'comprehensive',  // Extracción completa
                        'text',          // Solo texto
                        'html',          // HTML estructurado
                        'links',         // Enlaces con detalles
                        'images',        // Imágenes con metadatos
                        'metadata',      // Metadatos completos
                        'headings',      // Estructura de encabezados
                        'lists',         // Listas estructuradas
                        'keywords',      // Palabras clave extraídas
                        'feeds',         // RSS/Atom feeds
                        'social_media',  // Meta tags sociales
                        'outline'        // Estructura del documento
                    ),
                    'default' => 'comprehensive'
                ),
                'selector' => array(
                    'type' => 'string',
                    'description' => 'Selector CSS/XPath para contenido específico (opcional)'
                ),
                'max_length' => array(
                    'type' => 'integer',
                    'description' => 'Longitud máxima del contenido extraído',
                    'default' => 15000,
                    'minimum' => 500,
                    'maximum' => 100000
                ),
                'include_analysis' => array(
                    'type' => 'boolean',
                    'description' => 'Incluir análisis automático de contenido legal',
                    'default' => true
                ),
                'follow_links' => array(
                    'type' => 'boolean',
                    'description' => 'Seguir enlaces internos para extracción adicional',
                    'default' => false
                )
            ),
            'required' => array('url')
        );
    }

    /**
     * Execute the tool with advanced PHPScraper capabilities
     */
    public function execute(array $parameters): array {
        $url = $parameters['url'];
        $extract_type = $parameters['extract_type'] ?? 'comprehensive';
        $selector = $parameters['selector'] ?? null;
        $max_length = $parameters['max_length'] ?? 15000;
        $include_analysis = $parameters['include_analysis'] ?? true;
        $follow_links = $parameters['follow_links'] ?? false;

        try {
            // Validate URL
            $this->validate_url($url);

            // Navigate to URL
            $this->scraper->go($url);

            // Extract content based on type
            $extracted_data = $this->extract_advanced_content($extract_type, $selector, $max_length);

            // Add legal analysis if requested
            if ($include_analysis) {
                $extracted_data['legal_analysis'] = $this->analyze_legal_content($extracted_data);
            }

            // Follow internal links if requested
            if ($follow_links) {
                $extracted_data['related_content'] = $this->extract_related_content($url, 3);
            }

            // Build comprehensive response
            $response = array(
                'url' => $url,
                'extract_type' => $extract_type,
                'data' => $extracted_data,
                'metadata' => array(
                    'title' => $this->scraper->title ?? '',
                    'description' => $this->scraper->description ?? '',
                    'keywords' => $this->scraper->keywords ?? array(),
                    'content_length' => $this->calculate_content_length($extracted_data),
                    'extraction_time' => date('Y-m-d H:i:s'),
                    'scraper_version' => 'PHPScraper 3.0 + LexAI 2.1'
                ),
                'success' => true
            );

            return $response;

        } catch (Exception $e) {
            return array(
                'url' => $url,
                'extract_type' => $extract_type,
                'data' => null,
                'error' => $e->getMessage(),
                'success' => false,
                'timestamp' => date('Y-m-d H:i:s')
            );
        }
    }

    /**
     * Extract content using advanced PHPScraper capabilities
     */
    private function extract_advanced_content(string $extract_type, ?string $selector, int $max_length): array {
        switch ($extract_type) {
            case 'comprehensive':
                return $this->extract_comprehensive_data($max_length);

            case 'text':
                $paragraphs = $this->scraper->paragraphs ?? array();
                $all_text = implode(' ', $paragraphs);
                return array(
                    'content' => $this->limit_content($all_text, $max_length),
                    'paragraphs' => $paragraphs,
                    'clean_paragraphs' => $this->scraper->cleanParagraphs ?? array()
                );

            case 'html':
                return array(
                    'html' => $this->limit_content($this->scraper->html ?? '', $max_length),
                    'body_html' => $this->scraper->bodyHtml ?? ''
                );

            case 'links':
                return array(
                    'links' => $this->scraper->links ?? array(),
                    'links_with_details' => $this->scraper->linksWithDetails ?? array(),
                    'internal_links' => $this->scraper->internalLinks ?? array(),
                    'external_links' => $this->scraper->externalLinks ?? array()
                );

            case 'images':
                return array(
                    'images' => $this->scraper->images ?? array(),
                    'images_with_details' => $this->scraper->imagesWithDetails ?? array(),
                    'image_alt_texts' => $this->scraper->imageAltTexts ?? array()
                );

            case 'metadata':
                return $this->extract_complete_metadata();

            case 'headings':
                return array(
                    'h1' => $this->scraper->h1 ?? array(),
                    'h2' => $this->scraper->h2 ?? array(),
                    'h3' => $this->scraper->h3 ?? array(),
                    'h4' => $this->scraper->h4 ?? array(),
                    'h5' => $this->scraper->h5 ?? array(),
                    'h6' => $this->scraper->h6 ?? array(),
                    'headings' => $this->scraper->headings ?? array()
                );

            case 'lists':
                return array(
                    'lists' => $this->scraper->lists ?? array(),
                    'ordered_lists' => $this->scraper->orderedLists ?? array(),
                    'unordered_lists' => $this->scraper->unorderedLists ?? array()
                );

            case 'keywords':
                return array(
                    'content_keywords' => $this->scraper->contentKeywords ?? array(),
                    'keyword_tags' => $this->scraper->keywords ?? array(),
                    'keyword_density' => $this->calculate_keyword_density()
                );

            case 'feeds':
                return array(
                    'feeds' => array(),
                    'note' => 'Feed extraction requires specific implementation'
                );

            case 'social_media':
                return $this->extract_social_media_data();

            case 'outline':
                return array(
                    'outline' => array(),
                    'structure' => $this->analyze_document_structure(),
                    'note' => 'Document outline generated from headings'
                );

            default:
                throw new InvalidArgumentException("Unsupported extract_type: {$extract_type}");
        }
    }

    /**
     * Extract comprehensive data (all available information)
     */
    private function extract_comprehensive_data(int $max_length): array {
        $paragraphs = $this->scraper->paragraphs ?? array();
        $all_text = implode(' ', $paragraphs);

        return array(
            'content' => array(
                'title' => $this->scraper->title ?? '',
                'text' => $this->limit_content($all_text, $max_length),
                'paragraphs' => array_slice($paragraphs, 0, 20)
            ),
            'structure' => array(
                'headings' => $this->scraper->headings ?? array(),
                'lists' => $this->scraper->lists ?? array(),
                'h1' => $this->scraper->h1 ?? array()
            ),
            'links' => array(
                'internal' => array_slice($this->scraper->internalLinks ?? array(), 0, 10),
                'external' => array_slice($this->scraper->externalLinks ?? array(), 0, 10),
                'details' => array_slice($this->scraper->linksWithDetails ?? array(), 0, 15)
            ),
            'media' => array(
                'images' => array_slice($this->scraper->images ?? array(), 0, 10),
                'image_details' => array_slice($this->scraper->imagesWithDetails ?? array(), 0, 10)
            ),
            'metadata' => $this->extract_complete_metadata(),
            'keywords' => array(
                'content_keywords' => $this->scraper->contentKeywords ?? array(),
                'meta_keywords' => $this->scraper->keywords ?? array()
            ),
            'feeds' => array(),
            'social_media' => $this->extract_social_media_data()
        );
    }

    /**
     * Validate URL against allowed domains
     */
    private function validate_url(string $url): void {
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException("Invalid URL format: {$url}");
        }

        $parsed_url = parse_url($url);
        $domain = $parsed_url['host'] ?? '';

        $allowed = false;
        foreach ($this->allowed_domains as $allowed_domain) {
            if (strpos($domain, $allowed_domain) !== false) {
                $allowed = true;
                break;
            }
        }

        if (!$allowed) {
            throw new InvalidArgumentException("Domain not allowed for scraping: {$domain}. Allowed domains: " . implode(', ', $this->allowed_domains));
        }
    }

    /**
     * Extract complete metadata
     */
    private function extract_complete_metadata(): array {
        return array(
            'basic' => array(
                'title' => $this->scraper->title ?? '',
                'description' => $this->scraper->description ?? '',
                'keywords' => $this->scraper->keywords ?? array(),
                'author' => $this->scraper->author ?? ''
            ),
            'technical' => array(
                'canonical' => $this->scraper->canonical ?? '',
                'viewport' => $this->scraper->viewport ?? ''
            ),
            'social' => $this->extract_social_media_data(),
            'custom' => $this->scraper->metaTags ?? array()
        );
    }

    /**
     * Extract social media metadata
     */
    private function extract_social_media_data(): array {
        return array(
            'twitter' => array(
                'available' => false,
                'note' => 'Twitter meta tags require specific implementation'
            ),
            'facebook' => array(
                'available' => false,
                'note' => 'Facebook meta tags require specific implementation'
            ),
            'open_graph' => array()
        );
    }

    /**
     * Analyze document structure
     */
    private function analyze_document_structure(): array {
        $headings = $this->scraper->headings ?? array();
        $structure = array();

        foreach ($headings as $heading) {
            if (is_array($heading) && isset($heading['tag'], $heading['text'])) {
                $level = (int) substr($heading['tag'], 1); // Extract number from h1, h2, etc.
                $structure[] = array(
                    'level' => $level,
                    'text' => $heading['text'],
                    'tag' => $heading['tag']
                );
            }
        }

        return array(
            'hierarchy' => $structure,
            'depth' => $this->calculate_heading_depth($structure),
            'sections' => $this->identify_sections($structure)
        );
    }

    /**
     * Calculate keyword density
     */
    private function calculate_keyword_density(): array {
        $keywords = $this->scraper->contentKeywords ?? array();
        $paragraphs = $this->scraper->paragraphs ?? array();
        $text = implode(' ', $paragraphs);
        $word_count = str_word_count($text);

        $density = array();
        foreach ($keywords as $keyword) {
            $keyword_count = substr_count(strtolower($text), strtolower($keyword));
            $density[$keyword] = array(
                'count' => $keyword_count,
                'density' => $word_count > 0 ? round(($keyword_count / $word_count) * 100, 2) : 0
            );
        }

        return $density;
    }

    /**
     * Analyze legal content for relevant information
     */
    private function analyze_legal_content(array $extracted_data): array {
        $content = '';

        // Extract text content for analysis
        if (isset($extracted_data['content']['text'])) {
            $content = $extracted_data['content']['text'];
        } elseif (isset($extracted_data['content'])) {
            $content = is_string($extracted_data['content']) ? $extracted_data['content'] : '';
        }

        $legal_terms = array(
            'constitutional' => array('constitucional', 'constitución', 'carta magna'),
            'civil' => array('civil', 'código civil', 'derecho civil'),
            'penal' => array('penal', 'código penal', 'delito', 'delitos'),
            'laboral' => array('laboral', 'trabajo', 'trabajador', 'empleado'),
            'fiscal' => array('fiscal', 'impuesto', 'impuestos', 'hacienda'),
            'administrative' => array('administrativo', 'administración pública'),
            'commercial' => array('mercantil', 'comercial', 'empresa', 'sociedades'),
            'procedural' => array('procedimiento', 'proceso', 'juicio', 'demanda'),
            'jurisprudence' => array('jurisprudencia', 'tesis', 'criterio', 'precedente'),
            'legislation' => array('ley', 'decreto', 'reglamento', 'norma', 'disposición')
        );

        $found_terms = array();
        $content_lower = strtolower($content);

        foreach ($legal_terms as $category => $terms) {
            $found_terms[$category] = array();
            foreach ($terms as $term) {
                if (strpos($content_lower, $term) !== false) {
                    $found_terms[$category][] = $term;
                }
            }
        }

        return array(
            'legal_categories' => array_filter($found_terms),
            'legal_relevance' => $this->calculate_legal_relevance($found_terms),
            'document_type' => $this->identify_legal_document_type($content_lower),
            'jurisdiction' => $this->identify_jurisdiction($content_lower),
            'legal_entities' => $this->extract_legal_entities($content)
        );
    }

    /**
     * Extract related content by following internal links
     */
    private function extract_related_content(string $base_url, int $max_links): array {
        $internal_links = array_slice($this->scraper->internalLinks, 0, $max_links);
        $related_content = array();

        foreach ($internal_links as $link) {
            try {
                // Create new scraper instance for each link to avoid conflicts
                $link_scraper = new PHPScraper();
                $link_scraper->setConfig([
                    'agent' => 'Mozilla/5.0 (compatible; LexAI-Legal-Bot/2.1; +https://tuasesorlegalvirtual.online)',
                    'timeout' => 15
                ]);

                $link_scraper->go($link);

                $related_content[] = array(
                    'url' => $link,
                    'title' => $link_scraper->title,
                    'description' => $link_scraper->description,
                    'text_preview' => $this->limit_content($link_scraper->cleanText, 500),
                    'keywords' => array_slice($link_scraper->contentKeywords, 0, 5)
                );

            } catch (Exception $e) {
                // Skip failed links
                continue;
            }
        }

        return $related_content;
    }

    /**
     * Utility methods for content processing and analysis
     */

    /**
     * Limit content to specified length
     */
    private function limit_content(string $content, int $max_length): string {
        if (strlen($content) <= $max_length) {
            return $content;
        }

        return substr($content, 0, $max_length) . '...';
    }

    /**
     * Calculate total content length from extracted data
     */
    private function calculate_content_length(array $data): int {
        $total_length = 0;

        array_walk_recursive($data, function($value) use (&$total_length) {
            if (is_string($value)) {
                $total_length += strlen($value);
            }
        });

        return $total_length;
    }

    /**
     * Calculate heading depth
     */
    private function calculate_heading_depth(array $structure): int {
        if (empty($structure)) {
            return 0;
        }

        $max_level = 0;
        foreach ($structure as $heading) {
            if ($heading['level'] > $max_level) {
                $max_level = $heading['level'];
            }
        }

        return $max_level;
    }

    /**
     * Identify document sections based on headings
     */
    private function identify_sections(array $structure): array {
        $sections = array();
        $current_section = null;

        foreach ($structure as $heading) {
            if ($heading['level'] <= 2) { // H1 or H2 starts new section
                if ($current_section) {
                    $sections[] = $current_section;
                }
                $current_section = array(
                    'title' => $heading['text'],
                    'level' => $heading['level'],
                    'subsections' => array()
                );
            } elseif ($current_section && $heading['level'] > 2) {
                $current_section['subsections'][] = array(
                    'title' => $heading['text'],
                    'level' => $heading['level']
                );
            }
        }

        if ($current_section) {
            $sections[] = $current_section;
        }

        return $sections;
    }

    /**
     * Calculate legal relevance score
     */
    private function calculate_legal_relevance(array $found_terms): int {
        $total_categories = count($found_terms);
        $categories_with_terms = count(array_filter($found_terms));

        if ($total_categories === 0) {
            return 0;
        }

        return round(($categories_with_terms / $total_categories) * 100);
    }

    /**
     * Identify legal document type
     */
    private function identify_legal_document_type(string $content): string {
        $document_types = array(
            'ley' => array('ley', 'código', 'estatuto'),
            'decreto' => array('decreto', 'acuerdo'),
            'reglamento' => array('reglamento', 'disposiciones'),
            'jurisprudencia' => array('jurisprudencia', 'tesis', 'criterio'),
            'sentencia' => array('sentencia', 'resolución', 'fallo'),
            'contrato' => array('contrato', 'convenio', 'acuerdo'),
            'demanda' => array('demanda', 'escrito', 'promoción'),
            'dictamen' => array('dictamen', 'opinión', 'parecer')
        );

        foreach ($document_types as $type => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($content, $keyword) !== false) {
                    return $type;
                }
            }
        }

        return 'documento_general';
    }

    /**
     * Identify jurisdiction
     */
    private function identify_jurisdiction(string $content): string {
        $jurisdictions = array(
            'federal' => array('federal', 'federación', 'república'),
            'local' => array('estado', 'municipal', 'local'),
            'scjn' => array('suprema corte', 'scjn'),
            'tribunales' => array('tribunal', 'juzgado'),
            'administrativa' => array('administrativa', 'administrativo')
        );

        foreach ($jurisdictions as $jurisdiction => $keywords) {
            foreach ($keywords as $keyword) {
                if (strpos($content, $keyword) !== false) {
                    return $jurisdiction;
                }
            }
        }

        return 'general';
    }

    /**
     * Extract legal entities (simplified)
     */
    private function extract_legal_entities(string $content): array {
        $entities = array();

        // Simple regex patterns for common legal entities
        $patterns = array(
            'articles' => '/artículo\s+(\d+)/i',
            'laws' => '/ley\s+([^.]+)/i',
            'codes' => '/código\s+([^.]+)/i',
            'institutions' => '/(suprema\s+corte|tribunal|juzgado|secretaría)\s+([^.]+)/i'
        );

        foreach ($patterns as $type => $pattern) {
            if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
                $entities[$type] = array_slice(array_unique(array_column($matches, 0)), 0, 5);
            }
        }

        return $entities;
    }

    /**
     * Get tool category
     */
    public function get_category(): string {
        return 'web_advanced';
    }

    /**
     * Check if tool is available
     */
    public function is_available(): bool {
        return class_exists('Spekulatius\PHPScraper\PHPScraper');
    }

    /**
     * Get tool metadata
     */
    public function get_metadata(): array {
        return array(
            'version' => '2.1.0',
            'type' => 'phpscraper_advanced',
            'library' => 'PHPScraper 3.0',
            'allowed_domains' => $this->allowed_domains,
            'available' => $this->is_available(),
            'capabilities' => array(
                'comprehensive_extraction',
                'legal_content_analysis',
                'social_media_metadata',
                'keyword_extraction',
                'document_structure_analysis',
                'feed_processing',
                'related_content_discovery'
            ),
            'dependencies' => array('PHPScraper', 'Symfony BrowserKit', 'League URI')
        );
    }

    /**
     * Validate tool parameters
     */
    public function validate_parameters(array $parameters): bool {
        if (empty($parameters['url'])) {
            throw new InvalidArgumentException("URL parameter is required");
        }

        if (isset($parameters['extract_type'])) {
            $valid_types = array(
                'comprehensive', 'text', 'html', 'links', 'images',
                'metadata', 'headings', 'lists', 'keywords', 'feeds',
                'social_media', 'outline'
            );
            if (!in_array($parameters['extract_type'], $valid_types)) {
                throw new InvalidArgumentException("Invalid extract_type. Valid types: " . implode(', ', $valid_types));
            }
        }

        if (isset($parameters['max_length'])) {
            if ($parameters['max_length'] < 500 || $parameters['max_length'] > 100000) {
                throw new InvalidArgumentException("max_length must be between 500 and 100000");
            }
        }

        return true;
    }
}
