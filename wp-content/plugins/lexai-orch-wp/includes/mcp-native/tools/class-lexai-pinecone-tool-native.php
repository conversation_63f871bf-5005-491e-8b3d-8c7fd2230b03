<?php
/**
 * LexAI Pinecone Tool Native - Pure PHP Implementation
 * 
 * Native PHP implementation of Pinecone search functionality
 * Replaces Node.js MCP wrapper with direct API calls
 *
 * @package LexAI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Pinecone Tool Native Class
 * 
 * Pure PHP implementation of Pinecone search using REST API
 */
class LexAI_Pinecone_Tool_Native implements LexAI_MCP_Tool_Interface {

    /**
     * Pinecone API key
     */
    private $api_key;

    /**
     * Pinecone index host (2025-04 API format)
     */
    private $index_host;

    /**
     * Default index name
     */
    private $default_index = 'accord';

    /**
     * API version (latest stable)
     */
    private $api_version = '2025-04';

    /**
     * Namespace configurations
     */
    private $namespaces = array(
        'leyesycodigos' => array(
            'name' => 'leyesycodigos',
            'description' => 'Leyes, códigos y normativas mexicanas',
            'search_type' => 'semantic'
        ),
        'jurisprudencia' => array(
            'name' => 'jurisprudencia',
            'description' => 'Jurisprudencia y precedentes judiciales',
            'search_type' => 'document_retrieval'
        ),
        'tesisscjn' => array(
            'name' => 'tesisscjn',
            'description' => 'Tesis jurisprudenciales de la SCJN',
            'search_type' => 'document_retrieval'
        ),
        'formatos' => array(
            'name' => 'formatos',
            'description' => 'Templates y formatos de documentos legales',
            'search_type' => 'document_retrieval'
        )
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->load_configuration();
    }

    /**
     * Load Pinecone configuration (unified with main handler)
     */
    private function load_configuration(): void {
        // Get API key from unified multi-key system
        try {
            $api_handler = new LexAI_API_Handler();
            $api_key_obj = $api_handler->get_available_api_key('pinecone');
            if ($api_key_obj) {
                $this->api_key = $api_key_obj->api_key;
            }
        } catch (Exception $e) {
            error_log('LexAI Pinecone Native: Error accessing unified API key: ' . $e->getMessage());
            // Fallback to legacy storage
            $this->api_key = get_option('lexai_pinecone_api_key', '');
        }

        // Get configuration from unified settings
        $settings = get_option('lexai_settings', array());
        $pinecone_settings = $settings['pinecone'] ?? array();

        // Support both new host format and legacy environment
        $host = $pinecone_settings['host'] ?? '';
        $environment = $pinecone_settings['environment'] ?? '';
        $index_name = $pinecone_settings['index_name'] ?? 'lexai-knowledge-base';

        // Priority: use host if available (new format), fallback to environment (legacy)
        if (!empty($host)) {
            // New format: direct host URL
            $this->index_host = $host;
        } elseif (!empty($environment)) {
            // Legacy format: construct from environment
            $this->index_host = $this->construct_index_host($environment, $index_name);
        } else {
            // Fallback to legacy option
            $this->index_host = get_option('lexai_pinecone_index_host', '');
        }

        // Update default index from settings
        $this->default_index = $index_name;
    }

    /**
     * Construct index host from legacy environment (fallback)
     */
    private function construct_index_host(string $environment, string $index_name = null): string {
        $index = $index_name ?: $this->default_index;

        // Check if it's the new serverless format (contains dots)
        if (strpos($environment, '.') !== false) {
            // Serverless format: {index-name}.svc.{environment}.pinecone.io
            return "{$index}.svc.{$environment}.pinecone.io";
        } else {
            // Legacy pod-based format: {index-name}-{environment}.svc.pinecone.io
            return "{$index}-{$environment}.svc.pinecone.io";
        }
    }

    /**
     * Get tool name
     */
    public function get_name(): string {
        return 'pinecone_search_native';
    }

    /**
     * Get tool description
     */
    public function get_description(): string {
        return 'Búsqueda semántica en base de conocimientos legal usando Pinecone API nativa';
    }

    /**
     * Get tool schema
     */
    public function get_schema(): array {
        return array(
            'type' => 'object',
            'properties' => array(
                'query' => array(
                    'type' => 'string',
                    'description' => 'Consulta de búsqueda'
                ),
                'namespace' => array(
                    'type' => 'string',
                    'description' => 'Namespace específico (leyesycodigos, jurisprudencia, tesisscjn, formatos)',
                    'enum' => array_keys($this->namespaces)
                ),
                'limit' => array(
                    'type' => 'integer',
                    'description' => 'Número máximo de resultados',
                    'default' => 10,
                    'minimum' => 1,
                    'maximum' => 50
                ),
                'include_metadata' => array(
                    'type' => 'boolean',
                    'description' => 'Incluir metadata en resultados',
                    'default' => true
                ),
                'include_values' => array(
                    'type' => 'boolean',
                    'description' => 'Incluir valores de vectores en resultados (2025-04)',
                    'default' => false
                ),
                'sparse_vector' => array(
                    'type' => 'object',
                    'description' => 'Vector sparse para búsqueda híbrida (2025-04)',
                    'properties' => array(
                        'indices' => array('type' => 'array', 'items' => array('type' => 'integer')),
                        'values' => array('type' => 'array', 'items' => array('type' => 'number'))
                    )
                )
            ),
            'required' => array('query')
        );
    }

    /**
     * Execute the tool (2025-04 API)
     */
    public function execute(array $parameters): array {
        $query = $parameters['query'];
        $namespace = $parameters['namespace'] ?? null;
        $limit = $parameters['limit'] ?? 10;
        $include_metadata = $parameters['include_metadata'] ?? true;
        $include_values = $parameters['include_values'] ?? false;
        $sparse_vector = $parameters['sparse_vector'] ?? null;

        try {
            if ($namespace) {
                return $this->search_namespace($query, $namespace, $limit, $include_metadata, $include_values, $sparse_vector);
            } else {
                return $this->search_all_namespaces($query, $limit, $include_metadata, $include_values, $sparse_vector);
            }
        } catch (Exception $e) {
            throw new Exception("Pinecone search failed (API 2025-04): " . $e->getMessage());
        }
    }

    /**
     * Search specific namespace (2025-04 API)
     */
    private function search_namespace(string $query, string $namespace, int $limit, bool $include_metadata, bool $include_values = false, ?array $sparse_vector = null): array {
        if (!isset($this->namespaces[$namespace])) {
            throw new InvalidArgumentException("Invalid namespace: {$namespace}");
        }

        // Generate embedding for query (dense vector)
        $dense_embedding = $this->generate_embedding($query);

        // Perform Pinecone search with 2025-04 API
        $results = $this->pinecone_query_2025($dense_embedding, $namespace, $limit, $include_metadata, $include_values, $sparse_vector);

        return array(
            'namespace' => $namespace,
            'query' => $query,
            'results' => $results,
            'total_results' => count($results),
            'api_version' => $this->api_version
        );
    }

    /**
     * Search all namespaces (2025-04 API)
     */
    private function search_all_namespaces(string $query, int $limit, bool $include_metadata, bool $include_values = false, ?array $sparse_vector = null): array {
        $all_results = array();
        $per_namespace_limit = max(1, intval($limit / count($this->namespaces)));

        foreach (array_keys($this->namespaces) as $namespace) {
            try {
                $namespace_results = $this->search_namespace($query, $namespace, $per_namespace_limit, $include_metadata, $include_values, $sparse_vector);
                $all_results[$namespace] = $namespace_results['results'];
            } catch (Exception $e) {
                error_log("Pinecone Native 2025-04: Error searching namespace {$namespace}: " . $e->getMessage());
                $all_results[$namespace] = array();
            }
        }

        return array(
            'query' => $query,
            'namespaces' => $all_results,
            'total_namespaces' => count($this->namespaces),
            'api_version' => $this->api_version
        );
    }

    /**
     * Generate embedding using Google Gemini API (matching existing implementation)
     */
    private function generate_embedding(string $text): array {
        // Use existing LexAI embedding generation
        $lexai = LexAI::get_instance();
        if (!$lexai || !$lexai->pinecone_handler) {
            throw new Exception("LexAI Pinecone handler not available");
        }

        return $lexai->pinecone_handler->generate_embedding($text);
    }

    /**
     * Query Pinecone index (2025-04 API)
     */
    private function pinecone_query_2025(array $dense_embedding, string $namespace, int $limit, bool $include_metadata, bool $include_values = false, ?array $sparse_vector = null): array {
        $query_data = array(
            'topK' => $limit,
            'namespace' => $namespace,
            'includeMetadata' => $include_metadata,
            'includeValues' => $include_values
        );

        // 2025-04 API: Support for both dense and sparse vectors
        if (!empty($dense_embedding)) {
            $query_data['vector'] = $dense_embedding;
        }

        if (!empty($sparse_vector)) {
            $query_data['sparseVector'] = $sparse_vector;
        }

        $response = wp_remote_post("https://{$this->index_host}/query", array(
            'headers' => array(
                'Api-Key' => $this->api_key,
                'Content-Type' => 'application/json',
                'X-Pinecone-API-Version' => $this->api_version
            ),
            'body' => json_encode($query_data),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            throw new Exception("Pinecone API 2025-04 request failed: " . $response->get_error_message());
        }

        $http_code = wp_remote_retrieve_response_code($response);
        if ($http_code >= 400) {
            $error_body = wp_remote_retrieve_body($response);
            throw new Exception("Pinecone API 2025-04 error {$http_code}: {$error_body}");
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);

        if (!isset($body['matches'])) {
            throw new Exception("Invalid Pinecone API 2025-04 response format");
        }

        return $this->format_results_2025($body['matches']);
    }

    /**
     * Format search results (2025-04 API)
     */
    private function format_results_2025(array $matches): array {
        $results = array();

        foreach ($matches as $match) {
            $formatted_result = array(
                'id' => $match['id'],
                'score' => $match['score'],
                'metadata' => $match['metadata'] ?? array(),
                'content' => $match['metadata']['content'] ?? '',
                'title' => $match['metadata']['title'] ?? '',
                'source' => $match['metadata']['source'] ?? ''
            );

            // 2025-04 API: Include values if present
            if (isset($match['values'])) {
                $formatted_result['values'] = $match['values'];
            }

            // 2025-04 API: Include sparse values if present
            if (isset($match['sparseValues'])) {
                $formatted_result['sparse_values'] = $match['sparseValues'];
            }

            $results[] = $formatted_result;
        }

        return $results;
    }

    /**
     * Validate tool parameters
     */
    public function validate_parameters(array $parameters): bool {
        if (empty($parameters['query'])) {
            throw new InvalidArgumentException("Query parameter is required");
        }

        if (isset($parameters['namespace']) && !isset($this->namespaces[$parameters['namespace']])) {
            throw new InvalidArgumentException("Invalid namespace: " . $parameters['namespace']);
        }

        if (isset($parameters['limit']) && ($parameters['limit'] < 1 || $parameters['limit'] > 50)) {
            throw new InvalidArgumentException("Limit must be between 1 and 50");
        }

        return true;
    }

    /**
     * Get tool category
     */
    public function get_category(): string {
        return 'search';
    }

    /**
     * Check if tool is available (2025-04 API)
     */
    public function is_available(): bool {
        return !empty($this->api_key) && !empty($this->index_host);
    }

    /**
     * Get tool metadata (2025-04 API)
     */
    public function get_metadata(): array {
        return array(
            'version' => '2.0.0',
            'type' => 'native_php',
            'api_version' => $this->api_version,
            'namespaces' => $this->namespaces,
            'default_index' => $this->default_index,
            'index_host' => $this->index_host,
            'available' => $this->is_available(),
            'features' => array(
                'dense_vectors' => true,
                'sparse_vectors' => true,
                'hybrid_search' => true,
                'namespace_management' => true,
                'metadata_filtering' => true
            )
        );
    }
}
