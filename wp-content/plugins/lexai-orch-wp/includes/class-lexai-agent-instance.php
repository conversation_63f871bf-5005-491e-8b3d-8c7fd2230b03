<?php
/**
 * LexAI Agent Instance
 *
 * @package LexAI
 * @since 1.0.0
 */

class LexAI_Agent_Instance {
    
    /**
     * Agent data
     */
    private $agent_data;
    
    /**
     * API handler
     */
    private $api_handler;

    /**
     * Tool executor (MCP + legacy)
     */
    private $tool_executor;
    
    /**
     * Constructor
     */
    public function __construct($agent_data, $api_handler = null) {
        $this->agent_data = $agent_data;
        $this->api_handler = $api_handler ?: new LexAI_API_Handler();
        
        // Initialize tool executor with native MCP support
        try {
            require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-tool-executor.php';
            $this->tool_executor = new LexAI_Tool_Executor();
        } catch (Exception $e) {
            error_log("LexAI Agent Instance: Could not initialize native tool executor: " . $e->getMessage());
            $this->tool_executor = null;
        }
    }
    
    /**
     * Execute agent task
     */
    public function execute_task($task_data) {
        try {
            $messages = $task_data['messages'] ?? array();
            $context = $task_data['context'] ?? '';
            $tools = $task_data['tools'] ?? array();
            $files = $task_data['files'] ?? array();
            
            // Prepare system instruction
            $system_instruction = $this->agent_data->system_instruction;
            if ($context) {
                $system_instruction .= "\n\nCONTEXTO ADICIONAL:\n" . $context;
            }
            
            // Add file context if files are present
            if (!empty($files)) {
                $file_context = $this->build_file_context($files);
                if ($file_context) {
                    $system_instruction .= "\n\n" . $file_context;
                }
            }
            
            // Prepare files for Gemini API
            $gemini_files = $this->prepare_files_for_gemini($files);
            
            // Make API request
            $response = $this->api_handler->make_gemini_request(
                $messages,
                $system_instruction,
                $tools,
                $this->agent_data->model ?? null,
                $gemini_files,
                'default',
                array('max_output_tokens' => $this->agent_data->max_output_tokens ?? 8192)
            );
            
            return array(
                'success' => true,
                'response' => $response['response'] ?? '',
                'agent_id' => $this->agent_data->id,
                'agent_name' => $this->agent_data->name,
                'model_used' => $this->agent_data->model ?? 'gemini-2.5-flash',
                'metadata' => array(
                    'tokens_used' => $response['usage']['total_tokens'] ?? 0,
                    'files_processed' => count($files)
                )
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'agent_id' => $this->agent_data->id,
                'agent_name' => $this->agent_data->name
            );
        }
    }
    
    /**
     * Build file context for system instruction
     */
    private function build_file_context($files) {
        if (empty($files)) {
            return '';
        }
        
        $context = "ARCHIVOS ADJUNTOS DISPONIBLES:\n";
        
        foreach ($files as $file) {
            $context .= "\n--- ARCHIVO: {$file->original_filename} ---\n";
            $context .= "Tipo: {$file->file_type}\n";
            $context .= "Tamaño: " . size_format($file->file_size) . "\n";
            
            // Add extracted text for documents
            if ($file->file_type === 'document' && !empty($file->extracted_text)) {
                $context .= "Contenido extraído:\n";
                $context .= $file->extracted_text . "\n";
            }
            
            // Add description for other file types
            switch ($file->file_type) {
                case 'image':
                    $context .= "Descripción: Imagen disponible para análisis visual. Puede contener:\n";
                    $context .= "  - Texto o documentos escaneados\n";
                    $context .= "  - Gráficos, diagramas o esquemas legales\n";
                    $context .= "  - Evidencia fotográfica\n";
                    $context .= "  - Firmas, sellos o documentos oficiales\n";
                    break;
                case 'video':
                    $context .= "Descripción: Video disponible para análisis multimodal. Puede contener:\n";
                    $context .= "  - Declaraciones o testimonios grabados\n";
                    $context .= "  - Evidencia audiovisual de eventos\n";
                    $context .= "  - Presentaciones o explicaciones legales\n";
                    $context .= "  - Documentos mostrados en pantalla\n";
                    break;
                case 'audio':
                    $context .= "Descripción: Audio disponible para análisis y transcripción. Puede contener:\n";
                    $context .= "  - Conversaciones o llamadas telefónicas\n";
                    $context .= "  - Declaraciones o testimonios orales\n";
                    $context .= "  - Audiencias o procedimientos legales\n";
                    $context .= "  - Instrucciones o explicaciones verbales\n";
                    break;
            }
            
            $context .= "--- FIN ARCHIVO ---\n";
        }
        
        $context .= "\nINSTRUCCIONES PARA ANÁLISIS DE ARCHIVOS:\n";
        $context .= "1. ANÁLISIS INTEGRAL:\n";
        $context .= "   - Examina todos los archivos adjuntos en relación con la consulta legal\n";
        $context .= "   - Identifica elementos clave: fechas, nombres, términos legales, cláusulas\n";
        $context .= "   - Busca inconsistencias, errores o problemas legales\n\n";

        $context .= "2. DOCUMENTOS DE TEXTO:\n";
        $context .= "   - Utiliza el texto extraído para análisis detallado\n";
        $context .= "   - Identifica cláusulas problemáticas o faltantes\n";
        $context .= "   - Verifica cumplimiento con normativa aplicable\n\n";

        $context .= "3. CONTENIDO MULTIMEDIA:\n";
        $context .= "   - Para imágenes: analiza texto visible, firmas, sellos, diagramas\n";
        $context .= "   - Para videos: transcribe diálogos importantes, describe acciones relevantes\n";
        $context .= "   - Para audios: transcribe conversaciones, identifica voces y contexto\n\n";

        $context .= "4. CITACIÓN Y REFERENCIAS:\n";
        $context .= "   - Cita específicamente cada archivo al hacer referencia a su contenido\n";
        $context .= "   - Usa formato: \"Según [nombre_archivo]: [contenido específico]\"\n";
        $context .= "   - Señala claramente cualquier contradicción entre archivos\n\n";

        $context .= "5. RECOMENDACIONES:\n";
        $context .= "   - Proporciona recomendaciones específicas basadas en el contenido de los archivos\n";
        $context .= "   - Identifica documentos adicionales que podrían ser necesarios\n";
        $context .= "   - Sugiere acciones legales apropiadas basadas en la evidencia presentada\n";
        
        return $context;
    }
    
    /**
     * Prepare files for Gemini API
     */
    private function prepare_files_for_gemini($files) {
        $gemini_files = array();

        foreach ($files as $file) {
            if (!empty($file->gemini_uri) && $file->processing_status === 'completed') {
                $file_data = array(
                    'file_id' => $file->id,
                    'gemini_uri' => $file->gemini_uri,
                    'mime_type' => $file->mime_type,
                    'file_type' => $file->file_type,
                    'filename' => $file->original_filename,
                    'size' => $file->file_size
                );

                // Add extracted text for documents if available
                if ($file->file_type === 'document' && !empty($file->extracted_text)) {
                    $file_data['extracted_text'] = $file->extracted_text;
                }

                $gemini_files[] = $file_data;
            } else {
                // Log files that couldn't be processed
                error_log("LexAI: File {$file->original_filename} not ready for Gemini (status: {$file->processing_status}, URI: " . ($file->gemini_uri ? 'present' : 'missing') . ")");
            }
        }

        return $gemini_files;
    }
    
    /**
     * Get agent capabilities
     */
    public function get_capabilities() {
        $capabilities = array(
            'name' => $this->agent_data->name,
            'description' => $this->agent_data->description,
            'specialization' => $this->agent_data->specialization ?? 'general',
            'tools' => json_decode($this->agent_data->tools ?? '[]', true),
            'model' => $this->agent_data->model ?? 'gemini-2.5-flash'
        );
        
        // Add file processing capabilities
        $capabilities['file_support'] = array(
            'documents' => true,
            'images' => true,
            'videos' => true,
            'audio' => true,
            'text_extraction' => true,
            'multimodal_analysis' => true
        );
        
        return $capabilities;
    }
    
    /**
     * Validate task data
     */
    private function validate_task_data($task_data) {
        if (!isset($task_data['messages']) || !is_array($task_data['messages'])) {
            throw new Exception('Messages array is required');
        }
        
        // Validate file data if present
        if (isset($task_data['files']) && is_array($task_data['files'])) {
            foreach ($task_data['files'] as $file) {
                if (!is_object($file) || !isset($file->id)) {
                    throw new Exception('Invalid file data structure');
                }
            }
        }
        
        return true;
    }
    
    /**
     * Process tool calls if any - Enhanced with MCP support
     */
    private function process_tool_calls($response, $tools) {
        // Check if the response contains function calls
        if (!isset($response['function_calls']) || empty($response['function_calls'])) {
            return $response;
        }

        $tool_results = array();

        foreach ($response['function_calls'] as $function_call) {
            $tool_name = $function_call['functionCall']['name'] ?? '';
            $args = $function_call['functionCall']['args'] ?? array();

            try {
                // Use MCP tool executor if available
                if ($this->tool_executor) {
                    $result = $this->tool_executor->execute_tool($tool_name, $args, $this->agent_data->id);
                    
                    $tool_results[] = array(
                        'tool_call' => $function_call,
                        'result' => $result['data'] ?? $result,
                        'execution_type' => $result['execution_type'] ?? 'unknown',
                        'success' => $result['success'] ?? false
                    );
                } else {
                    // Fallback to legacy tool execution
                    $result = $this->execute_legacy_tool($tool_name, $args);
                    
                    $tool_results[] = array(
                        'tool_call' => $function_call,
                        'result' => $result,
                        'execution_type' => 'legacy_fallback',
                        'success' => isset($result['success']) ? $result['success'] : true
                    );
                }

            } catch (Exception $e) {
                $tool_results[] = array(
                    'tool_call' => $function_call,
                    'result' => array(
                        'success' => false,
                        'message' => 'Error al ejecutar herramienta: ' . $e->getMessage()
                    ),
                    'execution_type' => 'error',
                    'success' => false
                );
            }
        }

        // Add tool results to the response
        $response['tool_results'] = $tool_results;

        return $response;
    }

    /**
     * Execute legacy tool (fallback when MCP is not available)
     */
    private function execute_legacy_tool($tool_name, $args) {
        switch ($tool_name) {
            case 'legal_knowledge_base':
                $pinecone_handler = new LexAI_Pinecone_Handler();
                $query = $args['query'] ?? '';
                $category = $args['category'] ?? null;
                $filter = $category ? ['category' => ['$eq' => $category]] : null;
                return $pinecone_handler->search_knowledge_base($query, 5, $filter);

            case 'google_search':
                $google_agent = new LexAI_Google_Search_Agent();
                $query = $args['query'] ?? '';
                return $google_agent->search_and_ground($query, $this->agent_data->created_by ?? 0);

            case 'document_analysis':
                return array(
                    'success' => false,
                    'message' => 'Herramienta de análisis de documentos no implementada'
                );

            default:
                return array(
                    'success' => false,
                    'message' => 'Herramienta desconocida: ' . $tool_name
                );
        }
    }
    
    /**
     * Format response for consistency
     */
    private function format_response($raw_response) {
        // Clean up and format the response
        $formatted = trim($raw_response);
        
        // Add legal disclaimers if needed
        if (strpos($formatted, 'DISCLAIMER') === false) {
            $formatted .= "\n\n---\n*DISCLAIMER: Esta respuesta es solo para fines informativos y no constituye asesoría legal profesional. Para casos específicos, consulte con un abogado calificado.*";
        }
        
        return $formatted;
    }
    
    /**
     * Log agent execution
     */
    private function log_execution($task_data, $result) {
        $log_data = array(
            'agent_id' => $this->agent_data->id,
            'agent_name' => $this->agent_data->name,
            'model_used' => $this->agent_data->model ?? 'gemini-2.5-flash',
            'files_count' => count($task_data['files'] ?? array()),
            'success' => $result['success'],
            'timestamp' => current_time('mysql')
        );
        
        if (!$result['success']) {
            $log_data['error'] = $result['error'];
        }
        
        error_log('LexAI Agent Execution: ' . json_encode($log_data));
    }
    
    /**
     * Get agent statistics
     */
    public function get_statistics() {
        global $wpdb;

        $task_executions_table = $wpdb->prefix . LEXAI_TASK_EXECUTIONS_TABLE;

        // Get total executions for this agent
        $total_executions = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $task_executions_table WHERE agent_id = %d",
            $this->agent_data->id
        ));

        // Get successful executions
        $successful_executions = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $task_executions_table WHERE agent_id = %d AND status = 'completed'",
            $this->agent_data->id
        ));

        // Calculate success rate
        $success_rate = $total_executions > 0 ? ($successful_executions / $total_executions) * 100 : 0;

        // Get average response time
        $avg_response_time = $wpdb->get_var($wpdb->prepare(
            "SELECT AVG(execution_time) FROM $task_executions_table WHERE agent_id = %d AND status = 'completed'",
            $this->agent_data->id
        ));

        // Get files processed (count from task executions with file metadata)
        $files_processed = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $task_executions_table
             WHERE agent_id = %d AND metadata LIKE '%files_processed%'",
            $this->agent_data->id
        ));

        return array(
            'total_executions' => intval($total_executions),
            'success_rate' => round($success_rate, 2),
            'average_response_time' => round(floatval($avg_response_time), 2),
            'files_processed' => intval($files_processed)
        );
    }
}
