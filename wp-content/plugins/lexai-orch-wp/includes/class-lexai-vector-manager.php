<?php
/**
 * LexAI Vector Manager Class
 * Advanced vector database management with Pinecone
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Vector Manager Class
 */
class LexAI_Vector_Manager {
    
    /**
     * Pinecone handler
     */
    private $pinecone_handler;
    
    /**
     * API handler
     */
    private $api_handler;
    
    /**
     * Queue table name
     */
    private $queue_table;
    
    /**
     * Constructor
     */
    public function __construct($pinecone_handler = null, $api_handler = null) {
        global $wpdb;

        $this->pinecone_handler = $pinecone_handler ?: new LexAI_Pinecone_Handler();
        $this->api_handler = $api_handler ?: new LexAI_API_Handler();
        $this->queue_table = $wpdb->prefix . 'lexai_vector_queue';

        $this->create_queue_table();
        $this->setup_cron_hooks();
        add_action('lexai_process_vector_file', array($this, 'process_file'));
        add_action('lexai_process_vector_queue_item', array($this, 'process_batch_from_queue'));
    }

    /**
     * LEX-PERF-002: Setup cron hooks for background processing
     */
    private function setup_cron_hooks() {
        // Schedule queue processor if not already scheduled
        if (!wp_next_scheduled('lexai_process_vector_queue')) {
            wp_schedule_event(time(), 'hourly', 'lexai_process_vector_queue');
        }
        
        add_action('lexai_process_vector_queue', array($this, 'process_queue_cron'));
    }

    /**
     * LEX-PERF-002: Cron job to process pending items in queue
     */
    public function process_queue_cron() {
        global $wpdb;
        
        // Find pending or stalled processing items
        $pending_items = $wpdb->get_results(
            "SELECT id FROM {$this->queue_table} 
             WHERE status = 'pending' 
             OR (status = 'processing' AND updated_at < DATE_SUB(NOW(), INTERVAL 1 HOUR))
             ORDER BY created_at ASC 
             LIMIT 5"
        );
        
        foreach ($pending_items as $item) {
            // Reset stalled items to pending
            if ($wpdb->get_var($wpdb->prepare(
                "SELECT status FROM {$this->queue_table} WHERE id = %d", 
                $item->id
            )) === 'processing') {
                $wpdb->update(
                    $this->queue_table,
                    array('status' => 'pending'),
                    array('id' => $item->id),
                    array('%s'),
                    array('%d')
                );
            }
            
            // Schedule processing
            wp_schedule_single_event(time() + 10, 'lexai_process_vector_file', array($item->id));
        }
    }
    
    /**
     * Create queue table
     */
    private function create_queue_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS {$this->queue_table} (
            id int(11) NOT NULL AUTO_INCREMENT,
            file_name varchar(255) NOT NULL,
            file_path varchar(500) NOT NULL,
            file_size bigint NOT NULL,
            file_type varchar(50) NOT NULL,
            status enum('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
            progress int(3) DEFAULT 0,
            total_chunks int(11) DEFAULT 0,
            processed_chunks int(11) DEFAULT 0,
            metadata longtext,
            error_message text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY status (status),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Add file to processing queue with namespace support
     */
    public function add_to_queue($file_path, $metadata = array()) {
        global $wpdb;

        if (!file_exists($file_path)) {
            throw new Exception(__('Archivo no encontrado', 'lexai'));
        }

        $file_info = pathinfo($file_path);
        $file_size = filesize($file_path);

        // Validate namespace if provided
        $namespace = $metadata['namespace'] ?? 'leyesycodigos'; // Default namespace
        $valid_namespaces = array('leyesycodigos', 'jurisprudencia', 'tesisscjn', 'formatos');

        if (!in_array($namespace, $valid_namespaces)) {
            throw new Exception(__('Namespace inválido. Debe ser uno de: ' . implode(', ', $valid_namespaces), 'lexai'));
        }

        // Ensure namespace is in metadata
        $metadata['namespace'] = $namespace;

        $result = $wpdb->insert(
            $this->queue_table,
            array(
                'file_name' => $file_info['basename'],
                'file_path' => $file_path,
                'file_size' => $file_size,
                'file_type' => $file_info['extension'] ?? 'unknown',
                'metadata' => json_encode($metadata),
                'status' => 'pending'
            ),
            array('%s', '%s', '%d', '%s', '%s', '%s')
        );

        if ($result === false) {
            throw new Exception(__('Error al agregar archivo a la cola', 'lexai'));
        }

        $queue_id = $wpdb->insert_id;
        wp_schedule_single_event(time() + 5, 'lexai_process_vector_file', array($queue_id));

        return $queue_id;
    }
    
    /**
     * LEX-PERF-002: Process file from queue in batches to avoid timeouts
     */
    public function process_file($queue_id) {
        global $wpdb;
        
        $queue_item = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->queue_table} WHERE id = %d", $queue_id));
        
        if (!$queue_item) {
            return;
        }

        // LEX-PERF-002: Allow resuming processing
        if ($queue_item->status === 'pending') {
            $this->initialize_processing($queue_id, $queue_item);
        } elseif ($queue_item->status === 'processing') {
            $this->continue_processing($queue_id, $queue_item);
        } else {
            return; // Already completed or failed
        }
    }

    /**
     * LEX-PERF-002: Initialize processing for new files
     */
    private function initialize_processing($queue_id, $queue_item) {
        try {
            $this->update_queue_status($queue_id, 'processing', 0);
            
            $text_content = $this->extract_text_from_file($queue_item->file_path);
            if (empty($text_content)) {
                throw new Exception(__('No se pudo extraer texto del archivo', 'lexai'));
            }
            
            $metadata = json_decode($queue_item->metadata, true) ?: array();
            $chunks = $this->split_text_into_chunks($text_content, $metadata);
            
            // Store chunks temporarily for batch processing
            $chunks_file = $queue_item->file_path . '.chunks';
            file_put_contents($chunks_file, json_encode($chunks));
            
            $this->update_queue_chunks($queue_id, count($chunks), 0);
            
            // Process first batch
            $this->process_batch_from_queue($queue_id, 5); // Start with 5 chunks
            
        } catch (Exception $e) {
            $this->update_queue_status($queue_id, 'failed', 0, $e->getMessage());
        }
    }

    /**
     * LEX-PERF-002: Continue processing existing files
     */
    private function continue_processing($queue_id, $queue_item) {
        $this->process_batch_from_queue($queue_id, 5);
    }

    /**
     * LEX-PERF-002: Process a batch of chunks from queue
     */
    public function process_batch_from_queue($queue_id, $batch_size = 5) {
        global $wpdb;
        
        $queue_item = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->queue_table} WHERE id = %d", $queue_id));
        if (!$queue_item || $queue_item->status !== 'processing') {
            return;
        }

        $chunks_file = $queue_item->file_path . '.chunks';
        if (!file_exists($chunks_file)) {
            $this->update_queue_status($queue_id, 'failed', 0, 'Archivo de chunks no encontrado');
            return;
        }

        $chunks = json_decode(file_get_contents($chunks_file), true);
        if (!$chunks) {
            $this->update_queue_status($queue_id, 'failed', 0, 'Error al leer chunks');
            return;
        }

        $start_index = $queue_item->processed_chunks;
        $chunks_to_process = array_slice($chunks, $start_index, $batch_size);

        if (empty($chunks_to_process)) {
            // Processing completed
            $this->finalize_processing($queue_id, $queue_item);
            return;
        }

        try {
            $vectors_batch = array();
            $processed_in_batch = 0;

            foreach ($chunks_to_process as $relative_index => $chunk) {
                $absolute_index = $start_index + $relative_index;
                
                try {
                    $embedding = $this->generate_embedding($chunk['content']);
                    if (!$embedding) {
                        continue;
                    }
                    
                    $vector_id = $this->generate_vector_id($queue_item->file_name, $absolute_index);
                    $vectors_batch[] = array(
                        'id' => $vector_id,
                        'values' => $embedding,
                        'metadata' => array_merge($chunk['metadata'], array(
                            'file_name' => $queue_item->file_name,
                            'chunk_index' => $absolute_index,
                            'processed_at' => current_time('mysql')
                        ))
                    );
                    
                    $processed_in_batch++;
                    
                } catch (Exception $e) {
                    error_log("LexAI Vector Processing Error for chunk {$absolute_index}: " . $e->getMessage());
                    continue;
                }
            }

            // Upload batch to Pinecone with namespace
            if (!empty($vectors_batch)) {
                $metadata = json_decode($queue_item->metadata, true) ?? array();
                $namespace = $metadata['namespace'] ?? 'leyesycodigos';
                $this->upload_vectors_batch($vectors_batch, $namespace);
            }

            // Update progress
            $new_processed_count = $start_index + count($chunks_to_process);
            $progress = round(($new_processed_count / $queue_item->total_chunks) * 100);
            $this->update_queue_progress($queue_id, $progress, $new_processed_count);

            // Schedule next batch processing
            if ($new_processed_count < $queue_item->total_chunks) {
                wp_schedule_single_event(time() + 5, 'lexai_process_vector_queue_item', array($queue_id));
            } else {
                $this->finalize_processing($queue_id, $queue_item);
            }

        } catch (Exception $e) {
            $this->update_queue_status($queue_id, 'failed', 0, $e->getMessage());
        }
    }

    /**
     * LEX-PERF-002: Finalize processing and cleanup
     */
    private function finalize_processing($queue_id, $queue_item) {
        $this->update_queue_status($queue_id, 'completed', 100);
        
        // Cleanup temporary files
        $chunks_file = $queue_item->file_path . '.chunks';
        if (file_exists($chunks_file)) {
            @unlink($chunks_file);
        }
        
        // Delete original file if it's in uploads directory
        if (strpos($queue_item->file_path, wp_upload_dir()['basedir']) !== false) {
            @unlink($queue_item->file_path);
        }
        
        error_log("LexAI: Successfully processed file {$queue_item->file_name} with {$queue_item->total_chunks} chunks");
    }
    
    private function generate_embedding($text) {
        try {
            $settings = get_option('lexai_settings', array());
            $pinecone_settings = $settings['pinecone'] ?? array();
            $model = $pinecone_settings['embedding_model'] ?? 'text-embedding-004';
            
            $api_key = $this->api_handler->get_available_api_key();
            
            $url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:embedContent";
            
            $payload = array(
                'model' => "models/{$model}",
                'content' => array('parts' => array(array('text' => $text))),
                'taskType' => 'SEMANTIC_SIMILARITY'
            );
            
            $headers = array('Content-Type' => 'application/json', 'x-goog-api-key' => $api_key->api_key);
            
            $args = array('method' => 'POST', 'headers' => $headers, 'body' => json_encode($payload), 'timeout' => 30, 'sslverify' => true);
            
            $response = wp_remote_request($url, $args);
            
            if (is_wp_error($response)) throw new Exception($response->get_error_message());
            
            $response_code = wp_remote_retrieve_response_code($response);
            $response_body = wp_remote_retrieve_body($response);
            
            if ($response_code !== 200) {
                $error_data = json_decode($response_body, true);
                throw new Exception($error_data['error']['message'] ?? 'Error desconocido');
            }
            
            $data = json_decode($response_body, true);
            
            if (!isset($data['embedding']['values'])) throw new Exception(__('Respuesta de embedding inválida', 'lexai'));
            
            return $data['embedding']['values'];
        } catch (Exception $e) {
            error_log('LexAI Embedding Error: ' . $e->getMessage());
            return null;
        }
    }
    
    private function split_text_into_chunks($text, $metadata = array()) {
        $settings = get_option('lexai_settings', array());
        $pinecone_settings = $settings['pinecone'] ?? array();
        $chunk_size = intval($pinecone_settings['chunk_size'] ?? 1000);
        $chunk_overlap = intval($pinecone_settings['chunk_overlap'] ?? 200);
        
        $chunks = array();
        $text = $this->clean_text($text);
        $sections = $this->split_by_semantic_boundaries($text);
        
        foreach ($sections as $section_index => $section) {
            if (strlen($section) <= $chunk_size) {
                $chunks[] = array('content' => $section, 'metadata' => array_merge($metadata, array('section_index' => $section_index, 'chunk_type' => 'section', 'character_count' => strlen($section))));
            } else {
                $section_chunks = $this->split_large_section($section, $chunk_size, $chunk_overlap);
                foreach ($section_chunks as $chunk_index => $chunk_content) {
                    $chunks[] = array('content' => $chunk_content, 'metadata' => array_merge($metadata, array('section_index' => $section_index, 'chunk_index' => $chunk_index, 'chunk_type' => 'subsection', 'character_count' => strlen($chunk_content))));
                }
            }
        }
        return $chunks;
    }
    
    private function clean_text($text) {
        $text = preg_replace('/\s+/', ' ', $text);
        $text = preg_replace('/[^\p{L}\p{N}\p{P}\p{Z}]/u', '', $text);
        $text = str_replace(array("\r\n", "\r"), "\n", $text);
        return trim($text);
    }
    
    private function split_by_semantic_boundaries($text) {
        $patterns = array('/(?=Art[íi]culo\s+\d+)/i', '/(?=Secci[óo]n\s+\d+)/i', '/(?=Cap[íi]tulo\s+\d+)/i', '/(?=T[íi]tulo\s+\d+)/i', '/(?=Libro\s+\d+)/i');
        $best_split = array($text);
        $max_sections = 1;
        
        foreach ($patterns as $pattern) {
            $split = preg_split($pattern, $text, -1, PREG_SPLIT_NO_EMPTY);
            if (count($split) > $max_sections && count($split) < 100) {
                $best_split = $split;
                $max_sections = count($split);
            }
        }
        
        if ($max_sections === 1) {
            $best_split = preg_split('/\n\s*\n/', $text, -1, PREG_SPLIT_NO_EMPTY);
        }
        return array_map('trim', $best_split);
    }
    
    private function split_large_section($text, $chunk_size, $overlap) {
        $chunks = array();
        $start = 0;
        $text_length = strlen($text);
        
        while ($start < $text_length) {
            $end = $start + $chunk_size;
            if ($end >= $text_length) {
                $chunks[] = substr($text, $start);
                break;
            }
            
            $break_point = $this->find_break_point($text, $start, $end);
            if ($break_point === false) $break_point = $end;
            
            $chunks[] = substr($text, $start, $break_point - $start);
            $start = $break_point - $overlap;
            if ($start < 0) $start = 0;
        }
        return array_filter($chunks, function($chunk) { return strlen(trim($chunk)) > 50; });
    }
    
    private function find_break_point($text, $start, $end) {
        $search_range = 100;
        $sentence_endings = array('.', '!', '?');
        
        for ($i = $end; $i >= max($start, $end - $search_range); $i--) {
            if (in_array($text[$i], $sentence_endings)) {
                if ($i + 1 >= strlen($text) || ctype_space($text[$i + 1])) return $i + 1;
            }
        }
        for ($i = $end; $i >= max($start, $end - $search_range); $i--) {
            if ($text[$i] === "\n") return $i + 1;
        }
        for ($i = $end; $i >= max($start, $end - $search_range); $i--) {
            if (ctype_space($text[$i])) return $i + 1;
        }
        return false;
    }

    private function extract_text_from_file($file_path) {
        $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION) ?? '');
        switch ($extension) {
            case 'txt': return file_get_contents($file_path);
            case 'pdf': return $this->extract_text_from_pdf($file_path);
            case 'doc':
            case 'docx': return $this->extract_text_from_docx($file_path);
            default: throw new Exception(__('Formato de archivo no soportado', 'lexai'));
        }
    }

    private function extract_text_from_pdf($file_path) {
        // Dependency: composer require smalot/pdfparser
        if (!class_exists('\Smalot\PdfParser\Parser')) {
            throw new Exception('La librería PdfParser no está instalada. Ejecuta `composer require smalot/pdfparser`.');
        }

        try {
            $parser = new \Smalot\PdfParser\Parser();
            $pdf = $parser->parseFile($file_path);
            return $pdf->getText();
        } catch (Exception $e) {
            throw new Exception('Error al procesar el PDF: ' . $e->getMessage());
        }
    }

    private function extract_text_from_docx($file_path) {
        // Dependency: composer require phpoffice/phpword
        if (!class_exists('\PhpOffice\PhpWord\IOFactory')) {
            throw new Exception('La librería PhpWord no está instalada. Ejecuta `composer require phpoffice/phpword`.');
        }

        try {
            $phpWord = \PhpOffice\PhpWord\IOFactory::load($file_path);
            $text = '';
            foreach ($phpWord->getSections() as $section) {
                foreach ($section->getElements() as $element) {
                    if (method_exists($element, 'getText')) {
                        $text .= $element->getText() . "\n";
                    }
                }
            }
            return $text;
        } catch (Exception $e) {
            throw new Exception('Error al procesar el DOCX: ' . $e->getMessage());
        }
    }

    private function generate_vector_id($file_name, $chunk_index) {
        return 'doc_' . preg_replace('/[^a-zA-Z0-9_-]/', '_', $file_name) . '_chunk_' . $chunk_index . '_' . time();
    }

    private function upload_vectors_batch($vectors, $namespace = null) {
        return $this->pinecone_handler->upsert_vectors($vectors, $namespace);
    }

    private function update_queue_status($queue_id, $status, $progress = null, $error_message = null) {
        global $wpdb;
        $update_data = array('status' => $status);
        $update_format = array('%s');
        if ($progress !== null) {
            $update_data['progress'] = $progress;
            $update_format[] = '%d';
        }
        if ($error_message !== null) {
            $update_data['error_message'] = $error_message;
            $update_format[] = '%s';
        }
        return $wpdb->update($this->queue_table, $update_data, array('id' => $queue_id), $update_format, array('%d'));
    }

    private function update_queue_progress($queue_id, $progress, $processed_chunks) {
        global $wpdb;
        return $wpdb->update($this->queue_table, array('progress' => $progress, 'processed_chunks' => $processed_chunks), array('id' => $queue_id), array('%d', '%d'), array('%d'));
    }

    private function update_queue_chunks($queue_id, $total_chunks, $processed_chunks) {
        global $wpdb;
        return $wpdb->update($this->queue_table, array('total_chunks' => $total_chunks, 'processed_chunks' => $processed_chunks), array('id' => $queue_id), array('%d', '%d'), array('%d'));
    }

    public function get_queue_status() {
        global $wpdb;
        $stats = $wpdb->get_row("SELECT COUNT(*) as total, SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending, SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing, SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed, SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed FROM {$this->queue_table}");
        return array('total' => intval($stats->total ?? 0), 'pending' => intval($stats->pending ?? 0), 'processing' => intval($stats->processing ?? 0), 'completed' => intval($stats->completed ?? 0), 'failed' => intval($stats->failed ?? 0));
    }

    public function get_queue_items($limit = 50, $offset = 0) {
        global $wpdb;
        return $wpdb->get_results($wpdb->prepare("SELECT * FROM {$this->queue_table} ORDER BY created_at DESC LIMIT %d OFFSET %d", $limit, $offset));
    }

    public function clear_completed_items() {
        global $wpdb;
        return $wpdb->delete($this->queue_table, array('status' => 'completed'), array('%s'));
    }

    public function delete_queue_item($queue_id) {
        global $wpdb;
        return $wpdb->delete($this->queue_table, array('id' => $queue_id), array('%d'));
    }

    public function process_next_in_queue() {
        global $wpdb;
        $next_item = $wpdb->get_row("SELECT id FROM {$this->queue_table} WHERE status = 'pending' ORDER BY created_at ASC LIMIT 1");
        if ($next_item) {
            return $this->process_file($next_item->id);
        }
        return false;
    }

    public function get_index_statistics() {
        try {
            return $this->pinecone_handler->get_index_stats();
        } catch (Exception $e) {
            return array('error' => $e->getMessage());
        }
    }

    public function test_search($query, $top_k = 5) {
        try {
            return $this->pinecone_handler->search_knowledge_base($query, $top_k);
        } catch (Exception $e) {
            return array('error' => $e->getMessage());
        }
    }

    /**
     * Process file upload from admin interface
     */
    public function process_file_upload($file, $title, $category, $namespace = 'leyesycodigos') {
        try {
            // Validate file
            if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
                throw new Exception(__('Archivo no válido', 'lexai'));
            }

            // Check file size (50MB limit)
            $max_size = 50 * 1024 * 1024; // 50MB
            if ($file['size'] > $max_size) {
                throw new Exception(__('El archivo es demasiado grande. Máximo 50MB.', 'lexai'));
            }

            // Check file type
            $allowed_types = array('pdf', 'doc', 'docx', 'txt');
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

            if (!in_array($file_extension, $allowed_types)) {
                throw new Exception(__('Tipo de archivo no soportado. Formatos permitidos: PDF, DOC, DOCX, TXT', 'lexai'));
            }

            // Move file to uploads directory
            $upload_dir = wp_upload_dir();
            $target_dir = $upload_dir['basedir'] . '/lexai-documents/';

            if (!file_exists($target_dir)) {
                wp_mkdir_p($target_dir);
            }

            $target_file = $target_dir . uniqid() . '_' . sanitize_file_name($file['name']);

            if (!move_uploaded_file($file['tmp_name'], $target_file)) {
                throw new Exception(__('Error al mover el archivo', 'lexai'));
            }

            // Prepare metadata
            $metadata = array(
                'title' => sanitize_text_field($title),
                'category' => sanitize_text_field($category),
                'namespace' => sanitize_text_field($namespace),
                'original_filename' => $file['name'],
                'uploaded_by' => get_current_user_id(),
                'uploaded_at' => current_time('mysql')
            );

            // Add to processing queue
            $queue_id = $this->add_to_queue($target_file, $metadata);

            return array(
                'success' => true,
                'queue_id' => $queue_id,
                'message' => __('Archivo añadido a la cola de procesamiento', 'lexai')
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
}