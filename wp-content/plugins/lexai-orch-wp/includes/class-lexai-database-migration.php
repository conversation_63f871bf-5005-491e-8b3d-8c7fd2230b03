<?php
/**
 * LexAI Database Migration Handler
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Database Migration Class
 */
class LexAI_Database_Migration {

    /**
     * Current migration version
     */
    const CURRENT_VERSION = '1.3.0';

    /**
     * Migration option key
     */
    const MIGRATION_OPTION = 'lexai_db_version';

    /**
     * Run migrations if needed
     */
    public static function run_migrations() {
        $current_version = get_option(self::MIGRATION_OPTION, '1.0.0');
        
        if (version_compare($current_version, self::CURRENT_VERSION, '<')) {
            if (version_compare($current_version, '1.1.0', '<')) {
                self::migrate_to_1_1_0();
            }
            if (version_compare($current_version, '1.2.0', '<')) {
                self::migrate_to_1_2_0();
            }
            if (version_compare($current_version, '1.3.0', '<')) {
                self::migrate_to_1_3_0();
            }
            update_option(self::MIGRATION_OPTION, self::CURRENT_VERSION);
            error_log('LexAI: Database migrated to version ' . self::CURRENT_VERSION);
        }
    }

    /**
     * Migration to version 1.1.0 - Audio format improvements
     */
    private static function migrate_to_1_1_0() {
        global $wpdb;
        
        $audio_table = $wpdb->prefix . 'lexai_audio_files';
        
        // Check if table exists
        $table_exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = %s AND table_name = %s",
            DB_NAME,
            $audio_table
        ));
        
        if (!$table_exists) {
            error_log('LexAI Migration: Audio table does not exist, skipping migration');
            return;
        }
        
        // Check if columns already exist
        $columns = $wpdb->get_results("DESCRIBE $audio_table");
        $existing_columns = array_column($columns, 'Field');
        
        $migrations_needed = array();
        
        if (!in_array('pcm_file_path', $existing_columns)) {
            $migrations_needed[] = "ADD COLUMN pcm_file_path varchar(500) DEFAULT NULL";
        }
        
        if (!in_array('pcm_file_url', $existing_columns)) {
            $migrations_needed[] = "ADD COLUMN pcm_file_url varchar(500) DEFAULT NULL";
        }
        
        if (!in_array('audio_format', $existing_columns)) {
            $migrations_needed[] = "ADD COLUMN audio_format varchar(100) DEFAULT 'audio/L16;codec=pcm;rate=24000'";
        }
        
        // Execute migrations
        if (!empty($migrations_needed)) {
            $sql = "ALTER TABLE $audio_table " . implode(', ', $migrations_needed);
            
            $result = $wpdb->query($sql);
            
            if ($result === false) {
                error_log('LexAI Migration Error: ' . $wpdb->last_error);
                throw new Exception('Failed to migrate audio table: ' . $wpdb->last_error);
            }
            
            error_log('LexAI Migration: Successfully added audio format columns');
        } else {
            error_log('LexAI Migration: Audio table already up to date');
        }
        
        // Update existing records with default audio format
        $updated = $wpdb->update(
            $audio_table,
            array('audio_format' => 'audio/L16;codec=pcm;rate=24000'),
            array('audio_format' => null),
            array('%s'),
            array('%s')
        );
        
        if ($updated !== false && $updated > 0) {
            error_log("LexAI Migration: Updated $updated existing audio records with format info");
        }
    }

    /**
     * Migration to version 1.2.0 - Add max_output_tokens to agents table
     */
    private static function migrate_to_1_2_0() {
        global $wpdb;

        $agents_table = $wpdb->prefix . LEXAI_AGENTS_TABLE;

        // Check if max_output_tokens column exists
        $column_exists = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = 'max_output_tokens'",
                DB_NAME,
                $agents_table
            )
        );

        if (empty($column_exists)) {
            $sql = "ALTER TABLE $agents_table ADD COLUMN max_output_tokens int(11) DEFAULT 8192 AFTER model";

            $result = $wpdb->query($sql);

            if ($result === false) {
                error_log('LexAI Migration Error: ' . $wpdb->last_error);
                throw new Exception('Failed to add max_output_tokens column: ' . $wpdb->last_error);
            }

            error_log('LexAI Migration: Successfully added max_output_tokens column to agents table');
        } else {
            error_log('LexAI Migration: max_output_tokens column already exists');
        }
    }

    /**
     * Migration to version 1.3.0 - Add metadata column to messages table for async streaming
     */
    private static function migrate_to_1_3_0() {
        global $wpdb;
        $messages_table = $wpdb->prefix . LEXAI_MESSAGES_TABLE;

        // Comprobar si la columna metadata ya existe
        $column_exists = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = %s AND TABLE_NAME = %s AND COLUMN_NAME = 'metadata'",
                DB_NAME,
                $messages_table
            )
        );

        if (empty($column_exists)) {
            $sql = "ALTER TABLE $messages_table ADD COLUMN metadata JSON DEFAULT NULL AFTER content;";
            $result = $wpdb->query($sql);

            if ($result === false) {
                error_log('LexAI Migration Error: ' . $wpdb->last_error);
                throw new Exception('Failed to add metadata column: ' . $wpdb->last_error);
            }

            error_log('LexAI Migration: Successfully added metadata column to messages table.');
        } else {
            error_log('LexAI Migration: metadata column already exists');
        }
    }

    /**
     * Get current database version
     */
    public static function get_current_version() {
        return get_option(self::MIGRATION_OPTION, '1.0.0');
    }

    /**
     * Force migration (for testing)
     */
    public static function force_migration() {
        delete_option(self::MIGRATION_OPTION);
        self::run_migrations();
    }
}

// Hook into WordPress init to run migrations
add_action('init', array('LexAI_Database_Migration', 'run_migrations'), 5);