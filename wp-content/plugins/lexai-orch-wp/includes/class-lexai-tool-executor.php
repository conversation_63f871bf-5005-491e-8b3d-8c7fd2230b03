<?php
/**
 * LexAI Tool Executor Class - Native Only Implementation
 * 
 * Simplified tool executor that uses only the native PHP MCP implementation.
 * All Node.js dependencies have been removed for better performance and reliability.
 *
 * @package LexAI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Tool Executor Class - 100% Native PHP
 * 
 * Handles execution of MCP tools using only the native PHP implementation.
 * Legacy Node.js support has been completely removed.
 */
class LexAI_Tool_Executor {

    /**
     * Native MCP Manager instance
     */
    private $native_mcp_manager;

    /**
     * Tools Manager instance
     */
    private $tools_manager;

    /**
     * Tool execution cache
     */
    private static $execution_cache = array();

    /**
     * Cache TTL in seconds
     */
    private const CACHE_TTL = 300; // 5 minutes

    /**
     * Constructor
     */
    public function __construct() {
        $this->tools_manager = new LexAI_Tools_Manager();

        // Initialize native MCP manager
        try {
            require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/class-lexai-mcp-manager-native.php';
            $this->native_mcp_manager = new LexAI_MCP_Manager_Native(array(
                'debug' => false,
                'cache_enabled' => true
            ));
            error_log("LexAI Tool Executor: Native MCP manager initialized (native-only mode)");
        } catch (Exception $e) {
            error_log("LexAI Tool Executor: Native MCP initialization failed: " . $e->getMessage());
            throw new Exception("Failed to initialize native MCP manager: " . $e->getMessage());
        }
    }

    /**
     * Execute tool using native implementation only
     */
    public function execute_tool($tool_name, $args, $agent_id) {
        try {
            // Check if agent can use this tool
            if (!$this->tools_manager->can_agent_use_tool($agent_id, $tool_name)) {
                throw new Exception("Agent not authorized to use tool: $tool_name");
            }

            // Validate and sanitize arguments from LLM
            $validated_args = $this->validate_and_sanitize_args($tool_name, $args);

            // Check cache first
            $cache_key = $this->get_cache_key($tool_name, $validated_args);
            if (isset(self::$execution_cache[$cache_key])) {
                $cached_result = self::$execution_cache[$cache_key];
                if (time() - $cached_result['timestamp'] < self::CACHE_TTL) {
                    error_log("LexAI Tool Executor: Using cached result for $tool_name");
                    return $cached_result;
                }
                // Remove expired cache entry
                unset(self::$execution_cache[$cache_key]);
            }

            // Execute using native implementation
            $result = $this->execute_native_tool($tool_name, $validated_args, $agent_id);

            // Cache successful results
            if ($result['success']) {
                $result['timestamp'] = time();
                self::$execution_cache[$cache_key] = $result;
            }

            // Log execution
            $this->log_tool_execution($tool_name, $validated_args, $result, $agent_id);

            return $result;

        } catch (Exception $e) {
            error_log("LexAI Tool Executor: Tool execution failed - " . $e->getMessage());
            
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'tool' => $tool_name,
                'timestamp' => time()
            );
        }
    }

    /**
     * Execute tool using native implementation
     */
    private function execute_native_tool($tool_name, $validated_args, $agent_id) {
        // Map tool names to native equivalents
        $native_tool_mapping = array(
            'search-records' => 'pinecone_search_native',
            'legal_research' => 'web_scraper_native',
            'google_search' => 'web_scraper_native'
        );

        $native_tool_name = $native_tool_mapping[$tool_name] ?? $tool_name;

        // Prepare parameters for native tools
        $native_params = $this->prepare_native_parameters($tool_name, $validated_args);

        // Execute using native manager
        $result = $this->native_mcp_manager->execute_tool($native_tool_name, $native_params);

        return array(
            'success' => true,
            'data' => $result,
            'tool' => $tool_name,
            'execution_type' => 'native',
            'timestamp' => time()
        );
    }

    /**
     * Validate and sanitize tool arguments
     */
    private function validate_and_sanitize_args($tool_name, $args) {
        if (!is_array($args)) {
            throw new Exception("Tool arguments must be an array");
        }

        $sanitized = array();
        
        foreach ($args as $key => $value) {
            // Sanitize key
            $clean_key = sanitize_key($key);
            
            // Sanitize value based on type
            if (is_string($value)) {
                $sanitized[$clean_key] = sanitize_text_field($value);
            } elseif (is_array($value)) {
                $sanitized[$clean_key] = array_map('sanitize_text_field', $value);
            } elseif (is_numeric($value)) {
                $sanitized[$clean_key] = $value;
            } else {
                $sanitized[$clean_key] = sanitize_text_field(strval($value));
            }
        }

        return $sanitized;
    }

    /**
     * Prepare parameters for native tools
     */
    private function prepare_native_parameters($tool_name, $args) {
        // Convert legacy parameter names to native equivalents
        $param_mapping = array(
            'search-records' => array(
                'query' => 'query',
                'namespace' => 'namespace',
                'top_k' => 'top_k'
            ),
            'legal_research' => array(
                'query' => 'search_query',
                'url' => 'target_url'
            )
        );

        if (isset($param_mapping[$tool_name])) {
            $mapped_params = array();
            foreach ($param_mapping[$tool_name] as $old_key => $new_key) {
                if (isset($args[$old_key])) {
                    $mapped_params[$new_key] = $args[$old_key];
                }
            }
            return $mapped_params;
        }

        return $args;
    }

    /**
     * Generate cache key for tool execution
     */
    private function get_cache_key($tool_name, $args) {
        return 'lexai_tool_' . md5($tool_name . serialize($args));
    }

    /**
     * Log tool execution for debugging and monitoring
     */
    private function log_tool_execution($tool_name, $args, $result, $agent_id) {
        $log_data = array(
            'tool' => $tool_name,
            'agent_id' => $agent_id,
            'success' => $result['success'],
            'execution_type' => 'native',
            'timestamp' => current_time('mysql')
        );

        if (!$result['success']) {
            $log_data['error'] = $result['error'] ?? 'Unknown error';
        }

        error_log("LexAI Tool Execution: " . json_encode($log_data));
    }

    /**
     * Get available tools
     */
    public function get_available_tools() {
        if ($this->native_mcp_manager) {
            return $this->native_mcp_manager->get_available_tools();
        }
        return array();
    }

    /**
     * Test tool execution
     */
    public function test_tool($tool_name) {
        try {
            $test_args = $this->get_test_args_for_tool($tool_name);
            return $this->execute_tool($tool_name, $test_args, 1); // Use agent ID 1 for testing
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage(),
                'tool' => $tool_name
            );
        }
    }

    /**
     * Get test arguments for a specific tool
     */
    private function get_test_args_for_tool($tool_name) {
        $test_args = array(
            'search-records' => array(
                'query' => 'test query',
                'namespace' => 'legal-docs',
                'top_k' => 3
            ),
            'legal_research' => array(
                'query' => 'derecho civil'
            )
        );

        return $test_args[$tool_name] ?? array();
    }

    /**
     * Clear execution cache
     */
    public function clear_cache() {
        self::$execution_cache = array();
        error_log("LexAI Tool Executor: Cache cleared");
    }
}
