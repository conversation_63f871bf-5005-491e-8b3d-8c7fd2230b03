<?php
/**
 * LexAI AJAX Handler
 * Centralized AJAX request handling for the LexAI plugin
 *
 * @package LexAI
 * @since 1.0.0
 */

class LexAI_Ajax {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize AJAX hooks
     */
    private function init_hooks() {
        // Admin AJAX endpoints
        add_action('wp_ajax_lexai_test_api_key', array($this, 'handle_test_api_key'));
        add_action('wp_ajax_lexai_test_api', array($this, 'handle_test_api'));
        add_action('wp_ajax_lexai_save_api_key', array($this, 'handle_save_api_key'));
        add_action('wp_ajax_lexai_save_pinecone_config', array($this, 'handle_save_pinecone_config'));
        add_action('wp_ajax_lexai_delete_api_key', array($this, 'handle_delete_api_key'));
        add_action('wp_ajax_lexai_save_agent', array($this, 'handle_save_agent'));
        add_action('wp_ajax_lexai_delete_agent', array($this, 'handle_delete_agent'));
        add_action('wp_ajax_lexai_load_template', array($this, 'handle_load_template'));
        add_action('wp_ajax_lexai_create_specialized_agents', array($this, 'handle_create_specialized_agents'));
        add_action('wp_ajax_lexai_get_agent_tools', array($this, 'handle_get_agent_tools'));
        add_action('wp_ajax_lexai_sync_mcp_tools', array($this, 'handle_sync_mcp_tools'));
        add_action('wp_ajax_lexai_test_pinecone', array($this, 'handle_test_pinecone'));
        add_action('wp_ajax_lexai_upload_knowledge', array($this, 'handle_upload_knowledge'));
        add_action('wp_ajax_lexai_delete_knowledge', array($this, 'handle_delete_knowledge'));
        add_action('wp_ajax_lexai_get_usage_stats', array($this, 'handle_get_usage_stats'));
        add_action('wp_ajax_lexai_reset_usage', array($this, 'handle_reset_usage'));
        add_action('wp_ajax_lexai_test_google_search', array($this, 'handle_test_google_search'));
        
        // Public AJAX endpoints (for logged-in users)
        // Chat process with polling
        add_action('wp_ajax_lexai_start_chat_processing', array($this, 'handle_start_chat_processing'));
        add_action('wp_ajax_lexai_check_chat_status', array($this, 'handle_check_chat_status'));

        add_action('wp_ajax_lexai_get_conversations', array($this, 'handle_get_conversations'));
        add_action('wp_ajax_lexai_load_conversations', array($this, 'handle_get_conversations')); // Alias
        add_action('wp_ajax_lexai_load_conversation', array($this, 'handle_load_conversation'));
        add_action('wp_ajax_lexai_create_conversation', array($this, 'handle_create_conversation'));
        add_action('wp_ajax_lexai_delete_conversation_ajax', array($this, 'handle_delete_conversation'));
        add_action('wp_ajax_lexai_export_single_message', array($this, 'handle_export_single_message'));
        add_action('wp_ajax_lexai_generate_tts', array($this, 'handle_generate_tts'));
        add_action('wp_ajax_lexai_process_stt', array($this, 'handle_process_stt'));
        
        // Non-privileged AJAX endpoints (for non-logged-in users)
        add_action('wp_ajax_nopriv_lexai_chat', array($this, 'handle_chat_message'));
        add_action('wp_ajax_nopriv_lexai_get_conversations', array($this, 'handle_get_conversations'));
        add_action('wp_ajax_nopriv_lexai_load_conversations', array($this, 'handle_get_conversations')); // Alias
        add_action('wp_ajax_nopriv_lexai_load_conversation', array($this, 'handle_load_conversation'));
    }
    
    /**
     * Handle API key testing
     */
    public function handle_test_api_key() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            $api_key = sanitize_text_field($_POST['api_key']);
            $provider = sanitize_text_field($_POST['provider']);
            
            if (empty($api_key) || empty($provider)) {
                wp_send_json_error(__('API key y proveedor son requeridos', 'lexai'));
                return;
            }
            
            // Test the API key
            $api_handler = new LexAI_API_Handler();

            if ($provider === 'gemini') {
                $test_result = $api_handler->test_api_key($api_key);
            } else {
                // For other providers, we'll implement later
                $test_result = array('success' => true, 'message' => 'API key format accepted');
            }

            if ($test_result['success']) {
                wp_send_json_success($test_result['message']);
            } else {
                wp_send_json_error($test_result['message']);
            }
            
        } catch (Exception $e) {
            error_log('LexAI Test API Key Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al probar API key', 'lexai'));
        }
    }

    /**
     * Handle API testing from button (uses saved API key)
     */
    public function handle_test_api() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }

            $api_type = sanitize_text_field($_POST['api_type']);

            if (empty($api_type)) {
                wp_send_json_error(__('Tipo de API es requerido', 'lexai'));
                return;
            }

            // Get saved API key
            $api_handler = new LexAI_API_Handler();
            $active_key = $api_handler->get_active_api_key($api_type);

            if (!$active_key || empty($active_key->api_key)) {
                wp_send_json_error(__('No hay API key configurada para ' . $api_type, 'lexai'));
                return;
            }

            // Test the API key
            if ($api_type === 'gemini') {
                $test_result = $api_handler->test_api_key($active_key->api_key);
            } else {
                // For other providers, we'll implement later
                $test_result = array('success' => true, 'message' => 'API key configurada correctamente');
            }

            if ($test_result['success']) {
                wp_send_json_success(array('message' => $test_result['message']));
            } else {
                wp_send_json_error($test_result['message']);
            }

        } catch (Exception $e) {
            error_log('LexAI Test API Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al probar API: ' . $e->getMessage(), 'lexai'));
        }
    }

    /**
     * Handle API key saving
     */
    public function handle_save_api_key() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }

            $api_key = sanitize_text_field($_POST['api_key']);
            $provider = sanitize_text_field($_POST['provider']);
            $name = sanitize_text_field($_POST['name']);

            if (empty($api_key) || empty($provider)) {
                wp_send_json_error(__('API key y proveedor son requeridos', 'lexai'));
                return;
            }

            if (empty($name)) {
                $name = ucfirst($provider) . ' API Key';
            }

            // Save the API key
            $api_handler = new LexAI_API_Handler();
            $result = $api_handler->add_api_key($name, $api_key, $provider);

            if ($result['success']) {
                wp_send_json_success($result['message']);
            } else {
                wp_send_json_error($result['message']);
            }

        } catch (Exception $e) {
            error_log('LexAI Save API Key Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al guardar API key: ' . $e->getMessage(), 'lexai'));
        }
    }

    /**
     * Handle Pinecone configuration saving
     */
    public function handle_save_pinecone_config() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }

            $api_key = sanitize_text_field($_POST['api_key']);
            $host = sanitize_text_field($_POST['host']);
            $index_name = sanitize_text_field($_POST['index_name']);

            if (empty($api_key)) {
                wp_send_json_error(__('API key es requerida', 'lexai'));
                return;
            }

            if (empty($host)) {
                wp_send_json_error(__('Host del índice es requerido', 'lexai'));
                return;
            }

            if (empty($index_name)) {
                $index_name = 'lexai-knowledge-base';
            }

            // Save as API key in the unified system
            $api_handler = new LexAI_API_Handler();
            $result = $api_handler->add_api_key('Pinecone API Key', $api_key, 'pinecone');

            if ($result['success']) {
                // Also save additional Pinecone settings
                $settings = get_option('lexai_settings', array());
                $settings['pinecone'] = array(
                    'host' => $host,
                    'index_name' => $index_name,
                    'api_key' => $api_key // Keep for backward compatibility
                );
                update_option('lexai_settings', $settings);

                wp_send_json_success(__('Configuración de Pinecone guardada exitosamente', 'lexai'));
            } else {
                wp_send_json_error($result['message']);
            }

        } catch (Exception $e) {
            error_log('LexAI Save Pinecone Config Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al guardar configuración de Pinecone: ' . $e->getMessage(), 'lexai'));
        }
    }

    /**
     * Handle API key deletion
     */
    public function handle_delete_api_key() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }

            $provider = sanitize_text_field($_POST['provider']);

            if (empty($provider)) {
                wp_send_json_error(__('Proveedor es requerido', 'lexai'));
                return;
            }

            global $wpdb;
            $api_keys_table = $wpdb->prefix . LEXAI_API_KEYS_TABLE;

            $result = $wpdb->delete(
                $api_keys_table,
                array('provider' => $provider),
                array('%s')
            );

            if ($result !== false) {
                wp_send_json_success(__('API key eliminada exitosamente', 'lexai'));
            } else {
                wp_send_json_error(__('Error al eliminar API key', 'lexai'));
            }

        } catch (Exception $e) {
            error_log('LexAI Delete API Key Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al eliminar API key: ' . $e->getMessage(), 'lexai'));
        }
    }
    
    /**
     * Handle agent saving
     */
    public function handle_save_agent() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            // Collect agent tools
            $agent_tools = array();
            if (isset($_POST['agent_tools']) && is_array($_POST['agent_tools'])) {
                $agent_tools = array_map('sanitize_text_field', $_POST['agent_tools']);
            }
            
            $agent_data = array(
                'name' => sanitize_text_field($_POST['name']),
                'description' => sanitize_textarea_field($_POST['description']),
                'system_instruction' => sanitize_textarea_field($_POST['system_instruction']),
                'model' => sanitize_text_field($_POST['model']),
                'max_output_tokens' => intval($_POST['max_output_tokens']),
                'status' => sanitize_text_field($_POST['status']),
                'tools' => $agent_tools
            );
            
            // Validate required fields
            if (empty($agent_data['name']) || empty($agent_data['system_instruction'])) {
                wp_send_json_error(__('Nombre e instrucciones del sistema son requeridos', 'lexai'));
                return;
            }
            
            // Validate max_output_tokens range
            if ($agent_data['max_output_tokens'] < 1 || $agent_data['max_output_tokens'] > 32768) {
                wp_send_json_error(__('El máximo de tokens de salida debe estar entre 1 y 32768', 'lexai'));
                return;
            }
            
            $db = new LexAI_DB();
            $agent_id = isset($_POST['agent_id']) ? intval($_POST['agent_id']) : null;
            
            $result = $db->save_agent($agent_data, $agent_id);
            
            if ($result) {
                // Save agent tools relationships
                if (!empty($agent_tools)) {
                    $tools_manager = new LexAI_Tools_Manager();
                    $tools_manager->save_agent_tools($result, $agent_tools);
                }
                
                $message = $agent_id ? __('Agente actualizado exitosamente', 'lexai') : __('Agente creado exitosamente', 'lexai');
                
                wp_send_json_success(array(
                    'message' => $message,
                    'agent_id' => $result
                ));
            } else {
                wp_send_json_error(__('Error al guardar agente', 'lexai'));
            }
            
        } catch (Exception $e) {
            error_log('LexAI Save Agent Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al guardar agente: ' . $e->getMessage(), 'lexai'));
        }
    }
    
    /**
     * Handle agent deletion
     */
    public function handle_delete_agent() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            $agent_id = intval($_POST['agent_id']);
            
            if (!$agent_id) {
                wp_send_json_error(__('ID de agente inválido', 'lexai'));
                return;
            }
            
            $db = new LexAI_DB();
            $result = $db->delete_agent($agent_id);
            
            if ($result) {
                wp_send_json_success(__('Agente eliminado exitosamente', 'lexai'));
            } else {
                wp_send_json_error(__('Error al eliminar agente', 'lexai'));
            }
            
        } catch (Exception $e) {
            error_log('LexAI Delete Agent Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al eliminar agente', 'lexai'));
        }
    }
    
    /**
     * Handle Pinecone testing
     */
    public function handle_test_pinecone() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            $pinecone_handler = new LexAI_Pinecone_Handler();
            $test_result = $pinecone_handler->test_connection();
            
            if ($test_result['success']) {
                wp_send_json_success(__('Conexión a Pinecone exitosa', 'lexai'));
            } else {
                wp_send_json_error($test_result['error']);
            }
            
        } catch (Exception $e) {
            error_log('LexAI Test Pinecone Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al probar conexión Pinecone', 'lexai'));
        }
    }
    
    /**
     * Handle knowledge upload
     */
    public function handle_upload_knowledge() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            // Check if file was uploaded
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                wp_send_json_error(__('Error al subir archivo', 'lexai'));
                return;
            }
            
            $file = $_FILES['file'];
            $title = sanitize_text_field($_POST['title']);
            $category = sanitize_text_field($_POST['category']);
            
            // Process the file and add to knowledge base
            $vector_manager = new LexAI_Vector_Manager(
                new LexAI_Pinecone_Handler(),
                new LexAI_API_Handler()
            );
            
            $result = $vector_manager->process_file_upload($file, $title, $category);
            
            if ($result['success']) {
                wp_send_json_success(__('Archivo procesado y añadido a la base de conocimientos', 'lexai'));
            } else {
                wp_send_json_error($result['error']);
            }
            
        } catch (Exception $e) {
            error_log('LexAI Upload Knowledge Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al procesar archivo', 'lexai'));
        }
    }
    
    /**
     * Handle knowledge deletion
     */
    public function handle_delete_knowledge() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            $vector_id = sanitize_text_field($_POST['vector_id']);
            
            if (empty($vector_id)) {
                wp_send_json_error(__('ID de vector inválido', 'lexai'));
                return;
            }
            
            $pinecone_handler = new LexAI_Pinecone_Handler();
            $result = $pinecone_handler->delete_vector($vector_id);
            
            if ($result['success']) {
                wp_send_json_success(__('Conocimiento eliminado exitosamente', 'lexai'));
            } else {
                wp_send_json_error($result['error']);
            }
            
        } catch (Exception $e) {
            error_log('LexAI Delete Knowledge Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al eliminar conocimiento', 'lexai'));
        }
    }
    
    /**
     * Handle usage stats retrieval
     */
    public function handle_get_usage_stats() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            $period = sanitize_text_field($_POST['period']) ?: 'week';
            $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : null;
            
            $db = new LexAI_DB();
            $stats = $db->get_usage_statistics($period, $user_id);
            
            wp_send_json_success($stats);
            
        } catch (Exception $e) {
            error_log('LexAI Get Usage Stats Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al obtener estadísticas', 'lexai'));
        }
    }
    
    /**
     * Handle usage reset
     */
    public function handle_reset_usage() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : null;
            
            $usage_limiter = new LexAI_Usage_Limiter(new LexAI_DB());
            
            if ($user_id) {
                $result = $usage_limiter->reset_usage($user_id);
                $message = __('Uso del usuario reiniciado', 'lexai');
            } else {
                $result = $usage_limiter->reset_all_usage();
                $message = __('Uso de todos los usuarios reiniciado', 'lexai');
            }
            
            if ($result) {
                wp_send_json_success($message);
            } else {
                wp_send_json_error(__('Error al reiniciar uso', 'lexai'));
            }
            
        } catch (Exception $e) {
            error_log('LexAI Reset Usage Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al reiniciar uso', 'lexai'));
        }
    }
    
    /**
     * Handle Google Search testing
     */
    public function handle_test_google_search() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_die(__('Acceso no autorizado', 'lexai'));
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }
            
            $query = sanitize_text_field($_POST['query']) ?: 'test search';
            
            $google_search_agent = new LexAI_Google_Search_Agent(
                new LexAI_API_Handler(),
                new LexAI_Usage_Limiter(new LexAI_DB())
            );
            
            $result = $google_search_agent->search($query);
            
            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => __('Búsqueda exitosa', 'lexai'),
                    'results' => $result['results']
                ));
            } else {
                wp_send_json_error($result['error']);
            }
            
        } catch (Exception $e) {
            error_log('LexAI Test Google Search Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al probar Google Search', 'lexai'));
        }
    }
    
    /**
     * Handle chat message (legacy endpoint for compatibility)
     */
    public function handle_chat_message() {
        // This is now a legacy function. We forward to the new system.
        $this->handle_start_chat_processing();
    }

    /**
     * Starts the chat processing task.
     * Creates a task in the DB and returns a task ID to the client.
     */
    public function handle_start_chat_processing() {
        try {
            // Nonce verification - use fullpage nonce for consistency
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_fullpage_nonce')) {
                wp_send_json_error(['message' => __('Invalid security token.', 'lexai')], 403);
                return;
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => __('Authentication required.', 'lexai')], 401);
                return;
            }

            $message = sanitize_textarea_field($_POST['message']);
            $conversation_id = intval($_POST['conversation_id']);

            if (empty($message) || !$conversation_id) {
                wp_send_json_error(['message' => __('Invalid message or conversation ID.', 'lexai')], 400);
                return;
            }

            // Create a new task
            $db = new LexAI_DB();
            $task_id = $db->create_chat_task($conversation_id, $message);

            if (!$task_id) {
                wp_send_json_error(['message' => __('Could not create processing task.', 'lexai')], 500);
                return;
            }
            
            // Schedule a one-off background event to process the task
            wp_schedule_single_event(time(), 'lexai_process_chat_task', ['task_id' => $task_id]);

            wp_send_json_success([
                'task_id' => $task_id,
                'message' => __('Task started.', 'lexai')
            ]);

        } catch (Exception $e) {
            error_log('LexAI Start Chat Processing Error: ' . $e->getMessage());
            wp_send_json_error(['message' => __('Error starting chat processing.', 'lexai')], 500);
        }
    }

    /**
     * Checks the status of a chat processing task.
     * Returns any new messages since the last check.
     */
    public function handle_check_chat_status() {
        try {
            // Nonce verification - use fullpage nonce for consistency
            if (!wp_verify_nonce($_GET['nonce'], 'lexai_fullpage_nonce')) {
                wp_send_json_error(['message' => __('Invalid security token.', 'lexai')], 403);
                return;
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => __('Authentication required.', 'lexai')], 401);
                return;
            }

            $task_id = intval($_GET['task_id']);
            $last_message_id = isset($_GET['last_message_id']) ? intval($_GET['last_message_id']) : 0;

            if (!$task_id) {
                wp_send_json_error(['message' => __('Invalid task ID.', 'lexai')], 400);
                return;
            }

            $db = new LexAI_DB();
            $task = $db->get_chat_task($task_id);

            if (!$task) {
                wp_send_json_error(['message' => __('Task not found.', 'lexai')], 404);
                return;
            }
            
            // Get new messages for the conversation associated with the task
            $new_messages = $db->get_new_messages_for_conversation($task->conversation_id, $last_message_id);

            wp_send_json_success([
                'status' => $task->status,
                'messages' => $new_messages
            ]);

        } catch (Exception $e) {
            error_log('LexAI Check Chat Status Error: ' . $e->getMessage());
            wp_send_json_error(['message' => __('Error checking chat status.', 'lexai')], 500);
        }
    }
    
    /**
     * Handle get conversations
     */
    public function handle_get_conversations() {
        try {
            $user_id = get_current_user_id();
            
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }
            
            $db = new LexAI_DB();
            $conversations = $db->get_user_conversations($user_id);
            
            wp_send_json_success($conversations);
            
        } catch (Exception $e) {
            error_log('LexAI Get Conversations Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al cargar conversaciones', 'lexai'));
        }
    }

    /**
     * Handle load conversation (specific conversation with messages)
     */
    public function handle_load_conversation() {
        try {
            $user_id = get_current_user_id();
            $conversation_id = intval($_POST['conversation_id']);

            if (!$conversation_id) {
                wp_send_json_error(__('ID de conversación inválido', 'lexai'));
                return;
            }

            $db = new LexAI_DB();

            // Get conversation details
            $conversation = $db->get_conversation($conversation_id);
            if (!$conversation) {
                wp_send_json_error(__('Conversación no encontrada', 'lexai'));
                return;
            }

            // Check if user owns this conversation (if logged in)
            if ($user_id && $conversation->user_id != $user_id) {
                wp_send_json_error(__('No tienes permisos para ver esta conversación', 'lexai'));
                return;
            }

            // Get messages
            $messages = $db->get_conversation_messages($conversation_id);

            wp_send_json_success(array(
                'conversation' => $conversation,
                'messages' => $messages
            ));

        } catch (Exception $e) {
            error_log('LexAI Load Conversation Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al cargar conversación', 'lexai'));
        }
    }
    
    /**
     * Handle create conversation
     */
    public function handle_create_conversation() {
        try {
            $user_id = get_current_user_id();
            
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }
            
            $title = sanitize_text_field($_POST['title']) ?: 'Nueva conversación';
            
            $db = new LexAI_DB();
            $conversation_id = $db->create_conversation($user_id, $title);
            
            if ($conversation_id) {
                wp_send_json_success(array(
                    'conversation_id' => $conversation_id,
                    'title' => $title
                ));
            } else {
                wp_send_json_error(__('Error al crear conversación', 'lexai'));
            }
            
        } catch (Exception $e) {
            error_log('LexAI Create Conversation Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al crear conversación', 'lexai'));
        }
    }
    
    /**
     * Handle delete conversation
     */
    public function handle_delete_conversation() {
        try {
            $user_id = get_current_user_id();
            
            if (!$user_id) {
                wp_send_json_error(__('Debes iniciar sesión', 'lexai'));
                return;
            }
            
            $conversation_id = intval($_POST['conversation_id']);
            
            if (!$conversation_id) {
                wp_send_json_error(__('ID de conversación inválido', 'lexai'));
                return;
            }
            
            $db = new LexAI_DB();
            $result = $db->delete_conversation($conversation_id, $user_id);
            
            if ($result) {
                wp_send_json_success(__('Conversación eliminada', 'lexai'));
            } else {
                wp_send_json_error(__('Error al eliminar conversación', 'lexai'));
            }
            
        } catch (Exception $e) {
            error_log('LexAI Delete Conversation Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al eliminar conversación', 'lexai'));
        }
    }
    
    /**
     * Get or create temporary user for non-logged sessions
     */
    private function get_or_create_temp_user() {
        // Create a temporary user ID based on session
        if (!session_id()) {
            session_start();
        }
        
        $temp_user_id = 'temp_' . session_id();
        
        // Store in session for consistency
        if (!isset($_SESSION['lexai_temp_user_id'])) {
            $_SESSION['lexai_temp_user_id'] = $temp_user_id;
        }
        
        return $_SESSION['lexai_temp_user_id'];
    }

    /**
     * Handle load template request
     */
    public function handle_load_template() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_send_json_error(__('Token de seguridad inválido', 'lexai'));
                return;
            }

            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }

            $template_key = sanitize_text_field($_POST['template_key']);

            if (empty($template_key)) {
                wp_send_json_error(__('Clave de plantilla requerida', 'lexai'));
                return;
            }

            // Get template from agent factory
            $agent_factory = new LexAI_Agent_Factory();
            $templates = $agent_factory->get_agent_templates();

            if (!isset($templates[$template_key])) {
                wp_send_json_error(__('Plantilla no encontrada', 'lexai'));
                return;
            }

            $template = $templates[$template_key];

            // Return template data
            wp_send_json_success(array(
                'template' => $template,
                'message' => __('Plantilla cargada correctamente', 'lexai')
            ));

        } catch (Exception $e) {
            error_log('LexAI Load Template Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al cargar plantilla', 'lexai'));
        }
    }

    /**
     * Handle create specialized agents request
     */
    public function handle_create_specialized_agents() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_send_json_error(__('Token de seguridad inválido', 'lexai'));
                return;
            }

            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }

            // Force creation of specialized agents
            require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-activator.php';
            LexAI_Activator::force_create_specialized_agents();

            wp_send_json_success(array(
                'message' => __('Agentes especializados creados exitosamente. Recarga la página para verlos.', 'lexai')
            ));

        } catch (Exception $e) {
            error_log('LexAI Create Specialized Agents Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al crear agentes especializados', 'lexai'));
        }
    }

    /**
     * Handle get agent tools request
     */
    public function handle_get_agent_tools() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_send_json_error(__('Token de seguridad inválido', 'lexai'));
                return;
            }

            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }

            $agent_id = intval($_POST['agent_id']);

            if (!$agent_id) {
                wp_send_json_error(__('ID de agente requerido', 'lexai'));
                return;
            }

            // Get agent from database
            $db = new LexAI_DB();
            $agent = $db->get_agent($agent_id);

            if (!$agent) {
                wp_send_json_error(__('Agente no encontrado', 'lexai'));
                return;
            }

            // Get tools from agent data (stored as JSON in tools column)
            $tools = array();
            if (!empty($agent->tools)) {
                $tools = json_decode($agent->tools, true);
                if (!is_array($tools)) {
                    $tools = array();
                }
            }

            wp_send_json_success(array(
                'tools' => $tools,
                'agent_id' => $agent_id
            ));

        } catch (Exception $e) {
            error_log('LexAI Get Agent Tools Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al obtener herramientas del agente', 'lexai'));
        }
    }

    /**
     * Handle sync MCP tools request
     */
    public function handle_sync_mcp_tools() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_admin_nonce')) {
                wp_send_json_error(__('Token de seguridad inválido', 'lexai'));
                return;
            }

            // Check user permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(__('Permisos insuficientes', 'lexai'));
                return;
            }

            // Sync MCP tools to legacy tools table
            $tools_manager = new LexAI_Tools_Manager();
            $synced_count = $tools_manager->sync_mcp_tools_to_legacy();

            if ($synced_count > 0) {
                wp_send_json_success(array(
                    'message' => sprintf(__('Se sincronizaron %d herramientas MCP exitosamente', 'lexai'), $synced_count),
                    'synced_count' => $synced_count
                ));
            } else {
                wp_send_json_success(array(
                    'message' => __('No hay nuevas herramientas MCP para sincronizar', 'lexai'),
                    'synced_count' => 0
                ));
            }

        } catch (Exception $e) {
            error_log('LexAI Sync MCP Tools Error: ' . $e->getMessage());
            wp_send_json_error(__('Error al sincronizar herramientas MCP: ' . $e->getMessage(), 'lexai'));
        }
    }

    /**
     * Handle single message export
     */
    public function handle_export_single_message() {
        try {
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_chat_nonce')) {
                wp_send_json_error(['message' => __('Invalid security token.', 'lexai')], 403);
                return;
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => __('Authentication required.', 'lexai')], 401);
                return;
            }

            $message_id = intval($_POST['message_id']);
            $content = wp_kses_post(wp_unslash($_POST['content'])); // Sanitize content
            $format = sanitize_text_field($_POST['format']);

            if (empty($content) || empty($format)) {
                wp_send_json_error(['message' => __('Content and format are required.', 'lexai')], 400);
                return;
            }

            // Initialize export handler
            $export_handler = new LexAI_Export_Handler();
            $filename = "lexai_message_" . $message_id;

            $result = $export_handler->export_content($content, $format, $filename);

            if ($result['success']) {
                wp_send_json_success(['download_url' => $result['download_url'], 'filename' => $result['filename']]);
            } else {
                wp_send_json_error(['message' => $result['error']]);
            }

        } catch (Exception $e) {
            error_log('LexAI Export Single Message Error: ' . $e->getMessage());
            wp_send_json_error(['message' => __('Error exporting message.', 'lexai')], 500);
        }
    }

    /**
     * Handle Text-to-Speech generation
     */
    public function handle_generate_tts() {
        try {
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_chat_nonce')) {
                wp_send_json_error(['message' => __('Invalid security token.', 'lexai')], 403);
                return;
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => __('Authentication required.', 'lexai')], 401);
                return;
            }

            $text = wp_kses_post(wp_unslash($_POST['text']));
            $message_id = intval($_POST['message_id']);

            if (empty($text)) {
                wp_send_json_error(['message' => __('Text is required for TTS.', 'lexai')], 400);
                return;
            }

            // Initialize TTS handler
            $tts_handler = new LexAI_TTS_Handler();
            $result = $tts_handler->generate_audio($text, $user_id, $message_id);

            if ($result['success']) {
                $db = new LexAI_DB();
                $message = $db->get_message($message_id);
                if ($message) {
                    $metadata = json_decode($message->metadata, true) ?: [];
                    $metadata['audio_url'] = $result['audio_url'];
                    $db->update_message_metadata($message_id, $metadata);
                }
                wp_send_json_success(['audio_url' => $result['audio_url']]);
            } else {
                wp_send_json_error(['message' => $result['error']]);
            }

        } catch (Exception $e) {
            error_log('LexAI Generate TTS Error: ' . $e->getMessage());
            wp_send_json_error(['message' => __('Error generating audio.', 'lexai')], 500);
        }
    }

    /**
     * Handle Speech-to-Text processing
     */
    public function handle_process_stt() {
        try {
            if (!wp_verify_nonce($_POST['nonce'], 'lexai_chat_nonce')) {
                wp_send_json_error(['message' => __('Invalid security token.', 'lexai')], 403);
                return;
            }

            $user_id = get_current_user_id();
            if (!$user_id) {
                wp_send_json_error(['message' => __('Authentication required.', 'lexai')], 401);
                return;
            }

            if (!isset($_FILES['audio']) || $_FILES['audio']['error'] !== UPLOAD_ERR_OK) {
                wp_send_json_error(['message' => __('No audio file uploaded.', 'lexai')], 400);
                return;
            }

            $audio_file = $_FILES['audio'];

            // Initialize STT handler
            $stt_handler = new LexAI_STT_Handler();
            $result = $stt_handler->transcribe_audio($audio_file, $user_id);

            if ($result['success']) {
                wp_send_json_success(['text' => $result['text']]);
            } else {
                wp_send_json_error(['message' => $result['error']]);
            }

        } catch (Exception $e) {
            error_log('LexAI Process STT Error: ' . $e->getMessage());
            wp_send_json_error(['message' => __('Error processing audio for transcription.', 'lexai')], 500);
        }
    }
}