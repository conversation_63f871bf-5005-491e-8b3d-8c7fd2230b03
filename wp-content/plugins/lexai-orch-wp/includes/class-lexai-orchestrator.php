<?php
/**
 * Orchestrator Class - Central brain that coordinates AI agents
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * LexAI Orchestrator Class
 * This class is the core of the plugin, responsible for receiving user queries,
 * analyzing them, creating an execution plan, coordinating agents, and synthesizing
 * a final response.
 */
class LexAI_Orchestrator {

    private $agent_factory;
    private $db;
    private $api_handler;
    private $tools_manager;
    private $tool_executor;

    public function __construct($agent_factory = null, $db = null, $api_handler = null, $tools_manager = null, $tool_executor = null) {
        $this->agent_factory = $agent_factory ?: new LexAI_Agent_Factory();
        $this->db = $db ?: new LexAI_DB();
        $this->api_handler = $api_handler ?: new LexAI_API_Handler();
        $this->tools_manager = $tools_manager ?: new LexAI_Tools_Manager();
        $this->tool_executor = $tool_executor ?: new LexAI_Tool_Executor();
    }
    
    /**
     * Main entry point for processing a user query.
     * LEX-LOGIC-004: Re-architected with intelligent task planning
     * @param int $user_id The ID of the user making the query.
     * @param int $conversation_id The ID of the current conversation.
     * @param string $user_message The user's message.
     * @param array $files An array of uploaded file objects.
     * @return array The final result успеха or error.
     */
    public function process_query($user_id, $conversation_id, $user_message, $files = array()) {
        try {
            // LEX-LOGIC-004: PASO 1 - Crear el plan de ejecución
            $execution_plan = $this->create_execution_plan($user_message, $files);

            // CRITICAL FIX: Ejecutar el plan si existe y es válido, usar fallback solo si está vacío
            if (!empty($execution_plan) && is_array($execution_plan) && count($execution_plan) > 0) {
                // Ejecutar el plan de múltiples tareas
                error_log("LexAI Orchestrator: Executing multi-task plan with " . count($execution_plan) . " tasks");
                return $this->execute_multi_task_plan_with_streaming($user_id, $conversation_id, $user_message, $execution_plan, $files);
            } else {
                // Fallback to simple single-agent processing solo si no hay plan válido
                error_log("LexAI Orchestrator: No valid execution plan created. Falling back to simple query processing.");
                return $this->process_simple_query($user_id, $conversation_id, $user_message, $files);
            }

        } catch (Exception $e) {
            error_log("LexAI Orchestrator: Error in process_query: " . $e->getMessage());

            // Fallback seguro en caso de error
            try {
                return $this->process_simple_query($user_id, $conversation_id, $user_message, $files);
            } catch (Exception $fallback_error) {
                return [
                    'success' => false,
                    'response' => __('Error interno del sistema. Por favor, inténtalo de nuevo.', 'lexai'),
                    'error' => $e->getMessage()
                ];
            }
        }
    }

    /**
     * Execute multi-task execution plan with streaming responses - NEW METHOD
     */
    private function execute_multi_task_plan_with_streaming($user_id, $conversation_id, $user_message, $execution_plan, $files) {
        try {
            // Save the execution plan to the database for the frontend to poll
            $plan_display = $this->format_execution_plan_for_display($execution_plan);
            $this->save_partial_response($conversation_id, $user_id, 'plan', $plan_display);

            $final_results = [];
            $task_context = "";
            $conversation_messages = $this->db->get_conversation_messages($conversation_id);

            foreach ($execution_plan as $task_index => $task) {
                try {
                    $task_start_message = "🔄 **Ejecutando Tarea " . ($task_index + 1) . ":** " . $task['details'];
                    $this->save_partial_response($conversation_id, $user_id, 'task_start', $task_start_message);

                    $agent = $this->select_agent_for_task($task['type'], $task['details']);
                    $agent_message = "👤 **Agente asignado:** " . $agent->name;
                    $this->save_partial_response($conversation_id, $user_id, 'agent_assigned', $agent_message);

                    $task_input = $task['details'];
                    if (!empty($task_context)) {
                        $task_input .= "\n\nContexto de tareas anteriores:\n" . $task_context;
                    }

                    $task_messages = $conversation_messages;
                    $task_messages[] = ['role' => 'user', 'content' => $task_input];

                    $tools = $this->get_agent_tools_schemas($agent->id);
                    $response = $this->api_handler->make_gemini_request(
                        $task_messages,
                        $agent->system_instruction,
                        $tools,
                        $agent->model,
                        $files,
                        $task['type'],
                        array('max_output_tokens' => $agent->max_output_tokens ?? 8192)
                    );

                    $task_result = $this->handle_function_calling($response, $task_messages, $agent, $tools, $files, $user_id);
                    
                    $task_content = is_array($task_result) ? $task_result['content'] : $task_result;
                    $task_tokens = is_array($task_result) ? ($task_result['tokens_used'] ?? 0) : 0;

                    $task_result_message = "✅ **Resultado Tarea " . ($task_index + 1) . ":**\n\n" . $task_content;
                    $this->save_partial_response($conversation_id, $user_id, 'task_result', $task_result_message);

                    $final_results[] = [
                        'task_type' => $task['type'],
                        'task_details' => $task['details'],
                        'agent_used' => $agent->name,
                        'result' => $task_content,
                        'tokens_used' => $task_tokens
                    ];

                    $task_context .= "\n\nTarea " . ($task_index + 1) . " (" . $task['type'] . "): " . substr($task_content, 0, 500) . "...";

                } catch (Exception $task_error) {
                    $error_message = "❌ **Error en Tarea " . ($task_index + 1) . ":** " . $task_error->getMessage();
                    $this->save_partial_response($conversation_id, $user_id, 'task_error', $error_message);
                    $final_results[] = [
                        'task_type' => $task['type'],
                        'task_details' => $task['details'],
                        'agent_used' => 'Error',
                        'result' => 'Error: ' . $task_error->getMessage(),
                        'tokens_used' => 0
                    ];
                }
            }

            $final_response = $this->synthesize_final_response($user_message, $final_results);
            $final_message = "🎯 **Respuesta Final:**\n\n" . $final_response;
            $this->save_partial_response($conversation_id, $user_id, 'final_response', $final_message);

            return ['success' => true, 'response' => $final_response];

        } catch (Exception $e) {
            error_log('LexAI Orchestrator Error: ' . $e->getMessage());
            $error_message = "❌ **Error del Sistema:** " . $e->getMessage();
            $this->save_partial_response($conversation_id, $user_id, 'system_error', $error_message);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Execute multi-task execution plan - ORIGINAL METHOD (kept for compatibility)
     */
    private function execute_multi_task_plan($user_id, $conversation_id, $user_message, $execution_plan, $files) {
        try {
            $final_results = [];
            $task_context = ""; // El contexto se acumula de tarea en tarea
            $conversation_messages = $this->db->get_conversation_messages($conversation_id);

            // LEX-LOGIC-004: PASO 2 - Ejecutar cada tarea del plan
            foreach ($execution_plan as $task_index => $task) {
                try {
                    error_log("LexAI Orchestrator: Starting task " . ($task_index + 1) . ": " . $task['type']);

                    // 2a: Seleccionar el mejor agente PARA ESTA TAREA
                    $agent = $this->select_agent_for_task($task['type'], $task['details']);
                    error_log("LexAI Orchestrator: Selected agent: " . $agent->name);
                    
                    // 2b: Preparar y ejecutar la tarea
                    $task_input = $task['details'];
                    if (!empty($task_context)) {
                        $task_input .= "\n\nContexto de tareas anteriores:\n" . $task_context;
                    }

                    // Prepare messages for this specific task
                    $task_messages = $conversation_messages;
                    $task_messages[] = ['role' => 'user', 'content' => $task_input];

                    $tools = $this->get_agent_tools_schemas($agent->id);
                    error_log("LexAI Orchestrator: Got " . count($tools) . " tools for agent");

                    error_log("LexAI Orchestrator: Making Gemini request for task");
                    $response = $this->api_handler->make_gemini_request(
                        $task_messages,
                        $agent->system_instruction,
                        $tools,
                        $agent->model,
                        $files,
                        $task['type'], // Task type for dynamic configuration
                        array('max_output_tokens' => $agent->max_output_tokens ?? 8192) // Pass agent's max_output_tokens
                    );
                    error_log("LexAI Orchestrator: Gemini request completed");

                    // Handle function calling if needed for this subtask
                    error_log("LexAI Orchestrator: Starting function calling handling");
                    $task_result = $this->handle_function_calling($response, $task_messages, $agent, $tools, $files, $user_id);
                    error_log("LexAI Orchestrator: Function calling completed");
                    
                    $final_results[] = [
                        'task_type' => $task['type'],
                        'task_details' => $task['details'],
                        'agent_used' => $agent->name,
                        'result' => $task_result
                    ];
                    
                    $task_context .= "\n\nResultado de la tarea '{$task['type']}':\n" . $task_result;

                } catch (Exception $e) {
                    error_log("LexAI Orchestrator: Error en tarea {$task_index}: " . $e->getMessage());
                    $final_results[] = [
                        'task_type' => $task['type'],
                        'task_details' => $task['details'],
                        'agent_used' => 'Error',
                        'result' => 'Error al ejecutar esta tarea: ' . $e->getMessage()
                    ];
                }
            }

            // LEX-LOGIC-004: PASO 3 - Sintetizar la respuesta final
            $final_response = $this->synthesize_final_response($user_message, $final_results);

            return [
                'success' => true,
                'response' => $final_response,
                'execution_plan' => $execution_plan,
                'task_results' => $final_results
            ];

        } catch (Exception $e) {
            error_log('LexAI Orchestrator Error: ' . $e->getMessage());

            // Fallback to simple processing if planning fails
            try {
                return $this->process_simple_query($user_id, $conversation_id, $user_message, $files);
            } catch (Exception $fallback_error) {
                return [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'fallback_response' => 'Lo siento, ha ocurrido un error al procesar tu solicitud.'
                ];
            }
        }
    }


    /**
     * LEX-LOGIC-004: Create execution plan by decomposing complex queries
     * Fixed ORCH-PLAN-001: Now includes dynamic context analysis for better planning
     */
    private function create_execution_plan($user_message, $files = []) {
        // 1. Analizar el contexto de la consulta
        $context_summary = [];
        if (!empty($files)) {
            $context_summary[] = "El usuario ha adjuntado " . count($files) . " archivo(s). Prioriza la tarea 'analizar_documento' al principio del plan.";
        }
        if (preg_match('/jurisprudencia|tesis|precedente/i', $user_message)) {
            $context_summary[] = "La consulta menciona jurisprudencia. Incluye y prioriza la tarea 'buscar_jurisprudencia'.";
        }
        if (preg_match('/contrato|acuerdo|convenio|cláusula/i', $user_message)) {
            $context_summary[] = "La consulta está relacionada con contratos. Considera tareas de 'buscar_legislacion' y 'redactar_clausula'.";
        }
        if (preg_match('/noticia|actualidad|reforma reciente/i', $user_message)) {
            $context_summary[] = "La consulta busca información reciente. Prioriza 'buscar_noticias' o 'google_search'.";
        }

        $context_string = empty($context_summary) ? "No hay contexto adicional." : implode("\n- ", $context_summary);

        // 2. Construir el prompt dinámico
        $planning_prompt = "Eres un planificador de tareas experto para un sistema de IA legal. Tu objetivo es analizar la consulta del usuario y su contexto para crear un plan de ejecución secuencial y lógico.

**Tipos de Tareas Disponibles:**
- `buscar_legislacion`: Para encontrar leyes, códigos y normativas.
- `buscar_jurisprudencia`: Para buscar tesis y precedentes judiciales.
- `analizar_documento`: Para examinar el contenido de archivos adjuntos.
- `redactar_clausula`: Para generar borradores de textos legales, cláusulas o documentos.
- `investigar_tema`: Para investigación general sobre un tema legal.
- `google_search`: Para búsquedas web generales y de actualidad.

**Contexto de la Consulta Actual:**
- {$context_string}

**Instrucciones de Planificación:**
1.  **Prioriza según el contexto:** Usa el contexto proporcionado para ordenar las tareas de manera lógica. Por ejemplo, si hay archivos, analiza los documentos primero.
2.  **Sé específico:** Cada tarea debe tener un campo 'details' claro y conciso.
3.  **Crea planes detallados:** Para consultas complejas (redacción, análisis comparativo), genera un plan de 3 a 7 pasos. Para consultas simples, puedes generar un plan de 1 o 2 pasos.
4.  **Responde SIEMPRE en formato JSON válido.**

**Consulta del Usuario:**
\"" . $user_message . "\"

**Formato de Salida (JSON):**
```json
{
  \"plan\": [
    {\"type\": \"tipo_de_tarea_1\", \"details\": \"Descripción detallada de la tarea 1\"},
    {\"type\": \"tipo_de_tarea_2\", \"details\": \"Descripción detallada de la tarea 2\"}
  ]
}
```";

        try {
            $messages = [['role' => 'user', 'content' => $planning_prompt]];
            
            $response = $this->api_handler->make_gemini_request(
                $messages,
                "Responde únicamente con un objeto JSON que contenga una clave 'plan' con un array de tareas.",
                null,
                'gemini-2.5-flash'
            );

            $plan_json = $this->extract_json_from_response($response['content']);

            if ($plan_json && isset($plan_json['plan']) && is_array($plan_json['plan'])) {
                error_log("LexAI Orchestrator: Plan creado con " . count($plan_json['plan']) . " tareas.");
                return $plan_json['plan'];
            }

            error_log("LexAI Orchestrator: No se pudo crear un plan estructurado. Se procederá con un agente general.");
            return []; // Fallback a procesamiento simple

        } catch (Exception $e) {
            error_log('LexAI Planning Error: ' . $e->getMessage());
            return []; // Fallback to simple processing
        }
    }

    /**
     * LEX-LOGIC-004: Select best agent for a specific task type using AI with Function Calling
     * Fixed ORCH-SELECT-001: Now uses structured function calling instead of fragile text parsing
     */
    private function select_agent_for_task($task_type, $task_details) {
        $active_agents = $this->db->get_agents('active');
        if (empty($active_agents)) {
            throw new Exception(__('No hay agentes activos configurados.', 'lexai'));
        }

        // 1. Crear una lista de agentes para el prompt
        $agents_list_for_prompt = "";
        foreach ($active_agents as $agent) {
            $agents_list_for_prompt .= "ID: {$agent->id}, Nombre: {$agent->name}, Descripción: {$agent->description}\n";
        }

        // 2. Definir la herramienta (función) para la selección
        $selection_tool = [
            'functionDeclarations' => [
                [
                    'name' => 'assign_task_to_agent',
                    'description' => 'Asigna la tarea al agente más adecuado.',
                    'parameters' => [
                        'type' => 'OBJECT',
                        'properties' => [
                            'agent_id' => [
                                'type' => 'INTEGER',
                                'description' => 'El ID del agente seleccionado.'
                            ],
                            'justification' => [
                                'type' => 'STRING',
                                'description' => 'Breve justificación de por qué se seleccionó este agente.'
                            ]
                        ],
                        'required' => ['agent_id', 'justification']
                    ]
                ]
            ]
        ];

        // 3. Crear el prompt para el LLM
        $selection_prompt = "Tu única tarea es seleccionar el agente más adecuado para la siguiente tarea y llamar a la función 'assign_task_to_agent'.

**Tarea a Realizar:**
- **Tipo:** {$task_type}
- **Detalles:** {$task_details}

**Agentes Disponibles:**
{$agents_list_for_prompt}

Analiza la tarea y las descripciones de los agentes y elige el ID del agente más calificado.";

        try {
            $messages = [['role' => 'user', 'content' => $selection_prompt]];

            // 4. Llamar a la API con la herramienta definida
            $response = $this->api_handler->make_gemini_request(
                $messages,
                "Selecciona el mejor agente para la tarea llamando a la función 'assign_task_to_agent'.",
                [$selection_tool], // Pasar la herramienta
                'gemini-2.5-flash'
            );

            // 5. Procesar la respuesta estructurada (function call)
            if (!empty($response['function_calls'])) {
                $function_call = $response['function_calls'][0]['functionCall'];
                if ($function_call['name'] === 'assign_task_to_agent' && isset($function_call['args']['agent_id'])) {
                    $selected_agent_id = intval($function_call['args']['agent_id']);

                    foreach ($active_agents as $agent) {
                        if ($agent->id == $selected_agent_id) {
                            error_log("LexAI Orchestrator: AI selected agent ID {$selected_agent_id}: {$agent->name}. Justification: " . ($function_call['args']['justification'] ?? 'N/A'));
                            return $agent;
                        }
                    }
                }
            }

            // Fallback si el LLM no llama a la función correctamente
            error_log("LexAI Orchestrator: Gemini no seleccionó un agente válido mediante function calling. Usando fallback.");
            return $this->fallback_agent_selection($task_type, $active_agents);

        } catch (Exception $e) {
            error_log("LexAI Orchestrator: Error en selección de agente por IA: " . $e->getMessage() . ". Usando fallback.");
            return $this->fallback_agent_selection($task_type, $active_agents);
        }
    }

    /**
     * Fallback agent selection based on simple keyword matching
     * Used when AI-based selection fails
     */
    private function fallback_agent_selection($task_type, $agents) {
        // Lógica de fallback simple basada en palabras clave
        foreach ($agents as $agent) {
            if (stripos($agent->name, $task_type) !== false || stripos($agent->description, $task_type) !== false) {
                error_log("LexAI Orchestrator: Fallback selected agent: {$agent->name} (keyword match)");
                return $agent;
            }
        }
        // Si no hay coincidencia, devuelve el primer agente (probablemente el orquestador)
        error_log("LexAI Orchestrator: Fallback selected first agent: {$agents[0]->name} (no keyword match)");
        return $agents[0];
    }

    /**
     * LEX-LOGIC-004: Synthesize final response from multiple task results
     */
    private function synthesize_final_response($original_query, $task_results) {
        if (empty($task_results)) {
            return "No se pudieron procesar las tareas solicitadas.";
        }

        $synthesis_prompt = "Eres un sintetizador de respuestas legales. Tu trabajo es crear una respuesta coherente y completa basada en los resultados de múltiples tareas ejecutadas.

CONSULTA ORIGINAL: \"" . $original_query . "\"

RESULTADOS DE TAREAS:
";

        foreach ($task_results as $index => $result) {
            $synthesis_prompt .= "\nTarea " . ($index + 1) . " (" . $result['task_type'] . "):\n";
            $synthesis_prompt .= "Ejecutada por: " . $result['agent_used'] . "\n";
            $synthesis_prompt .= "Resultado: " . $result['result'] . "\n";
        }

        $synthesis_prompt .= "\nINSTRUCCIONES:
1. Crea una respuesta coherente que integre todos los resultados relevantes
2. Mantén un tono profesional y legal apropiado
3. Si hay errores en alguna tarea, menciónalos brevemente pero enfócate en lo que sí funcionó
4. Estructura la respuesta de manera lógica y fácil de entender
5. Incluye recomendaciones prácticas cuando sea apropiado

RESPUESTA SINTETIZADA:";

        try {
            $messages = [['role' => 'user', 'content' => $synthesis_prompt]];
            
            $response = $this->api_handler->make_gemini_request(
                $messages,
                "Eres un abogado experto en síntesis de información legal. Proporciona respuestas claras, precisas y profesionales.",
                null,
                'gemini-2.5-flash'
            );

            return $response['content'];

        } catch (Exception $e) {
            error_log('LexAI Synthesis Error: ' . $e->getMessage());
            
            // Fallback: simple concatenation
            $fallback_response = "Basado en el análisis realizado:\n\n";
            foreach ($task_results as $result) {
                if ($result['agent_used'] !== 'Error') {
                    $fallback_response .= "• " . $result['result'] . "\n\n";
                }
            }
            return $fallback_response;
        }
    }

    /**
     * Fallback to simple single-agent processing
     */
    private function process_simple_query($user_id, $conversation_id, $user_message, $files = array()) {
        // Original simple processing logic
        $agent = $this->select_agent_for_query($user_message, $files);

        $messages = $this->db->get_conversation_messages($conversation_id);
        if (empty($messages)) {
            $messages = [['role' => 'user', 'content' => $user_message]];
        }

        $tools = $this->get_agent_tools_schemas($agent->id);
        $response = $this->api_handler->make_gemini_request(
            $messages,
            $agent->system_instruction,
            $tools,
            $agent->model,
            $files,
            'default',
            array('max_output_tokens' => $agent->max_output_tokens ?? 8192)
        );

        $response_content = $this->handle_function_calling($response, $messages, $agent, $tools, $files, $user_id);

        return [
            'success' => true,
            'response' => $response_content,
            'agent_used' => $agent->name
        ];
    }

    /**
     * Handle function calling for any response - CORRECTED according to Gemini documentation
     */
    private function handle_function_calling($response, $messages, $agent, $tools, $files, $user_id) {
        if (!empty($response['function_calls'])) {
            // STEP 1: Add the model's response with function calls using correct format
            // According to Gemini docs, we need to add the model's response to conversation history
            $model_response_parts = [];

            // Add text content if present
            if (!empty($response['content'])) {
                $model_response_parts[] = ['text' => $response['content']];
            }

            // Add function calls as parts
            foreach ($response['function_calls'] as $function_call) {
                $model_response_parts[] = ['functionCall' => $function_call['functionCall']];
            }

            // Add model message with parts structure
            $messages[] = [
                'role' => 'model',
                'parts' => $model_response_parts
            ];

            // STEP 2: Execute tools and prepare function responses
            $function_response_parts = [];
            foreach ($response['function_calls'] as $function_call) {
                $execution_result = $this->execute_tool($function_call, $user_id, $agent->id);

                // CRITICAL FIX: Format function response according to Gemini documentation
                $response_data = $execution_result['success'] ? $execution_result['result'] : [
                    'error' => $execution_result['error'],
                    'success' => false
                ];
                error_log("LexAI Orchestrator: handle_function_calling - response_data type: " . gettype($response_data) . ", content: " . (is_array($response_data) || is_object($response_data) ? json_encode($response_data) : $response_data));

                $function_response_parts[] = [
                    'functionResponse' => [
                        'name' => $function_call['functionCall']['name'],
                        'response' => $response_data
                    ]
                ];
            }

            // STEP 3: Add user message with function responses using correct parts format
            $messages[] = [
                'role' => 'user',
                'parts' => $function_response_parts
            ];

            // STEP 4: Make a second API call with the function results
            $final_response = $this->api_handler->make_gemini_request(
                $messages,
                $agent->system_instruction,
                $tools,
                $agent->model,
                $files,
                'function_calling_followup',
                array('max_output_tokens' => $agent->max_output_tokens ?? 8192)
            );

            // Check if the final response also has function calls (compositional calling)
            if (!empty($final_response['function_calls'])) {
                // Recursively handle additional function calls
                return $this->handle_function_calling($final_response, $messages, $agent, $tools, $files, $user_id);
            }

            return $final_response['content'];
        } else {
            // No function calls, return the original content
            return $response['content'];
        }
    }

    /**
     * Extract JSON from LLM response
     */
    private function extract_json_from_response($response_text) {
        // Try to find JSON in the response
        $json_start = strpos($response_text, '{');
        $json_end = strrpos($response_text, '}');
        
        if ($json_start !== false && $json_end !== false && $json_end > $json_start) {
            $json_string = substr($response_text, $json_start, $json_end - $json_start + 1);
            $decoded = json_decode($json_string, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded;
            }
        }
        
        return null;
    }

    /**
     * Executes a tool based on the function call from the model - RENOVATED MCP-only
     *
     * @param array $function_call The function call data from Gemini.
     * @param int $user_id The current user ID.
     * @param int $agent_id The ID of the agent making the call (optional).
     * @return array The result of the tool execution.
     */
    private function execute_tool($function_call, $user_id, $agent_id = null) {
        $tool_name = $function_call['functionCall']['name'];
        $args = $function_call['functionCall']['args'];

        error_log("LexAI Orchestrator: Executing tool '$tool_name' with args: " . json_encode($args));

        // Check agent permissions
        if ($agent_id && !$this->can_agent_use_tool_cached($agent_id, $tool_name)) {
            return [
                'success' => false,
                'error' => sprintf(__('El agente no tiene permisos para usar la herramienta: %s', 'lexai'), $tool_name)
            ];
        }

        try {
            // Use the renovated Tool Executor for all tool executions
            $result = $this->tool_executor->execute_tool($tool_name, $args, $agent_id);

            // CRITICAL FIX: Format result properly for Gemini function response
            if ($result['success']) {
                // Return structured data that Gemini can understand
                error_log("LexAI Orchestrator: execute_tool - result['data'] type: " . gettype($result['data']) . ", content: " . (is_array($result['data']) || is_object($result['data']) ? json_encode($result['data']) : $result['data']));
                return [
                    'success' => true,
                    'result' => $result['data'], // Don't JSON encode - keep as structured data
                    'execution_type' => $result['execution_type'] ?? 'mcp',
                    'tool_name' => $tool_name,
                    'timestamp' => current_time('mysql')
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'] ?? 'Error desconocido en la ejecución de la herramienta',
                    'tool_name' => $tool_name,
                    'timestamp' => current_time('mysql')
                ];
            }

        } catch (Exception $e) {
            error_log('LexAI Orchestrator Tool Execution Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'Error al ejecutar la herramienta: ' . $e->getMessage(),
                'tool_name' => $tool_name
            ];
        }
    }

    /**
     * Generate research summary from multiple sources
     */
    private function generate_research_summary($research_results) {
        $summary = [];
        
        // Summarize knowledge base results
        if (!empty($research_results['knowledge_base'])) {
            $kb_count = count($research_results['knowledge_base']);
            $summary[] = "Se encontraron {$kb_count} documentos relevantes en la base de conocimiento legal.";
        }
        
        // Summarize web search results
        if (!empty($research_results['web_search']['success'])) {
            $summary[] = "Se realizó búsqueda web complementaria con resultados actualizados.";
        }
        
        if (empty($summary)) {
            $summary[] = "No se encontraron resultados específicos para esta consulta.";
        }
        
        return implode(' ', $summary);
    }

    /**
     * Selects the most appropriate agent for the query.
     * (Placeholder - implement more advanced logic here)
     *
     * @param string $user_message
     * @param array $files
     * @return object The selected agent object from the database.
     */
    private function select_agent_for_query($user_message, $files = []) {
        $active_agents = $this->db->get_agents('active');

        if (empty($active_agents)) {
            throw new Exception(__('No hay agentes activos configurados. Por favor, configura al menos un agente.', 'lexai'));
        }

        // Prepare agent selection tools
        $agent_selection_tools = $this->agent_factory->get_agent_selection_tool_schemas($active_agents);

        // Prepare system instruction for agent selection
        $system_instruction = 'Eres un orquestador de IA. Tu tarea es seleccionar el agente más adecuado para responder a la consulta del usuario. Debes elegir una de las herramientas de selección de agente disponibles. No respondas directamente a la consulta del usuario, solo selecciona el agente.';

        // Prepare messages for agent selection
        $messages = [
            ['role' => 'user', 'content' => 'La consulta del usuario es: "' . $user_message . ''. ($files ? ' (Archivos adjuntos: ' . count($files) . ')' : '') . '. Elige el agente más adecuado para manejarla.']
        ];

        try {
            // Make API call to Gemini for agent selection
            $response = $this->api_handler->make_gemini_request(
                $messages,
                $system_instruction,
                $agent_selection_tools, // Pass agent selection tools
                'gemini-2.5-flash' // Use a fast model for selection
            );

            // Check for function call (agent selection)
            if (!empty($response['function_calls'])) {
                $function_call = $response['function_calls'][0]; // Assuming one function call
                $tool_name = $function_call['functionCall']['name'];
                $agent_id_from_tool = $function_call['functionCall']['args']['agent_id'];

                // Find the selected agent by ID
                foreach ($active_agents as $agent) {
                    if ($agent->id == $agent_id_from_tool) {
                        return $agent;
                    }
                }
            }

            // Fallback if no agent is selected or an invalid agent_id is returned
            error_log('LexAI Orchestrator: Gemini no seleccionó un agente válido. Cayendo a agente por defecto.');
            return $this->get_orchestrator_agent();

        } catch (Exception $e) {
            error_log('LexAI Orchestrator Agent Selection Error: ' . $e->getMessage());
            // Fallback to default agent on error
            return $this->get_orchestrator_agent();
        }
    }

    /**
     * Gets the default orchestrator agent.
     *
     * @return object The orchestrator agent.
     */
    private function get_orchestrator_agent() {
        // This should fetch a predefined orchestrator/general agent from the DB
        $agents = $this->db->get_agents('active');
        foreach ($agents as $agent) {
            if (stripos($agent->name, 'orquestador') !== false) {
                return $agent;
            }
        }
        // Fallback to the first active agent
        return !empty($agents) ? $agents[0] : (object)[
            'id' => 0,
            'name' => 'Agente General por Defecto',
            'system_instruction' => 'Eres un asistente legal útil especializado en derecho mexicano.',
            'tools' => json_encode(['legal_knowledge_base', 'google_search']),
            'model' => 'gemini-2.5-flash'
        ];
    }

    /**
     * Get tool schemas for a specific agent
     *
     * @param int $agent_id The agent ID
     * @return array Array of tool schemas
     */
    private function get_agent_tools_schemas($agent_id) {
        if ($agent_id === 0) {
            // Default agent - return basic tools
            return $this->get_default_tools_schemas();
        }

        return $this->tools_manager->get_tools_schema_for_agent($agent_id);
    }

    /**
     * Get default tool schemas for fallback
     *
     * @return array Array of default tool schemas
     */
    private function get_default_tools_schemas() {
        return array(
            array(
                'name' => 'legal_knowledge_base',
                'description' => 'Busca información relevante en la base de conocimiento legal',
                'parameters' => array(
                    'type' => 'OBJECT',
                    'properties' => array(
                        'query' => array(
                            'type' => 'STRING',
                            'description' => 'Consulta de búsqueda'
                        ),
                        'category' => array(
                            'type' => 'STRING',
                            'description' => 'Categoría legal específica (opcional)'
                        )
                    ),
                    'required' => array('query')
                )
            ),
            array(
                'name' => 'google_search',
                'description' => 'Busca información actualizada en Google',
                'parameters' => array(
                    'type' => 'OBJECT',
                    'properties' => array(
                        'query' => array(
                            'type' => 'STRING',
                            'description' => 'Términos de búsqueda'
                        )
                    ),
                    'required' => array('query')
                )
            )
        );
    }

    /**
     * LEX-PERF-001: Get agent tool names efficiently (cached)
     * Cache for performance optimization
     */
    private $agent_tools_cache = [];

    private function get_agent_tool_names($agent_id) {
        // LEX-PERF-001: Check cache first
        if (isset($this->agent_tools_cache[$agent_id])) {
            return $this->agent_tools_cache[$agent_id];
        }

        // Load from database and cache
        $agent_tools = $this->tools_manager->get_agent_tools($agent_id);
        $tool_names = [];
        
        foreach ($agent_tools as $tool) {
            $tool_names[] = $tool->name;
        }

        // Cache the result
        $this->agent_tools_cache[$agent_id] = $tool_names;
        
        return $tool_names;
    }

    /**
     * LEX-PERF-001: Optimized tool permission check using cache
     */
    private function can_agent_use_tool_cached($agent_id, $tool_name) {
        $agent_tools = $this->get_agent_tool_names($agent_id);
        return in_array($tool_name, $agent_tools);
    }

    /**
     * Process message - Main entry point for AJAX handler
     */
    public function process_message($message, $conversation_id, $user_id, $files = array(), $agent_id = null) {
        try {
            // Use the existing process_query method
            $result = $this->process_query($user_id, $conversation_id, $message, $files);
            
            if ($result['success']) {
                return array(
                    'content' => $result['response'],
                    'agent_id' => $agent_id,
                    'agent_name' => $result['agent_used'] ?? 'LexAI',
                    'tokens_used' => $result['tokens_used'] ?? 0,
                    'metadata' => $result['metadata'] ?? null
                );
            } else {
                throw new Exception($result['error'] ?? 'Error desconocido');
            }
            
        } catch (Exception $e) {
            error_log('LexAI Orchestrator Process Message Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Format execution plan for display to user
     */
    private function format_execution_plan_for_display($execution_plan) {
        $plan_text = "📋 **Plan de Ejecución Creado:**\n\n";

        foreach ($execution_plan as $index => $task) {
            $task_number = $index + 1;
            $task_icon = $this->get_task_icon($task['type']);
            $plan_text .= "{$task_icon} **Tarea {$task_number}:** {$task['details']}\n\n";
        }

        $plan_text .= "🚀 **Iniciando ejecución del plan...**\n\n";

        return $plan_text;
    }

    /**
     * Get icon for task type
     */
    private function get_task_icon($task_type) {
        $icons = [
            'buscar_legislacion' => '<i class="fas fa-book"></i>',
            'buscar_jurisprudencia' => '<i class="fas fa-gavel"></i>',
            'redactar_clausula' => '<i class="fas fa-pen-alt"></i>',
            'analizar_documento' => '<i class="fas fa-search"></i>',
            'investigar_tema' => '<i class="fas fa-microscope"></i>',
            'buscar_noticias' => '<i class="far fa-newspaper"></i>',
            'consulta_general' => '<i class="fas fa-comment-dots"></i>'
        ];

        return $icons[$task_type] ?? '<i class="fas fa-cogs"></i>';
    }

    /**
     * Send partial response to user (for streaming)
     */
    private function save_partial_response($conversation_id, $user_id, $type, $content) {
        $this->db->add_message(
            $conversation_id,
            'assistant',
            $content,
            [
                'partial' => true,
                'type' => $type,
                'timestamp' => current_time('mysql')
            ]
        );
        error_log("LexAI Orchestrator: Saved partial response ({$type}) to DB.");
    }
}