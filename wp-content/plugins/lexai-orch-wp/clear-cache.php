<?php
/**
 * Script para limpiar caché y forzar recarga de CSS
 * 
 * @package LexAI
 * @since 2.0.1
 */

// Simular entorno WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/');
}

echo "<h1>🧹 LexAI Cache Cleaner</h1>\n";
echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

// Función para simular wp_cache_flush
function lexai_clear_cache() {
    $cache_cleared = false;
    
    // Limpiar caché de WordPress si está disponible
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
        $cache_cleared = true;
        echo "<p>✅ WordPress object cache limpiado</p>\n";
    }
    
    // Limpiar caché de plugins populares
    $cache_plugins = array(
        'W3 Total Cache' => 'w3tc_flush_all',
        'WP Super Cache' => 'wp_cache_clear_cache',
        'WP Rocket' => 'rocket_clean_domain',
        'LiteSpeed Cache' => 'LiteSpeed_Cache_API::purge_all',
        'Autoptimize' => 'autoptimizeCache::clearall'
    );
    
    foreach ($cache_plugins as $plugin_name => $function) {
        if (function_exists($function)) {
            call_user_func($function);
            echo "<p>✅ {$plugin_name} cache limpiado</p>\n";
            $cache_cleared = true;
        }
    }
    
    return $cache_cleared;
}

// Función para verificar el timestamp del CSS
function check_css_timestamp() {
    $css_file = LEXAI_PLUGIN_DIR . 'assets/css/lexai-fullpage-chat.css';
    
    if (file_exists($css_file)) {
        $timestamp = filemtime($css_file);
        $formatted_time = date('Y-m-d H:i:s', $timestamp);
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
        echo "<h3>📄 Información del archivo CSS</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Archivo:</strong> lexai-fullpage-chat.css</li>\n";
        echo "<li><strong>Última modificación:</strong> {$formatted_time}</li>\n";
        echo "<li><strong>Timestamp:</strong> {$timestamp}</li>\n";
        echo "<li><strong>Tamaño:</strong> " . number_format(filesize($css_file)) . " bytes</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        return $timestamp;
    } else {
        echo "<p>❌ Archivo CSS no encontrado: {$css_file}</p>\n";
        return false;
    }
}

// Función para generar URLs con cache busting
function generate_cache_busted_urls($timestamp) {
    $base_url = 'https://tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/assets/css/lexai-fullpage-chat.css';
    
    echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<h3>🔗 URLs con Cache Busting</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>URL original:</strong> <a href='{$base_url}' target='_blank'>{$base_url}</a></li>\n";
    echo "<li><strong>URL con timestamp:</strong> <a href='{$base_url}?v={$timestamp}' target='_blank'>{$base_url}?v={$timestamp}</a></li>\n";
    echo "<li><strong>URL con versión:</strong> <a href='{$base_url}?v=2.0.1' target='_blank'>{$base_url}?v=2.0.1</a></li>\n";
    echo "<li><strong>URL con random:</strong> <a href='{$base_url}?v=" . time() . "' target='_blank'>{$base_url}?v=" . time() . "</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

// Función para verificar cambios CSS
function verify_css_changes() {
    $css_file = LEXAI_PLUGIN_DIR . 'assets/css/lexai-fullpage-chat.css';
    
    if (!file_exists($css_file)) {
        echo "<p>❌ No se puede verificar: archivo CSS no encontrado</p>\n";
        return false;
    }
    
    $css_content = file_get_contents($css_file);
    
    // Buscar los cambios específicos aplicados
    $changes_to_check = array(
        'rgb(145 153 163)' => 'Conversation items - fondo gris',
        'rgb(98 102 107 / 27%)' => 'Search box - fondo gris',
        '--lexai-conversation-bg' => 'Variables de conversation items',
        '--lexai-search-bg' => 'Variables de search box',
        '--lexai-btn-secondary-bg' => 'Variables de botones',
        'body:not(.lexai-theme-dark) .lexai-conversation-item' => 'Override específico light theme',
        '.lexai-theme-dark .lexai-conversation-item' => 'Override específico dark theme'
    );
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<h3>🔍 Verificación de Cambios CSS</h3>\n";
    echo "<ul>\n";
    
    $all_changes_found = true;
    foreach ($changes_to_check as $search => $description) {
        if (strpos($css_content, $search) !== false) {
            echo "<li>✅ <strong>{$description}:</strong> Encontrado</li>\n";
        } else {
            echo "<li>❌ <strong>{$description}:</strong> NO encontrado</li>\n";
            $all_changes_found = false;
        }
    }
    
    echo "</ul>\n";
    
    if ($all_changes_found) {
        echo "<p style='color: green; font-weight: bold;'>🎉 Todos los cambios CSS están presentes en el archivo</p>\n";
    } else {
        echo "<p style='color: red; font-weight: bold;'>⚠️ Algunos cambios CSS no se encontraron</p>\n";
    }
    
    echo "</div>\n";
    
    return $all_changes_found;
}

// Función para generar instrucciones de limpieza manual
function generate_manual_instructions() {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<h3>🛠️ Instrucciones de Limpieza Manual</h3>\n";
    echo "<h4>1. Limpiar caché del navegador:</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Chrome/Edge:</strong> Ctrl+Shift+R o F12 → Network → Disable cache</li>\n";
    echo "<li><strong>Firefox:</strong> Ctrl+Shift+R o F12 → Network → Settings → Disable cache</li>\n";
    echo "<li><strong>Safari:</strong> Cmd+Option+R</li>\n";
    echo "</ul>\n";
    
    echo "<h4>2. Limpiar caché de WordPress:</h4>\n";
    echo "<ul>\n";
    echo "<li>Ir a wp-admin → Plugins → Desactivar y reactivar LexAI</li>\n";
    echo "<li>Si tienes plugin de caché: limpiar todo el caché</li>\n";
    echo "<li>Verificar que no hay archivos .htaccess bloqueando</li>\n";
    echo "</ul>\n";
    
    echo "<h4>3. Verificar permisos de archivos:</h4>\n";
    echo "<ul>\n";
    echo "<li>CSS file: 644 permisos</li>\n";
    echo "<li>Directorio assets: 755 permisos</li>\n";
    echo "</ul>\n";
    
    echo "<h4>4. Forzar recarga:</h4>\n";
    echo "<ul>\n";
    echo "<li>Agregar ?v=" . time() . " al final de la URL del CSS</li>\n";
    echo "<li>Cambiar versión del plugin en lexai.php</li>\n";
    echo "<li>Tocar el archivo CSS para actualizar timestamp</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

// Ejecutar todas las verificaciones
echo "<h2>🔍 Verificando estado actual...</h2>\n";

// 1. Verificar timestamp del CSS
$timestamp = check_css_timestamp();

// 2. Verificar cambios CSS
$changes_verified = verify_css_changes();

// 3. Generar URLs con cache busting
if ($timestamp) {
    generate_cache_busted_urls($timestamp);
}

// 4. Intentar limpiar caché
echo "<h2>🧹 Limpiando caché...</h2>\n";
$cache_cleared = lexai_clear_cache();

if (!$cache_cleared) {
    echo "<p>⚠️ No se detectaron plugins de caché activos</p>\n";
    echo "<p>💡 Esto es normal si no tienes plugins de caché instalados</p>\n";
}

// 5. Generar instrucciones manuales
generate_manual_instructions();

// 6. Resumen final
echo "<h2>📊 Resumen Final</h2>\n";
echo "<div style='background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
echo "<h3>🎯 Estado del Sistema</h3>\n";
echo "<ul>\n";
echo "<li><strong>Versión del plugin:</strong> 2.0.1 (actualizada)</li>\n";
echo "<li><strong>Archivo CSS:</strong> " . ($timestamp ? "✅ Encontrado" : "❌ No encontrado") . "</li>\n";
echo "<li><strong>Cambios CSS:</strong> " . ($changes_verified ? "✅ Verificados" : "❌ Faltantes") . "</li>\n";
echo "<li><strong>Cache busting:</strong> ✅ Implementado</li>\n";
echo "<li><strong>Timestamp actual:</strong> " . ($timestamp ?: 'N/A') . "</li>\n";
echo "</ul>\n";

if ($timestamp && $changes_verified) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<h4>🎉 ¡Todo está listo!</h4>\n";
    echo "<p>Los cambios CSS están aplicados y el sistema de cache busting está funcionando.</p>\n";
    echo "<p><strong>Próximo paso:</strong> Limpiar caché del navegador y recargar la página del chat.</p>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<h4>⚠️ Acción requerida</h4>\n";
    echo "<p>Algunos elementos necesitan atención. Revisa las instrucciones manuales arriba.</p>\n";
    echo "</div>\n";
}

echo "</div>\n";

// CSS para mejorar la presentación
echo "<style>
h1, h2, h3 { color: #333; margin: 15px 0 10px 0; }
ul, ol { margin: 10px 0; padding-left: 20px; }
li { margin: 5px 0; }
p { margin: 10px 0; }
a { color: #007cba; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
