<?php
/**
 * Test completo para TODAS las páginas de autenticación
 * 
 * @package LexAI
 * @since 2.0.1
 */

// Simular entorno WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/');
}

// Configurar headers
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 Test Completo Páginas de Autenticación - LexAI</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .card { background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #e9ecef; }
        .btn { display: inline-block; padding: 12px 24px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; font-weight: 600; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        ul { padding-left: 20px; }
        li { margin: 8px 0; }
        h1, h2, h3 { color: #333; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: 600; }
        .page-status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .page-status.ok { background: #d4edda; color: #155724; }
        .page-status.error { background: #f8d7da; color: #721c24; }
        .page-status.partial { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Completo Páginas de Autenticación - LexAI</h1>
        <p><strong>Ejecutado:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php
        // Definir todas las páginas de autenticación
        $auth_pages = array(
            'login' => array(
                'name' => 'Login',
                'url' => '/lexai-login/',
                'shortcode_used' => '[lexai_login_form]',
                'shortcode_registered' => 'lexai_login_form',
                'template' => 'templates/auth/login-form.php',
                'icon' => '🔑'
            ),
            'register' => array(
                'name' => 'Register',
                'url' => '/lexai-register/',
                'shortcode_used' => '[lexai_register_form]',
                'shortcode_registered' => 'lexai_register_form',
                'template' => 'templates/auth/register-form.php',
                'icon' => '📝'
            ),
            'forgot_password' => array(
                'name' => 'Forgot Password',
                'url' => '/lexai-reset-password/',
                'shortcode_used' => '[lexai_reset_password_form]',
                'shortcode_registered' => 'lexai_reset_password_form',
                'template' => 'templates/auth/forgot-password-form.php',
                'icon' => '🔄'
            )
        );
        
        // 1. Resumen general
        echo "<h2>📊 1. Resumen General</h2>\n";
        
        echo "<table>\n";
        echo "<thead>\n";
        echo "<tr>\n";
        echo "<th>Página</th>\n";
        echo "<th>URL</th>\n";
        echo "<th>Shortcode</th>\n";
        echo "<th>Template</th>\n";
        echo "<th>Estado</th>\n";
        echo "</tr>\n";
        echo "</thead>\n";
        echo "<tbody>\n";
        
        $total_pages = count($auth_pages);
        $working_pages = 0;
        
        foreach ($auth_pages as $key => $page) {
            $template_exists = file_exists(LEXAI_PLUGIN_DIR . $page['template']);
            $status = $template_exists ? 'ok' : 'error';
            $status_text = $template_exists ? '✅ Completo' : '❌ Falta Template';
            
            if ($template_exists) $working_pages++;
            
            echo "<tr>\n";
            echo "<td>{$page['icon']} <strong>{$page['name']}</strong></td>\n";
            echo "<td><code>{$page['url']}</code></td>\n";
            echo "<td><code>{$page['shortcode_used']}</code></td>\n";
            echo "<td><code>{$page['template']}</code></td>\n";
            echo "<td><span class='page-status {$status}'>{$status_text}</span></td>\n";
            echo "</tr>\n";
        }
        
        echo "</tbody>\n";
        echo "</table>\n";
        
        $completion_percentage = round(($working_pages / $total_pages) * 100);
        
        if ($completion_percentage == 100) {
            echo "<div class='success'>\n";
            echo "<h3>🎉 Todas las páginas están completas ({$working_pages}/{$total_pages})</h3>\n";
            echo "</div>\n";
        } else {
            echo "<div class='warning'>\n";
            echo "<h3>⚠️ Páginas completadas: {$working_pages}/{$total_pages} ({$completion_percentage}%)</h3>\n";
            echo "</div>\n";
        }
        
        // 2. Verificación detallada por página
        echo "<h2>🔍 2. Verificación Detallada por Página</h2>\n";
        
        echo "<div class='grid'>\n";
        
        foreach ($auth_pages as $key => $page) {
            echo "<div class='card'>\n";
            echo "<h3>{$page['icon']} {$page['name']}</h3>\n";
            
            // Verificar template
            $template_path = LEXAI_PLUGIN_DIR . $page['template'];
            if (file_exists($template_path)) {
                $template_size = filesize($template_path);
                $template_modified = date('Y-m-d H:i:s', filemtime($template_path));
                
                echo "<div class='success'>\n";
                echo "<h4>✅ Template Encontrado</h4>\n";
                echo "<ul>\n";
                echo "<li><strong>Tamaño:</strong> " . number_format($template_size) . " bytes</li>\n";
                echo "<li><strong>Modificado:</strong> {$template_modified}</li>\n";
                echo "</ul>\n";
                echo "</div>\n";
                
                // Verificar contenido del template
                $template_content = file_get_contents($template_path);
                
                $template_checks = array(
                    'wp_enqueue_script' => 'Carga de scripts',
                    'wp_localize_script' => 'Localización de scripts',
                    'lexaiConfig' => 'Configuración global',
                    'lexaiAuthPageActive' => 'Prevención de conflictos',
                    'lazyloadRunObserver' => 'Manejo de conflictos',
                    'LEXAI_VERSION' => 'Versión del plugin'
                );
                
                echo "<h4>🔍 Elementos del Template:</h4>\n";
                echo "<ul>\n";
                foreach ($template_checks as $search => $description) {
                    if (strpos($template_content, $search) !== false) {
                        echo "<li class='status-ok'>✅ {$description}</li>\n";
                    } else {
                        echo "<li class='status-error'>❌ {$description}</li>\n";
                    }
                }
                echo "</ul>\n";
                
            } else {
                echo "<div class='error'>\n";
                echo "<h4>❌ Template No Encontrado</h4>\n";
                echo "<p>Falta: <code>{$page['template']}</code></p>\n";
                echo "</div>\n";
            }
            
            // URL de prueba
            $full_url = 'https://tuasesorlegalvirtual.online' . $page['url'];
            echo "<div class='info'>\n";
            echo "<h4>🔗 URL de Prueba</h4>\n";
            echo "<p><a href='{$full_url}' target='_blank'>{$full_url}</a></p>\n";
            echo "</div>\n";
            
            echo "</div>\n";
        }
        
        echo "</div>\n";
        
        // 3. Verificación de shortcodes
        echo "<h2>📋 3. Verificación de Shortcodes</h2>\n";
        
        $shortcodes_expected = array(
            'lexai_login' => 'Shortcode principal de login',
            'lexai_login_form' => 'Alias para compatibilidad de login',
            'lexai_register' => 'Shortcode principal de registro',
            'lexai_register_form' => 'Alias para compatibilidad de registro',
            'lexai_forgot_password' => 'Shortcode principal de forgot password',
            'lexai_reset_password_form' => 'Alias para compatibilidad de forgot password'
        );
        
        echo "<div class='info'>\n";
        echo "<h3>📝 Shortcodes Esperados</h3>\n";
        echo "<ul>\n";
        foreach ($shortcodes_expected as $shortcode => $description) {
            echo "<li><strong>[{$shortcode}]</strong> - {$description}</li>\n";
        }
        echo "</ul>\n";
        echo "</div>\n";
        
        // 4. Verificación de assets compartidos
        echo "<h2>📦 4. Verificación de Assets Compartidos</h2>\n";
        
        $shared_assets = array(
            'assets/css/lexai-auth.css' => 'CSS de autenticación',
            'assets/js/lexai-auth.js' => 'JavaScript de autenticación'
        );
        
        echo "<ul>\n";
        foreach ($shared_assets as $asset => $description) {
            $asset_path = LEXAI_PLUGIN_DIR . $asset;
            if (file_exists($asset_path)) {
                $asset_size = filesize($asset_path);
                $asset_modified = date('Y-m-d H:i:s', filemtime($asset_path));
                echo "<li class='status-ok'>✅ <strong>{$description}:</strong> Encontrado ({$asset_size} bytes, modificado: {$asset_modified})</li>\n";
            } else {
                echo "<li class='status-error'>❌ <strong>{$description}:</strong> NO encontrado</li>\n";
            }
        }
        echo "</ul>\n";
        
        // 5. Verificación de prevención de conflictos
        echo "<h2>🛡️ 5. Verificación de Prevención de Conflictos</h2>\n";
        
        $fullpage_class = LEXAI_PLUGIN_DIR . 'public/class-lexai-public-fullpage.php';
        if (file_exists($fullpage_class)) {
            $fullpage_content = file_get_contents($fullpage_class);
            
            $conflict_checks = array(
                "is_page('lexai-login')" => 'Exclusión página login',
                "is_page('lexai-register')" => 'Exclusión página register',
                "is_page('lexai-reset-password')" => 'Exclusión página reset password',
                '[lexai_login' => 'Detección shortcode login',
                '[lexai_register' => 'Detección shortcode register',
                '[lexai_reset_password' => 'Detección shortcode reset password'
            );
            
            echo "<h3>🔍 Verificaciones en class-lexai-public-fullpage.php:</h3>\n";
            echo "<ul>\n";
            foreach ($conflict_checks as $search => $description) {
                if (strpos($fullpage_content, $search) !== false) {
                    echo "<li class='status-ok'>✅ {$description}: Implementado</li>\n";
                } else {
                    echo "<li class='status-error'>❌ {$description}: NO implementado</li>\n";
                }
            }
            echo "</ul>\n";
            
        } else {
            echo "<div class='error'>\n";
            echo "<h3>❌ Archivo de clase fullpage no encontrado</h3>\n";
            echo "</div>\n";
        }
        
        // 6. Instrucciones de prueba
        echo "<h2>🧪 6. Instrucciones de Prueba</h2>\n";
        
        echo "<div class='warning'>\n";
        echo "<h3>📋 Cómo Probar Cada Página</h3>\n";
        echo "<ol>\n";
        echo "<li><strong>Limpiar caché del navegador:</strong> Ctrl+Shift+R</li>\n";
        echo "<li><strong>Abrir DevTools:</strong> F12 → Console</li>\n";
        echo "<li><strong>Verificar que NO aparezcan estos errores:</strong>\n";
        echo "<ul>\n";
        echo "<li>❌ 'User not authenticated' (normal en páginas auth)</li>\n";
        echo "<li>❌ 'lazyloadRunObserver already declared'</li>\n";
        echo "<li>❌ Scripts de chat cargando</li>\n";
        echo "<li>❌ Versión incorrecta de scripts</li>\n";
        echo "</ul></li>\n";
        echo "<li><strong>Verificar que SÍ aparezcan:</strong>\n";
        echo "<ul>\n";
        echo "<li>✅ 'LexAI Auth: [Page] initialized'</li>\n";
        echo "<li>✅ lexaiConfig con isLoginPage/isRegisterPage/isForgotPasswordPage = true</li>\n";
        echo "<li>✅ Scripts lexai-auth.js y lexai-auth.css cargados</li>\n";
        echo "</ul></li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        // 7. Resumen final
        echo "<h2>🎯 7. Resumen Final</h2>\n";
        
        if ($working_pages == $total_pages) {
            echo "<div class='success'>\n";
            echo "<h3>🎉 Sistema de Autenticación Completamente Funcional</h3>\n";
            echo "<p>Todas las páginas de autenticación están configuradas correctamente:</p>\n";
            echo "<ul>\n";
            echo "<li>✅ <strong>3/3 páginas</strong> con templates completos</li>\n";
            echo "<li>✅ <strong>6 shortcodes</strong> registrados (3 principales + 3 alias)</li>\n";
            echo "<li>✅ <strong>Prevención de conflictos</strong> implementada</li>\n";
            echo "<li>✅ <strong>Scripts específicos</strong> para auth pages</li>\n";
            echo "<li>✅ <strong>Versión actualizada</strong> a 2.0.1</li>\n";
            echo "</ul>\n";
            echo "<p><strong>🎯 Todas las páginas están listas para uso en producción.</strong></p>\n";
            echo "</div>\n";
        } else {
            echo "<div class='error'>\n";
            echo "<h3>⚠️ Sistema Parcialmente Funcional</h3>\n";
            echo "<p>Páginas completadas: {$working_pages}/{$total_pages}</p>\n";
            echo "<p>Revisa los templates faltantes arriba.</p>\n";
            echo "</div>\n";
        }
        ?>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="https://tuasesorlegalvirtual.online/lexai-login/" class="btn btn-success" target="_blank">
                🔑 Probar Login
            </a>
            <a href="https://tuasesorlegalvirtual.online/lexai-register/" class="btn btn-success" target="_blank">
                📝 Probar Register
            </a>
            <a href="https://tuasesorlegalvirtual.online/lexai-reset-password/" class="btn btn-success" target="_blank">
                🔄 Probar Reset Password
            </a>
            <a href="javascript:location.reload();" class="btn btn-warning">
                🔄 Recargar Test
            </a>
        </div>
        
        <script>
            console.log('🔐 LexAI Auth Pages Complete Test');
            console.log('Total pages:', <?php echo $total_pages; ?>);
            console.log('Working pages:', <?php echo $working_pages; ?>);
            console.log('Completion:', '<?php echo $completion_percentage; ?>%');
        </script>
    </div>
</body>
</html>
