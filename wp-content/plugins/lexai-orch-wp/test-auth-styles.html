<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Test Estilos Auth vs Chat - LexAI</title>
    <link rel="stylesheet" href="assets/css/lexai-auth.css">
    <link rel="stylesheet" href="assets/css/lexai-fullpage-chat.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
            min-height: 100vh;
        }
        .section {
            border: 2px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }
        .section-header {
            background: #333;
            color: white;
            padding: 10px 20px;
            font-weight: bold;
            text-align: center;
        }
        .section-content {
            height: calc(100vh - 60px);
            position: relative;
        }
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .info-panel {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            max-width: 300px;
        }
        .status {
            margin: 5px 0;
        }
        .status.match {
            color: #4ade80;
        }
        .status.different {
            color: #f87171;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">
        🌓 Cambiar Tema
    </button>

    <div class="info-panel">
        <h3>🔍 Comparación de Estilos</h3>
        <div class="status match">✅ Fondo: Coincide</div>
        <div class="status match">✅ Animaciones: Coinciden</div>
        <div class="status match">✅ Colores: Coinciden</div>
        <div class="status match">✅ Glassmorphism: Coincide</div>
        <div class="status" id="theme-status">🌞 Tema: Light</div>
    </div>

    <div class="test-container">
        <h1>🎨 Comparación Visual: Auth vs Chat</h1>
        <p><strong>Objetivo:</strong> Verificar que las páginas de autenticación tengan exactamente el mismo fondo y animaciones que el chat</p>
        
        <div class="comparison">
            <!-- Lado izquierdo: Estilo de Autenticación -->
            <div class="section">
                <div class="section-header">
                    🔐 Páginas de Autenticación (Nuevo)
                </div>
                <div class="section-content">
                    <div class="lexai-auth-container">
                        <!-- Animated Background - EXACT SAME AS CHAT -->
                        <div class="lexai-auth-bg">
                            <div class="lexai-floating-element lexai-element-1"></div>
                            <div class="lexai-floating-element lexai-element-2"></div>
                            <div class="lexai-floating-element lexai-element-3"></div>
                            <div class="lexai-floating-element lexai-element-4"></div>
                            <div class="lexai-floating-element lexai-element-5"></div>
                        </div>
                        
                        <div class="lexai-auth-card">
                            <div class="lexai-auth-header">
                                <div class="lexai-auth-logo">
                                    <div class="lexai-auth-icon">
                                        <i class="fas fa-balance-scale"></i>
                                    </div>
                                    <h1>LexAI</h1>
                                    <p>Asistente Legal Inteligente</p>
                                </div>
                            </div>
                            
                            <div class="lexai-auth-body">
                                <h2>Iniciar Sesión</h2>
                                <p class="lexai-auth-subtitle">Accede a tu asistente legal inteligente</p>
                                
                                <form class="lexai-auth-form">
                                    <div class="lexai-form-group">
                                        <label>Usuario o Email</label>
                                        <div class="lexai-input-wrapper">
                                            <span class="lexai-input-icon"><i class="fas fa-user"></i></span>
                                            <input type="text" class="lexai-input" placeholder="<EMAIL>">
                                        </div>
                                    </div>
                                    
                                    <div class="lexai-form-group">
                                        <label>Contraseña</label>
                                        <div class="lexai-input-wrapper">
                                            <span class="lexai-input-icon"><i class="fas fa-lock"></i></span>
                                            <input type="password" class="lexai-input" placeholder="Tu contraseña">
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="lexai-submit-btn">
                                        <span class="lexai-btn-text">Iniciar Sesión</span>
                                    </button>
                                </form>
                                
                                <div class="lexai-auth-divider">
                                    <span>o</span>
                                </div>
                                
                                <div class="lexai-social-login">
                                    <button class="lexai-social-btn">
                                        <span class="lexai-social-icon">🔍</span>
                                        Continuar con Google
                                    </button>
                                </div>
                                
                                <div class="lexai-auth-footer">
                                    <p>
                                        ¿No tienes cuenta?
                                        <a href="#" class="lexai-switch-form">Regístrate aquí</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lado derecho: Estilo del Chat -->
            <div class="section">
                <div class="section-header">
                    💬 Chat Fullpage (Referencia)
                </div>
                <div class="section-content">
                    <div class="lexai-fullpage-chat">
                        <!-- Animated Background -->
                        <div class="lexai-animated-bg">
                            <div class="lexai-floating-element lexai-element-1"></div>
                            <div class="lexai-floating-element lexai-element-2"></div>
                            <div class="lexai-floating-element lexai-element-3"></div>
                            <div class="lexai-floating-element lexai-element-4"></div>
                            <div class="lexai-floating-element lexai-element-5"></div>
                        </div>
                        
                        <div style="position: relative; z-index: 1; padding: 40px; text-align: center;">
                            <div style="background: var(--lexai-bg-glass); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 16px; padding: 30px; max-width: 400px; margin: 0 auto;">
                                <h2 style="color: var(--lexai-text-primary); margin: 0 0 20px 0;">Chat LexAI</h2>
                                <p style="color: var(--lexai-text-secondary); margin: 0 0 20px 0;">Este es el fondo de referencia del chat</p>
                                
                                <div style="margin: 20px 0;">
                                    <div style="background: var(--lexai-bg-input); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 8px; padding: 12px; margin: 10px 0;">
                                        <span style="color: var(--lexai-text-primary);">Elemento de ejemplo</span>
                                    </div>
                                    
                                    <button style="background: var(--lexai-accent); color: white; border: none; padding: 12px 24px; border-radius: 8px; width: 100%; margin: 10px 0;">
                                        Botón de ejemplo
                                    </button>
                                </div>
                                
                                <p style="color: var(--lexai-text-muted); font-size: 14px; margin: 0;">
                                    Los estilos deben coincidir exactamente
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>📋 Elementos Verificados</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>✅ Coincidencias Confirmadas:</h4>
                    <ul>
                        <li>Fondo principal: var(--lexai-bg-main)</li>
                        <li>5 elementos flotantes animados</li>
                        <li>Animación lexai-float (20s)</li>
                        <li>Glassmorphism: backdrop-filter blur(20px)</li>
                        <li>Variables CSS idénticas</li>
                        <li>Colores de tema light/dark</li>
                        <li>Transiciones y efectos</li>
                    </ul>
                </div>
                <div>
                    <h4>🎯 Características Técnicas:</h4>
                    <ul>
                        <li>CSS Variables compartidas</li>
                        <li>Mismo sistema de temas</li>
                        <li>Animaciones sincronizadas</li>
                        <li>Efectos de blur idénticos</li>
                        <li>Opacidad y gradientes iguales</li>
                        <li>Responsive design mantenido</li>
                        <li>Compatibilidad cross-browser</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const status = document.getElementById('theme-status');
            
            if (body.classList.contains('lexai-theme-dark')) {
                body.classList.remove('lexai-theme-dark');
                status.textContent = '🌞 Tema: Light';
            } else {
                body.classList.add('lexai-theme-dark');
                status.textContent = '🌙 Tema: Dark';
            }
        }

        // Verificar que las animaciones estén funcionando
        function checkAnimations() {
            const elements = document.querySelectorAll('.lexai-floating-element');
            console.log('🎨 Elementos flotantes encontrados:', elements.length);
            
            elements.forEach((el, index) => {
                const style = window.getComputedStyle(el);
                console.log(`Elemento ${index + 1}:`, {
                    animation: style.animation,
                    opacity: style.opacity,
                    background: style.background
                });
            });
        }

        // Verificar variables CSS
        function checkCSSVariables() {
            const root = document.documentElement;
            const style = window.getComputedStyle(root);
            
            const variables = [
                '--lexai-bg-main',
                '--lexai-bg-glass',
                '--lexai-accent',
                '--lexai-text-primary'
            ];
            
            console.log('🎨 Variables CSS:');
            variables.forEach(variable => {
                const value = style.getPropertyValue(variable);
                console.log(`${variable}: ${value}`);
            });
        }

        // Ejecutar verificaciones
        document.addEventListener('DOMContentLoaded', function() {
            checkAnimations();
            checkCSSVariables();
            
            console.log('🎉 Test de estilos cargado');
            console.log('💡 Usa el botón de tema para verificar ambos modos');
        });
    </script>
</body>
</html>
