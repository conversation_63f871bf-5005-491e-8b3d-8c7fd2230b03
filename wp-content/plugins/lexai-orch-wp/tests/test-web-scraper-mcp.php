<?php
/**
 * Test del MCP Web Scraper Native
 * 
 * Test completo de capacidades y funcionamiento del Web Scraper MCP
 * Simula un agente legal usando la herramienta para diferentes casos de uso
 *
 * @package LexAI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Clase de Test para Web Scraper MCP
 */
class LexAI_Web_Scraper_MCP_Test {

    private $web_scraper;
    private $test_results = array();

    public function __construct() {
        // Load required files
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/interfaces/interface-mcp-tool.php';
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-web-scraper-tool-native.php';
        
        $this->web_scraper = new LexAI_Web_Scraper_Tool_Native();
    }

    /**
     * Ejecutar todos los tests
     */
    public function run_all_tests() {
        echo "<h1>🧪 Test Completo del MCP Web Scraper Native</h1>\n";
        echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
        
        // Test 1: Validación de herramienta
        $this->test_tool_validation();
        
        // Test 2: Extracción de texto de SCJN
        $this->test_scjn_text_extraction();
        
        // Test 3: Extracción de metadatos de DOF
        $this->test_dof_metadata_extraction();
        
        // Test 4: Extracción de enlaces de Orden Jurídico
        $this->test_orden_juridico_links();
        
        // Test 5: Extracción HTML específica
        $this->test_html_extraction();
        
        // Test 6: Validación de seguridad
        $this->test_security_validation();
        
        // Test 7: Manejo de errores
        $this->test_error_handling();
        
        // Test 8: Simulación de agente legal
        $this->test_legal_agent_simulation();
        
        // Mostrar resumen
        $this->show_test_summary();
    }

    /**
     * Test 1: Validación básica de la herramienta
     */
    private function test_tool_validation() {
        echo "<h2>📋 Test 1: Validación de Herramienta</h2>\n";
        
        try {
            $name = $this->web_scraper->get_name();
            $description = $this->web_scraper->get_description();
            $schema = $this->web_scraper->get_schema();
            $category = $this->web_scraper->get_category();
            $is_available = $this->web_scraper->is_available();
            
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ Herramienta Válida</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>Nombre:</strong> {$name}</li>\n";
            echo "<li><strong>Descripción:</strong> {$description}</li>\n";
            echo "<li><strong>Categoría:</strong> {$category}</li>\n";
            echo "<li><strong>Disponible:</strong> " . ($is_available ? 'Sí' : 'No') . "</li>\n";
            echo "<li><strong>Parámetros requeridos:</strong> " . implode(', ', $schema['required']) . "</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
            
            $this->test_results['tool_validation'] = 'PASS';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Validación</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['tool_validation'] = 'FAIL';
        }
    }

    /**
     * Test 2: Extracción de texto de SCJN
     */
    private function test_scjn_text_extraction() {
        echo "<h2>🏛️ Test 2: Extracción de Texto SCJN</h2>\n";
        
        $test_url = 'https://www.scjn.gob.mx/';
        
        try {
            $parameters = array(
                'url' => $test_url,
                'extract_type' => 'text',
                'max_length' => 2000
            );
            
            $result = $this->web_scraper->execute($parameters);
            
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ Extracción Exitosa</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>URL:</strong> {$result['url']}</li>\n";
            echo "<li><strong>Tipo:</strong> {$result['extract_type']}</li>\n";
            echo "<li><strong>Longitud:</strong> {$result['length']} caracteres</li>\n";
            echo "<li><strong>Timestamp:</strong> {$result['timestamp']}</li>\n";
            echo "</ul>\n";
            echo "<h4>📄 Contenido Extraído (primeros 500 caracteres):</h4>\n";
            echo "<div class='content-preview'>" . htmlspecialchars(substr($result['content'], 0, 500)) . "...</div>\n";
            echo "</div>\n";
            
            $this->test_results['scjn_extraction'] = 'PASS';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Extracción SCJN</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['scjn_extraction'] = 'FAIL';
        }
    }

    /**
     * Test 3: Extracción de metadatos de DOF
     */
    private function test_dof_metadata_extraction() {
        echo "<h2>📰 Test 3: Extracción de Metadatos DOF</h2>\n";
        
        $test_url = 'https://www.dof.gob.mx/';
        
        try {
            $parameters = array(
                'url' => $test_url,
                'extract_type' => 'metadata'
            );
            
            $result = $this->web_scraper->execute($parameters);
            $metadata = json_decode($result['content'], true);
            
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ Metadatos Extraídos</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>URL:</strong> {$result['url']}</li>\n";
            echo "<li><strong>Longitud:</strong> {$result['length']} caracteres</li>\n";
            echo "</ul>\n";
            echo "<h4>🏷️ Metadatos Encontrados:</h4>\n";
            echo "<pre>" . htmlspecialchars(json_encode($metadata, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>\n";
            echo "</div>\n";
            
            $this->test_results['dof_metadata'] = 'PASS';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Extracción DOF</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['dof_metadata'] = 'FAIL';
        }
    }

    /**
     * Test 4: Extracción de enlaces de Orden Jurídico
     */
    private function test_orden_juridico_links() {
        echo "<h2>🔗 Test 4: Extracción de Enlaces Orden Jurídico</h2>\n";
        
        $test_url = 'https://www.ordenjuridico.gob.mx/';
        
        try {
            $parameters = array(
                'url' => $test_url,
                'extract_type' => 'links'
            );
            
            $result = $this->web_scraper->execute($parameters);
            $links = json_decode($result['content'], true);
            
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ Enlaces Extraídos</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>URL:</strong> {$result['url']}</li>\n";
            echo "<li><strong>Total Enlaces:</strong> " . count($links) . "</li>\n";
            echo "</ul>\n";
            echo "<h4>🔗 Primeros 5 Enlaces:</h4>\n";
            echo "<ul>\n";
            for ($i = 0; $i < min(5, count($links)); $i++) {
                echo "<li><strong>{$links[$i]['text']}</strong> → {$links[$i]['url']}</li>\n";
            }
            echo "</ul>\n";
            echo "</div>\n";
            
            $this->test_results['orden_juridico_links'] = 'PASS';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Extracción Orden Jurídico</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['orden_juridico_links'] = 'FAIL';
        }
    }

    /**
     * Test 5: Extracción HTML específica
     */
    private function test_html_extraction() {
        echo "<h2>🏗️ Test 5: Extracción HTML Específica</h2>\n";
        
        $test_url = 'https://www.juridicas.unam.mx/';
        
        try {
            $parameters = array(
                'url' => $test_url,
                'extract_type' => 'html',
                'selector' => 'header',
                'max_length' => 1000
            );
            
            $result = $this->web_scraper->execute($parameters);
            
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ HTML Extraído</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>URL:</strong> {$result['url']}</li>\n";
            echo "<li><strong>Selector:</strong> header</li>\n";
            echo "<li><strong>Longitud:</strong> {$result['length']} caracteres</li>\n";
            echo "</ul>\n";
            echo "<h4>🏗️ HTML Extraído:</h4>\n";
            echo "<div class='html-preview'>" . htmlspecialchars(substr($result['content'], 0, 500)) . "...</div>\n";
            echo "</div>\n";
            
            $this->test_results['html_extraction'] = 'PASS';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Extracción HTML</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['html_extraction'] = 'FAIL';
        }
    }

    /**
     * Test 6: Validación de seguridad
     */
    private function test_security_validation() {
        echo "<h2>🛡️ Test 6: Validación de Seguridad</h2>\n";
        
        $forbidden_urls = array(
            'https://google.com/',
            'https://facebook.com/',
            'https://malicious-site.com/',
            'http://localhost/',
            'https://example.com/'
        );
        
        $blocked_count = 0;
        
        foreach ($forbidden_urls as $url) {
            try {
                $parameters = array(
                    'url' => $url,
                    'extract_type' => 'text'
                );
                
                $result = $this->web_scraper->execute($parameters);
                echo "<div class='test-result warning'>\n";
                echo "<p>⚠️ URL no bloqueada: {$url}</p>\n";
                echo "</div>\n";
                
            } catch (Exception $e) {
                $blocked_count++;
                echo "<div class='test-result success'>\n";
                echo "<p>✅ URL correctamente bloqueada: {$url}</p>\n";
                echo "<p>Razón: {$e->getMessage()}</p>\n";
                echo "</div>\n";
            }
        }
        
        if ($blocked_count === count($forbidden_urls)) {
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ Seguridad Validada</h3>\n";
            echo "<p>Todas las URLs no autorizadas fueron correctamente bloqueadas.</p>\n";
            echo "</div>\n";
            
            $this->test_results['security_validation'] = 'PASS';
        } else {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Falla de Seguridad</h3>\n";
            echo "<p>Algunas URLs no autorizadas no fueron bloqueadas.</p>\n";
            echo "</div>\n";
            
            $this->test_results['security_validation'] = 'FAIL';
        }
    }

    /**
     * Test 7: Manejo de errores
     */
    private function test_error_handling() {
        echo "<h2>⚠️ Test 7: Manejo de Errores</h2>\n";
        
        $error_tests = array(
            array(
                'name' => 'URL inválida',
                'params' => array('url' => 'not-a-url'),
                'expected_error' => 'Invalid URL format'
            ),
            array(
                'name' => 'URL inexistente',
                'params' => array('url' => 'https://scjn.gob.mx/nonexistent-page-12345'),
                'expected_error' => 'HTTP'
            ),
            array(
                'name' => 'Tipo de extracción inválido',
                'params' => array('url' => 'https://scjn.gob.mx/', 'extract_type' => 'invalid'),
                'expected_error' => 'Invalid extract_type'
            )
        );
        
        $errors_handled = 0;
        
        foreach ($error_tests as $test) {
            try {
                $result = $this->web_scraper->execute($test['params']);
                echo "<div class='test-result warning'>\n";
                echo "<p>⚠️ Error no detectado: {$test['name']}</p>\n";
                echo "</div>\n";
                
            } catch (Exception $e) {
                $errors_handled++;
                echo "<div class='test-result success'>\n";
                echo "<p>✅ Error correctamente manejado: {$test['name']}</p>\n";
                echo "<p>Mensaje: {$e->getMessage()}</p>\n";
                echo "</div>\n";
            }
        }
        
        if ($errors_handled === count($error_tests)) {
            $this->test_results['error_handling'] = 'PASS';
        } else {
            $this->test_results['error_handling'] = 'FAIL';
        }
    }

    /**
     * Test 8: Simulación de agente legal
     */
    private function test_legal_agent_simulation() {
        echo "<h2>🤖 Test 8: Simulación de Agente Legal</h2>\n";
        echo "<p><em>Simulando un agente legal que necesita investigar información sobre una nueva ley...</em></p>\n";
        
        $agent_tasks = array(
            array(
                'task' => 'Buscar información en SCJN',
                'url' => 'https://www.scjn.gob.mx/',
                'extract_type' => 'text',
                'purpose' => 'Obtener información general sobre la Suprema Corte'
            ),
            array(
                'task' => 'Extraer metadatos del DOF',
                'url' => 'https://www.dof.gob.mx/',
                'extract_type' => 'metadata',
                'purpose' => 'Verificar estructura y metadatos del Diario Oficial'
            ),
            array(
                'task' => 'Analizar enlaces del Orden Jurídico',
                'url' => 'https://www.ordenjuridico.gob.mx/',
                'extract_type' => 'links',
                'purpose' => 'Identificar secciones relevantes del marco jurídico'
            )
        );
        
        $successful_tasks = 0;
        
        foreach ($agent_tasks as $task) {
            echo "<h4>📋 {$task['task']}</h4>\n";
            echo "<p><strong>Propósito:</strong> {$task['purpose']}</p>\n";
            
            try {
                $parameters = array(
                    'url' => $task['url'],
                    'extract_type' => $task['extract_type'],
                    'max_length' => 1500
                );
                
                $result = $this->web_scraper->execute($parameters);
                
                echo "<div class='test-result success'>\n";
                echo "<p>✅ Tarea completada exitosamente</p>\n";
                echo "<ul>\n";
                echo "<li><strong>Datos extraídos:</strong> {$result['length']} caracteres</li>\n";
                echo "<li><strong>Tiempo:</strong> {$result['timestamp']}</li>\n";
                echo "</ul>\n";
                
                // Análisis específico según el tipo
                if ($task['extract_type'] === 'links') {
                    $links = json_decode($result['content'], true);
                    echo "<li><strong>Enlaces encontrados:</strong> " . count($links) . "</li>\n";
                } elseif ($task['extract_type'] === 'metadata') {
                    $metadata = json_decode($result['content'], true);
                    echo "<li><strong>Metadatos extraídos:</strong> " . count($metadata) . " elementos</li>\n";
                }
                
                echo "</div>\n";
                $successful_tasks++;
                
            } catch (Exception $e) {
                echo "<div class='test-result error'>\n";
                echo "<p>❌ Tarea fallida: {$e->getMessage()}</p>\n";
                echo "</div>\n";
            }
        }
        
        echo "<div class='agent-summary'>\n";
        echo "<h4>📊 Resumen del Agente</h4>\n";
        echo "<p><strong>Tareas completadas:</strong> {$successful_tasks}/{" . count($agent_tasks) . "}</p>\n";
        echo "<p><strong>Tasa de éxito:</strong> " . round(($successful_tasks / count($agent_tasks)) * 100, 1) . "%</p>\n";
        
        if ($successful_tasks === count($agent_tasks)) {
            echo "<p>✅ <strong>El agente puede realizar investigación legal efectiva</strong></p>\n";
            $this->test_results['agent_simulation'] = 'PASS';
        } else {
            echo "<p>⚠️ <strong>El agente tiene limitaciones en algunas tareas</strong></p>\n";
            $this->test_results['agent_simulation'] = 'PARTIAL';
        }
        echo "</div>\n";
    }

    /**
     * Mostrar resumen de tests
     */
    private function show_test_summary() {
        echo "<h2>📊 Resumen de Tests</h2>\n";
        
        $total_tests = count($this->test_results);
        $passed_tests = count(array_filter($this->test_results, function($result) {
            return $result === 'PASS';
        }));
        $partial_tests = count(array_filter($this->test_results, function($result) {
            return $result === 'PARTIAL';
        }));
        
        echo "<div class='summary-box'>\n";
        echo "<h3>🎯 Resultados Finales</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Tests ejecutados:</strong> {$total_tests}</li>\n";
        echo "<li><strong>Tests exitosos:</strong> {$passed_tests}</li>\n";
        echo "<li><strong>Tests parciales:</strong> {$partial_tests}</li>\n";
        echo "<li><strong>Tests fallidos:</strong> " . ($total_tests - $passed_tests - $partial_tests) . "</li>\n";
        echo "<li><strong>Tasa de éxito:</strong> " . round(($passed_tests / $total_tests) * 100, 1) . "%</li>\n";
        echo "</ul>\n";
        
        echo "<h4>📋 Detalle por Test:</h4>\n";
        echo "<ul>\n";
        foreach ($this->test_results as $test_name => $result) {
            $icon = $result === 'PASS' ? '✅' : ($result === 'PARTIAL' ? '⚠️' : '❌');
            echo "<li>{$icon} {$test_name}: {$result}</li>\n";
        }
        echo "</ul>\n";
        
        // Evaluación general
        if ($passed_tests >= 6) {
            echo "<div class='final-verdict success'>\n";
            echo "<h3>🎉 VEREDICTO: MCP WEB SCRAPER FUNCIONAL</h3>\n";
            echo "<p>El MCP Web Scraper está listo para uso en producción con agentes legales.</p>\n";
            echo "</div>\n";
        } elseif ($passed_tests >= 4) {
            echo "<div class='final-verdict warning'>\n";
            echo "<h3>⚠️ VEREDICTO: MCP WEB SCRAPER PARCIALMENTE FUNCIONAL</h3>\n";
            echo "<p>El MCP Web Scraper funciona pero requiere ajustes antes de producción.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div class='final-verdict error'>\n";
            echo "<h3>❌ VEREDICTO: MCP WEB SCRAPER REQUIERE CORRECCIONES</h3>\n";
            echo "<p>El MCP Web Scraper necesita correcciones significativas.</p>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
    }
}

// CSS para mejorar la presentación
echo "<style>
.test-result { margin: 10px 0; padding: 15px; border-radius: 5px; }
.test-result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.test-result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.test-result.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.content-preview, .html-preview { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
.summary-box { background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0; }
.agent-summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
.final-verdict { padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
.final-verdict.success { background: #d1ecf1; border: 2px solid #bee5eb; color: #0c5460; }
.final-verdict.warning { background: #fff3cd; border: 2px solid #ffeaa7; color: #856404; }
.final-verdict.error { background: #f8d7da; border: 2px solid #f5c6cb; color: #721c24; }
</style>";

// Ejecutar tests si se accede directamente
if (basename($_SERVER['PHP_SELF']) === 'test-web-scraper-mcp.php') {
    $test = new LexAI_Web_Scraper_MCP_Test();
    $test->run_all_tests();
}
?>
