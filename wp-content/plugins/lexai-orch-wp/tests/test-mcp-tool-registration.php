<?php
/**
 * Test de Registro de Herramientas MCP
 * 
 * Verifica que las herramientas MCP se registren correctamente
 * y estén disponibles para los agentes
 *
 * @package LexAI
 * @since 2.1.0
 */

// Simular entorno WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/');
}

echo "<h1>🔧 Test de Registro de Herramientas MCP</h1>\n";
echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

// Test 1: Verificar que las herramientas MCP se pueden instanciar
echo "<h2>📋 Test 1: Instanciación de Herramientas MCP</h2>\n";

try {
    // Load MCP interfaces
    require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/interfaces/interface-mcp-tool.php';
    echo "<p>✅ Interface MCP Tool cargada</p>\n";
    
    // Load Web Scraper MCP
    require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-web-scraper-tool-native.php';
    $web_scraper = new LexAI_Web_Scraper_Tool_Native();
    echo "<p>✅ Web Scraper MCP instanciado</p>\n";
    
    // Load Pinecone MCP
    require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-pinecone-tool-native.php';
    $pinecone_tool = new LexAI_Pinecone_Tool_Native();
    echo "<p>✅ Pinecone MCP instanciado</p>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error al instanciar herramientas MCP:</strong> " . $e->getMessage() . "</p>\n";
}

// Test 2: Verificar propiedades de las herramientas
echo "<h2>🔍 Test 2: Propiedades de Herramientas MCP</h2>\n";

if (isset($web_scraper)) {
    echo "<h3>🌐 Web Scraper MCP</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Nombre:</strong> " . $web_scraper->get_name() . "</li>\n";
    echo "<li><strong>Descripción:</strong> " . $web_scraper->get_description() . "</li>\n";
    echo "<li><strong>Categoría:</strong> " . $web_scraper->get_category() . "</li>\n";
    echo "<li><strong>Disponible:</strong> " . ($web_scraper->is_available() ? 'Sí' : 'No') . "</li>\n";
    
    $metadata = $web_scraper->get_metadata();
    echo "<li><strong>Versión:</strong> " . $metadata['version'] . "</li>\n";
    echo "<li><strong>Tipo:</strong> " . $metadata['type'] . "</li>\n";
    echo "<li><strong>Capacidades:</strong> " . count($metadata['capabilities']) . "</li>\n";
    echo "</ul>\n";
    
    echo "<h4>🎯 Capacidades del Web Scraper:</h4>\n";
    echo "<ul>\n";
    foreach ($metadata['capabilities'] as $capability) {
        echo "<li>✅ " . str_replace('_', ' ', ucfirst($capability)) . "</li>\n";
    }
    echo "</ul>\n";
}

if (isset($pinecone_tool)) {
    echo "<h3>🔍 Pinecone MCP</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Nombre:</strong> " . $pinecone_tool->get_name() . "</li>\n";
    echo "<li><strong>Descripción:</strong> " . $pinecone_tool->get_description() . "</li>\n";
    echo "<li><strong>Categoría:</strong> " . $pinecone_tool->get_category() . "</li>\n";
    echo "<li><strong>Disponible:</strong> " . ($pinecone_tool->is_available() ? 'Sí' : 'No') . "</li>\n";
    echo "</ul>\n";
}

// Test 3: Verificar schemas de herramientas
echo "<h2>📋 Test 3: Schemas de Herramientas</h2>\n";

if (isset($web_scraper)) {
    echo "<h3>🌐 Schema Web Scraper</h3>\n";
    $schema = $web_scraper->get_schema();
    echo "<ul>\n";
    echo "<li><strong>Tipo:</strong> " . $schema['type'] . "</li>\n";
    echo "<li><strong>Propiedades:</strong> " . count($schema['properties']) . "</li>\n";
    echo "<li><strong>Requeridos:</strong> " . implode(', ', $schema['required']) . "</li>\n";
    echo "</ul>\n";
    
    echo "<h4>📄 Propiedades del Schema:</h4>\n";
    echo "<ul>\n";
    foreach ($schema['properties'] as $prop_name => $prop_details) {
        echo "<li><strong>{$prop_name}</strong> ({$prop_details['type']}) - {$prop_details['description']}</li>\n";
    }
    echo "</ul>\n";
}

// Test 4: Verificar MCP Manager
echo "<h2>🎛️ Test 4: MCP Manager</h2>\n";

try {
    require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/class-lexai-mcp-manager-native.php';
    $mcp_manager = new LexAI_MCP_Manager_Native();
    echo "<p>✅ MCP Manager instanciado</p>\n";
    
    $available_tools = $mcp_manager->get_available_tools();
    echo "<p><strong>Herramientas disponibles:</strong> " . count($available_tools) . "</p>\n";
    
    echo "<h4>🛠️ Herramientas Registradas:</h4>\n";
    echo "<ul>\n";
    foreach ($available_tools as $tool) {
        echo "<li><strong>{$tool['name']}</strong> - {$tool['description']}</li>\n";
        echo "<ul>\n";
        echo "<li>Categoría: {$tool['category']}</li>\n";
        echo "<li>Implementación: {$tool['implementation']}</li>\n";
        echo "</ul>\n";
    }
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p>❌ <strong>Error con MCP Manager:</strong> " . $e->getMessage() . "</p>\n";
}

// Test 5: Verificar mapeo en Agent Factory
echo "<h2>🤖 Test 5: Mapeo en Agent Factory</h2>\n";

// Definir herramientas del Agent Factory que usan MCP
$agent_factory_tools = array(
    'legal_knowledge_base' => 'Base de Conocimientos Legales (Pinecone MCP - Namespace: leyesycodigos)',
    'jurisprudence_search' => 'Búsqueda de Jurisprudencia (Pinecone MCP - Namespace: jurisprudencia)',
    'scjn_thesis_search' => 'Búsqueda de Tesis SCJN (Pinecone MCP - Namespace: tesisscjn)',
    'legal_templates_search' => 'Búsqueda de Templates Legales (Pinecone MCP - Namespace: formatos)',
    'legal_research' => 'Investigación Jurídica (Web Scraper MCP Nativo)',
    'legal_news' => 'Noticias y Actualizaciones Legales (Web Scraper MCP Nativo)'
);

echo "<h3>📋 Herramientas del Agent Factory que usan MCP:</h3>\n";
echo "<ul>\n";
foreach ($agent_factory_tools as $tool_name => $description) {
    echo "<li><strong>{$tool_name}</strong> - {$description}</li>\n";
}
echo "</ul>\n";

// Test 6: Verificar Tool Executor mapping
echo "<h2>⚡ Test 6: Tool Executor Mapping</h2>\n";

// Mapeo actual en Tool Executor
$current_mapping = array(
    'search-records' => 'pinecone_search_native',
    'legal_research' => 'web_scraper_native',  // ❌ INCORRECTO
    'google_search' => 'web_scraper_native'    // ❌ INCORRECTO
);

// Mapeo correcto basado en herramientas MCP disponibles
$correct_mapping = array(
    'legal_knowledge_base' => 'pinecone_search_native',
    'jurisprudence_search' => 'pinecone_search_native',
    'scjn_thesis_search' => 'pinecone_search_native',
    'legal_templates_search' => 'pinecone_search_native',
    'legal_research' => 'web_scraper_advanced',  // ✅ CORRECTO
    'legal_news' => 'web_scraper_advanced'       // ✅ CORRECTO
);

echo "<h3>❌ Mapeo Actual (Incorrecto):</h3>\n";
echo "<ul>\n";
foreach ($current_mapping as $agent_tool => $mcp_tool) {
    echo "<li><strong>{$agent_tool}</strong> → {$mcp_tool}</li>\n";
}
echo "</ul>\n";

echo "<h3>✅ Mapeo Correcto Requerido:</h3>\n";
echo "<ul>\n";
foreach ($correct_mapping as $agent_tool => $mcp_tool) {
    echo "<li><strong>{$agent_tool}</strong> → {$mcp_tool}</li>\n";
}
echo "</ul>\n";

// Test 7: Problemas identificados
echo "<h2>⚠️ Test 7: Problemas Identificados</h2>\n";

echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
echo "<h3>🚨 Problemas de Compatibilidad Detectados</h3>\n";
echo "<ol>\n";
echo "<li><strong>Nombre de herramienta incorrecto:</strong> El Web Scraper MCP se llama 'web_scraper_advanced' pero el Tool Executor mapea a 'web_scraper_native'</li>\n";
echo "<li><strong>Mapeo incompleto:</strong> Faltan mapeos para legal_knowledge_base, jurisprudence_search, scjn_thesis_search, legal_templates_search</li>\n";
echo "<li><strong>Mapeo incorrecto:</strong> google_search no debería mapear a web_scraper</li>\n";
echo "<li><strong>Falta de validación:</strong> No hay validación de que las herramientas MCP estén disponibles antes del mapeo</li>\n";
echo "</ol>\n";
echo "</div>\n";

// Test 8: Soluciones recomendadas
echo "<h2>💡 Test 8: Soluciones Recomendadas</h2>\n";

echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
echo "<h3>🔧 Correcciones Necesarias</h3>\n";
echo "<ol>\n";
echo "<li><strong>Actualizar Tool Executor:</strong> Corregir el mapeo de herramientas en class-lexai-tool-executor.php</li>\n";
echo "<li><strong>Agregar mapeos faltantes:</strong> Incluir todas las herramientas del Agent Factory</li>\n";
echo "<li><strong>Validar disponibilidad:</strong> Verificar que las herramientas MCP estén disponibles antes de mapear</li>\n";
echo "<li><strong>Documentar mapeos:</strong> Crear documentación clara del mapeo Agent Factory → MCP</li>\n";
echo "<li><strong>Tests automáticos:</strong> Implementar tests de compatibilidad en CI/CD</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h2>🎯 Resumen Final</h2>\n";
echo "<div style='background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
echo "<h3>📊 Estado de Compatibilidad</h3>\n";
echo "<ul>\n";
echo "<li>✅ <strong>Herramientas MCP:</strong> Funcionando correctamente</li>\n";
echo "<li>✅ <strong>Schemas:</strong> Definidos y válidos</li>\n";
echo "<li>✅ <strong>MCP Manager:</strong> Registrando herramientas</li>\n";
echo "<li>❌ <strong>Tool Executor:</strong> Mapeo incorrecto</li>\n";
echo "<li>⚠️ <strong>Agent Factory:</strong> Schemas definidos pero mapeo roto</li>\n";
echo "</ul>\n";

echo "<h4>🎉 Conclusión</h4>\n";
echo "<p><strong>Las herramientas MCP están funcionando perfectamente, pero el mapeo en Tool Executor necesita corrección para que los agentes puedan usarlas.</strong></p>\n";
echo "</div>\n";

echo "<style>
h1, h2, h3 { color: #333; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
p { margin: 10px 0; }
</style>";
?>
