<?php
/**
 * Simulación de Agente Legal usando MCP Web Scraper
 * 
 * Simula casos de uso reales de un agente legal que necesita investigar
 * información jurídica usando el MCP Web Scraper Native
 *
 * @package LexAI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Simulador de Agente Legal
 */
class LexAI_Legal_Agent_Simulator {

    private $web_scraper;
    private $investigation_results = array();

    public function __construct() {
        // Load required files
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/interfaces/interface-mcp-tool.php';
        require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-web-scraper-tool-native.php';
        
        $this->web_scraper = new LexAI_Web_Scraper_Tool_Native();
    }

    /**
     * Simular investigación legal completa
     */
    public function simulate_legal_investigation() {
        echo "<h1>🤖 Simulación de Agente Legal - Investigación Jurídica</h1>\n";
        echo "<p><strong>Escenario:</strong> Un abogado necesita investigar sobre reformas constitucionales recientes</p>\n";
        echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
        
        // Caso 1: Investigación en SCJN
        $this->investigate_scjn_reforms();
        
        // Caso 2: Búsqueda en DOF
        $this->search_dof_publications();
        
        // Caso 3: Análisis del marco jurídico
        $this->analyze_legal_framework();
        
        // Caso 4: Investigación académica
        $this->research_academic_sources();
        
        // Caso 5: Análisis de tribunales
        $this->analyze_tribunal_decisions();
        
        // Generar reporte final
        $this->generate_investigation_report();
    }

    /**
     * Caso 1: Investigación en SCJN sobre reformas
     */
    private function investigate_scjn_reforms() {
        echo "<h2>🏛️ Caso 1: Investigación en SCJN</h2>\n";
        echo "<p><strong>Objetivo:</strong> Buscar información sobre reformas constitucionales y jurisprudencia</p>\n";
        
        $scjn_urls = array(
            'https://www.scjn.gob.mx/' => 'Página principal',
            'https://www.scjn.gob.mx/derechos-humanos' => 'Derechos humanos',
            'https://www.scjn.gob.mx/reforma-judicial' => 'Reforma judicial'
        );
        
        foreach ($scjn_urls as $url => $description) {
            echo "<h3>📋 Investigando: {$description}</h3>\n";
            
            try {
                $parameters = array(
                    'url' => $url,
                    'extract_type' => 'text',
                    'max_length' => 3000
                );
                
                $result = $this->web_scraper->execute($parameters);
                
                // Simular análisis del contenido
                $content = $result['content'];
                $keywords_found = $this->analyze_legal_keywords($content);
                
                echo "<div class='investigation-result success'>\n";
                echo "<h4>✅ Información Extraída</h4>\n";
                echo "<ul>\n";
                echo "<li><strong>Fuente:</strong> {$description}</li>\n";
                echo "<li><strong>Contenido:</strong> {$result['length']} caracteres</li>\n";
                echo "<li><strong>Palabras clave legales:</strong> " . implode(', ', $keywords_found) . "</li>\n";
                echo "<li><strong>Relevancia:</strong> " . $this->calculate_relevance($keywords_found) . "%</li>\n";
                echo "</ul>\n";
                
                // Mostrar extracto relevante
                $relevant_excerpt = $this->extract_relevant_content($content, $keywords_found);
                if ($relevant_excerpt) {
                    echo "<h5>📄 Extracto Relevante:</h5>\n";
                    echo "<div class='content-excerpt'>" . htmlspecialchars(substr($relevant_excerpt, 0, 300)) . "...</div>\n";
                }
                
                echo "</div>\n";
                
                $this->investigation_results['scjn'][] = array(
                    'source' => $description,
                    'url' => $url,
                    'content_length' => $result['length'],
                    'keywords' => $keywords_found,
                    'relevance' => $this->calculate_relevance($keywords_found),
                    'timestamp' => $result['timestamp']
                );
                
            } catch (Exception $e) {
                echo "<div class='investigation-result error'>\n";
                echo "<h4>❌ Error en Investigación</h4>\n";
                echo "<p>Fuente: {$description}</p>\n";
                echo "<p>Error: {$e->getMessage()}</p>\n";
                echo "</div>\n";
            }
        }
    }

    /**
     * Caso 2: Búsqueda en DOF
     */
    private function search_dof_publications() {
        echo "<h2>📰 Caso 2: Búsqueda en Diario Oficial</h2>\n";
        echo "<p><strong>Objetivo:</strong> Encontrar publicaciones recientes relacionadas con reformas</p>\n";
        
        try {
            $parameters = array(
                'url' => 'https://www.dof.gob.mx/',
                'extract_type' => 'links'
            );
            
            $result = $this->web_scraper->execute($parameters);
            $links = json_decode($result['content'], true);
            
            // Filtrar enlaces relevantes
            $relevant_links = $this->filter_relevant_links($links, array('reforma', 'constitucional', 'ley', 'decreto'));
            
            echo "<div class='investigation-result success'>\n";
            echo "<h4>✅ Enlaces del DOF Analizados</h4>\n";
            echo "<ul>\n";
            echo "<li><strong>Total enlaces:</strong> " . count($links) . "</li>\n";
            echo "<li><strong>Enlaces relevantes:</strong> " . count($relevant_links) . "</li>\n";
            echo "<li><strong>Tasa de relevancia:</strong> " . round((count($relevant_links) / count($links)) * 100, 1) . "%</li>\n";
            echo "</ul>\n";
            
            echo "<h5>🔗 Enlaces Más Relevantes:</h5>\n";
            echo "<ul>\n";
            foreach (array_slice($relevant_links, 0, 5) as $link) {
                echo "<li><strong>{$link['text']}</strong> → {$link['url']}</li>\n";
            }
            echo "</ul>\n";
            echo "</div>\n";
            
            $this->investigation_results['dof'] = array(
                'total_links' => count($links),
                'relevant_links' => count($relevant_links),
                'top_links' => array_slice($relevant_links, 0, 10),
                'timestamp' => $result['timestamp']
            );
            
        } catch (Exception $e) {
            echo "<div class='investigation-result error'>\n";
            echo "<h4>❌ Error en DOF</h4>\n";
            echo "<p>Error: {$e->getMessage()}</p>\n";
            echo "</div>\n";
        }
    }

    /**
     * Caso 3: Análisis del marco jurídico
     */
    private function analyze_legal_framework() {
        echo "<h2>⚖️ Caso 3: Análisis del Marco Jurídico</h2>\n";
        echo "<p><strong>Objetivo:</strong> Analizar la estructura del orden jurídico nacional</p>\n";
        
        try {
            $parameters = array(
                'url' => 'https://www.ordenjuridico.gob.mx/',
                'extract_type' => 'metadata'
            );
            
            $result = $this->web_scraper->execute($parameters);
            $metadata = json_decode($result['content'], true);
            
            echo "<div class='investigation-result success'>\n";
            echo "<h4>✅ Metadatos del Orden Jurídico</h4>\n";
            
            // Analizar metadatos
            $structure_analysis = $this->analyze_legal_structure($metadata);
            
            echo "<ul>\n";
            echo "<li><strong>Título del sitio:</strong> " . ($metadata['title'] ?? 'No disponible') . "</li>\n";
            echo "<li><strong>Metadatos encontrados:</strong> " . count($metadata['meta'] ?? array()) . "</li>\n";
            echo "<li><strong>Estructura identificada:</strong> {$structure_analysis['type']}</li>\n";
            echo "<li><strong>Áreas jurídicas:</strong> " . implode(', ', $structure_analysis['areas']) . "</li>\n";
            echo "</ul>\n";
            
            echo "<h5>🏷️ Metadatos Clave:</h5>\n";
            echo "<div class='metadata-display'>\n";
            foreach (array_slice($metadata['meta'] ?? array(), 0, 5, true) as $key => $value) {
                echo "<p><strong>{$key}:</strong> " . htmlspecialchars(substr($value, 0, 100)) . "</p>\n";
            }
            echo "</div>\n";
            echo "</div>\n";
            
            $this->investigation_results['orden_juridico'] = array(
                'metadata_count' => count($metadata['meta'] ?? array()),
                'structure' => $structure_analysis,
                'title' => $metadata['title'] ?? '',
                'timestamp' => $result['timestamp']
            );
            
        } catch (Exception $e) {
            echo "<div class='investigation-result error'>\n";
            echo "<h4>❌ Error en Orden Jurídico</h4>\n";
            echo "<p>Error: {$e->getMessage()}</p>\n";
            echo "</div>\n";
        }
    }

    /**
     * Caso 4: Investigación académica
     */
    private function research_academic_sources() {
        echo "<h2>🎓 Caso 4: Investigación Académica</h2>\n";
        echo "<p><strong>Objetivo:</strong> Buscar fuentes académicas en el Instituto de Investigaciones Jurídicas</p>\n";
        
        try {
            $parameters = array(
                'url' => 'https://www.juridicas.unam.mx/',
                'extract_type' => 'text',
                'max_length' => 2500
            );
            
            $result = $this->web_scraper->execute($parameters);
            
            // Análisis académico
            $academic_analysis = $this->analyze_academic_content($result['content']);
            
            echo "<div class='investigation-result success'>\n";
            echo "<h4>✅ Fuentes Académicas Analizadas</h4>\n";
            echo "<ul>\n";
            echo "<li><strong>Contenido extraído:</strong> {$result['length']} caracteres</li>\n";
            echo "<li><strong>Términos académicos:</strong> " . implode(', ', $academic_analysis['academic_terms']) . "</li>\n";
            echo "<li><strong>Áreas de investigación:</strong> " . implode(', ', $academic_analysis['research_areas']) . "</li>\n";
            echo "<li><strong>Nivel académico:</strong> {$academic_analysis['academic_level']}</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
            
            $this->investigation_results['academic'] = array(
                'content_length' => $result['length'],
                'analysis' => $academic_analysis,
                'timestamp' => $result['timestamp']
            );
            
        } catch (Exception $e) {
            echo "<div class='investigation-result error'>\n";
            echo "<h4>❌ Error en Fuentes Académicas</h4>\n";
            echo "<p>Error: {$e->getMessage()}</p>\n";
            echo "</div>\n";
        }
    }

    /**
     * Caso 5: Análisis de tribunales
     */
    private function analyze_tribunal_decisions() {
        echo "<h2>⚖️ Caso 5: Análisis de Decisiones Tribunales</h2>\n";
        echo "<p><strong>Objetivo:</strong> Investigar información sobre tribunales federales</p>\n";
        
        try {
            $parameters = array(
                'url' => 'https://www.cjf.gob.mx/',
                'extract_type' => 'text',
                'max_length' => 2000
            );
            
            $result = $this->web_scraper->execute($parameters);
            
            // Análisis de contenido judicial
            $judicial_analysis = $this->analyze_judicial_content($result['content']);
            
            echo "<div class='investigation-result success'>\n";
            echo "<h4>✅ Información Judicial Extraída</h4>\n";
            echo "<ul>\n";
            echo "<li><strong>Contenido:</strong> {$result['length']} caracteres</li>\n";
            echo "<li><strong>Términos judiciales:</strong> " . implode(', ', $judicial_analysis['judicial_terms']) . "</li>\n";
            echo "<li><strong>Tipos de procedimientos:</strong> " . implode(', ', $judicial_analysis['procedures']) . "</li>\n";
            echo "<li><strong>Relevancia judicial:</strong> {$judicial_analysis['relevance']}%</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
            
            $this->investigation_results['judicial'] = array(
                'content_length' => $result['length'],
                'analysis' => $judicial_analysis,
                'timestamp' => $result['timestamp']
            );
            
        } catch (Exception $e) {
            echo "<div class='investigation-result error'>\n";
            echo "<h4>❌ Error en Análisis Judicial</h4>\n";
            echo "<p>Error: {$e->getMessage()}</p>\n";
            echo "</div>\n";
        }
    }

    /**
     * Generar reporte final de investigación
     */
    private function generate_investigation_report() {
        echo "<h2>📊 Reporte Final de Investigación</h2>\n";
        
        $total_sources = count($this->investigation_results);
        $successful_investigations = 0;
        $total_content = 0;
        $all_keywords = array();
        
        foreach ($this->investigation_results as $source => $data) {
            if (!empty($data)) {
                $successful_investigations++;
                
                if (isset($data['content_length'])) {
                    $total_content += $data['content_length'];
                } elseif (is_array($data) && isset($data[0]['content_length'])) {
                    foreach ($data as $item) {
                        $total_content += $item['content_length'] ?? 0;
                        $all_keywords = array_merge($all_keywords, $item['keywords'] ?? array());
                    }
                }
            }
        }
        
        echo "<div class='final-report'>\n";
        echo "<h3>📈 Estadísticas de Investigación</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Fuentes investigadas:</strong> {$total_sources}</li>\n";
        echo "<li><strong>Investigaciones exitosas:</strong> {$successful_investigations}</li>\n";
        echo "<li><strong>Tasa de éxito:</strong> " . round(($successful_investigations / $total_sources) * 100, 1) . "%</li>\n";
        echo "<li><strong>Contenido total extraído:</strong> " . number_format($total_content) . " caracteres</li>\n";
        echo "<li><strong>Palabras clave únicas:</strong> " . count(array_unique($all_keywords)) . "</li>\n";
        echo "</ul>\n";
        
        echo "<h3>🎯 Análisis por Fuente</h3>\n";
        foreach ($this->investigation_results as $source => $data) {
            echo "<h4>📋 {$source}</h4>\n";
            if (!empty($data)) {
                echo "<p>✅ <strong>Exitosa</strong> - Datos extraídos correctamente</p>\n";
                
                if ($source === 'scjn' && is_array($data)) {
                    $avg_relevance = array_sum(array_column($data, 'relevance')) / count($data);
                    echo "<p>Relevancia promedio: " . round($avg_relevance, 1) . "%</p>\n";
                } elseif ($source === 'dof') {
                    echo "<p>Enlaces relevantes: {$data['relevant_links']}/{$data['total_links']}</p>\n";
                }
            } else {
                echo "<p>❌ <strong>Fallida</strong> - No se pudieron extraer datos</p>\n";
            }
        }
        
        echo "<h3>🏆 Conclusiones del Agente</h3>\n";
        
        if ($successful_investigations >= 4) {
            echo "<div class='conclusion success'>\n";
            echo "<h4>✅ INVESTIGACIÓN EXITOSA</h4>\n";
            echo "<p>El agente legal pudo realizar una investigación completa y exhaustiva utilizando el MCP Web Scraper.</p>\n";
            echo "<p><strong>Capacidades demostradas:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Extracción de contenido de múltiples fuentes oficiales</li>\n";
            echo "<li>Análisis de metadatos y estructura de sitios</li>\n";
            echo "<li>Identificación de enlaces y recursos relevantes</li>\n";
            echo "<li>Procesamiento de contenido académico y judicial</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        } elseif ($successful_investigations >= 2) {
            echo "<div class='conclusion warning'>\n";
            echo "<h4>⚠️ INVESTIGACIÓN PARCIAL</h4>\n";
            echo "<p>El agente pudo realizar investigación básica pero con limitaciones en algunas fuentes.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div class='conclusion error'>\n";
            echo "<h4>❌ INVESTIGACIÓN LIMITADA</h4>\n";
            echo "<p>El agente tuvo dificultades significativas para acceder a las fuentes de información.</p>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
    }

    /**
     * Analizar palabras clave legales en el contenido
     */
    private function analyze_legal_keywords($content) {
        $legal_keywords = array(
            'constitucional', 'reforma', 'jurisprudencia', 'amparo', 'tribunal',
            'suprema corte', 'derechos humanos', 'ley', 'código', 'decreto',
            'sentencia', 'resolución', 'procedimiento', 'justicia', 'legal'
        );
        
        $found_keywords = array();
        $content_lower = strtolower($content);
        
        foreach ($legal_keywords as $keyword) {
            if (strpos($content_lower, $keyword) !== false) {
                $found_keywords[] = $keyword;
            }
        }
        
        return $found_keywords;
    }

    /**
     * Calcular relevancia basada en palabras clave
     */
    private function calculate_relevance($keywords) {
        $max_keywords = 15;
        return min(100, (count($keywords) / $max_keywords) * 100);
    }

    /**
     * Extraer contenido relevante
     */
    private function extract_relevant_content($content, $keywords) {
        if (empty($keywords)) return '';
        
        $sentences = explode('.', $content);
        $relevant_sentences = array();
        
        foreach ($sentences as $sentence) {
            foreach ($keywords as $keyword) {
                if (stripos($sentence, $keyword) !== false) {
                    $relevant_sentences[] = trim($sentence);
                    break;
                }
            }
        }
        
        return implode('. ', array_slice($relevant_sentences, 0, 3));
    }

    /**
     * Filtrar enlaces relevantes
     */
    private function filter_relevant_links($links, $keywords) {
        $relevant = array();
        
        foreach ($links as $link) {
            $text_lower = strtolower($link['text']);
            foreach ($keywords as $keyword) {
                if (strpos($text_lower, $keyword) !== false) {
                    $relevant[] = $link;
                    break;
                }
            }
        }
        
        return $relevant;
    }

    /**
     * Analizar estructura legal
     */
    private function analyze_legal_structure($metadata) {
        $legal_areas = array('civil', 'penal', 'laboral', 'fiscal', 'administrativo', 'constitucional');
        $found_areas = array();
        
        $content = json_encode($metadata);
        $content_lower = strtolower($content);
        
        foreach ($legal_areas as $area) {
            if (strpos($content_lower, $area) !== false) {
                $found_areas[] = $area;
            }
        }
        
        return array(
            'type' => 'Portal Jurídico Oficial',
            'areas' => $found_areas
        );
    }

    /**
     * Analizar contenido académico
     */
    private function analyze_academic_content($content) {
        $academic_terms = array('investigación', 'estudio', 'análisis', 'doctrina', 'teoría');
        $research_areas = array('derecho constitucional', 'derecho civil', 'derecho penal');
        
        $found_terms = array();
        $found_areas = array();
        $content_lower = strtolower($content);
        
        foreach ($academic_terms as $term) {
            if (strpos($content_lower, $term) !== false) {
                $found_terms[] = $term;
            }
        }
        
        foreach ($research_areas as $area) {
            if (strpos($content_lower, $area) !== false) {
                $found_areas[] = $area;
            }
        }
        
        return array(
            'academic_terms' => $found_terms,
            'research_areas' => $found_areas,
            'academic_level' => count($found_terms) > 3 ? 'Alto' : 'Medio'
        );
    }

    /**
     * Analizar contenido judicial
     */
    private function analyze_judicial_content($content) {
        $judicial_terms = array('tribunal', 'sentencia', 'resolución', 'procedimiento', 'juicio');
        $procedures = array('amparo', 'apelación', 'casación', 'ejecutivo', 'ordinario');
        
        $found_terms = array();
        $found_procedures = array();
        $content_lower = strtolower($content);
        
        foreach ($judicial_terms as $term) {
            if (strpos($content_lower, $term) !== false) {
                $found_terms[] = $term;
            }
        }
        
        foreach ($procedures as $procedure) {
            if (strpos($content_lower, $procedure) !== false) {
                $found_procedures[] = $procedure;
            }
        }
        
        return array(
            'judicial_terms' => $found_terms,
            'procedures' => $found_procedures,
            'relevance' => min(100, (count($found_terms) + count($found_procedures)) * 10)
        );
    }
}

// CSS para mejorar la presentación
echo "<style>
.investigation-result { margin: 15px 0; padding: 15px; border-radius: 5px; }
.investigation-result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.investigation-result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.content-excerpt { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; margin: 10px 0; }
.metadata-display { background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px; }
.final-report { background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0; }
.conclusion { padding: 15px; border-radius: 5px; margin: 15px 0; }
.conclusion.success { background: #d1ecf1; border: 2px solid #bee5eb; color: #0c5460; }
.conclusion.warning { background: #fff3cd; border: 2px solid #ffeaa7; color: #856404; }
.conclusion.error { background: #f8d7da; border: 2px solid #f5c6cb; color: #721c24; }
</style>";

// Ejecutar simulación si se accede directamente
if (basename($_SERVER['PHP_SELF']) === 'test-legal-agent-simulation.php') {
    $simulator = new LexAI_Legal_Agent_Simulator();
    $simulator->simulate_legal_investigation();
}
?>
