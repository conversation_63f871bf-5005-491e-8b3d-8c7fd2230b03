<?php
/**
 * Test de Compatibilidad entre Agentes y MCP
 * 
 * Verifica que las herramientas MCP se expongan correctamente
 * a los agentes y que el mapeo funcione adecuadamente
 *
 * @package LexAI
 * @since 2.1.0
 */

// Simular entorno WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/');
}

// Load required files
require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-agent-factory.php';
require_once LEXAI_PLUGIN_DIR . 'includes/class-lexai-tool-executor.php';
require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/class-lexai-mcp-manager-native.php';

/**
 * Test de Compatibilidad Agentes-MCP
 */
class LexAI_Agent_MCP_Compatibility_Test {

    private $agent_factory;
    private $tool_executor;
    private $mcp_manager;
    private $test_results = array();

    public function __construct() {
        try {
            $this->agent_factory = new LexAI_Agent_Factory();
            $this->tool_executor = new LexAI_Tool_Executor();
            $this->mcp_manager = new LexAI_MCP_Manager_Native();
            echo "<p>✅ <strong>Componentes inicializados correctamente</strong></p>\n";
        } catch (Exception $e) {
            echo "<p>❌ <strong>Error al inicializar:</strong> " . $e->getMessage() . "</p>\n";
            return;
        }
    }

    /**
     * Ejecutar todos los tests de compatibilidad
     */
    public function run_all_tests() {
        echo "<h1>🔗 Test de Compatibilidad Agentes-MCP</h1>\n";
        echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
        
        if (!$this->agent_factory || !$this->tool_executor || !$this->mcp_manager) {
            echo "<p>❌ No se pueden ejecutar tests sin componentes inicializados</p>\n";
            return;
        }
        
        // Test 1: Verificar herramientas MCP disponibles
        $this->test_mcp_tools_available();
        
        // Test 2: Verificar herramientas del Agent Factory
        $this->test_agent_factory_tools();
        
        // Test 3: Verificar mapeo de herramientas
        $this->test_tool_mapping();
        
        // Test 4: Test de ejecución de herramientas
        $this->test_tool_execution();
        
        // Test 5: Test de schemas de herramientas
        $this->test_tool_schemas();
        
        // Test 6: Test de compatibilidad específica Web Scraper
        $this->test_web_scraper_compatibility();
        
        // Mostrar resumen
        $this->show_test_summary();
    }

    /**
     * Test 1: Verificar herramientas MCP disponibles
     */
    private function test_mcp_tools_available() {
        echo "<h2>🔧 Test 1: Herramientas MCP Disponibles</h2>\n";
        
        try {
            $mcp_tools = $this->mcp_manager->get_available_tools();
            
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ Herramientas MCP Encontradas</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>Total herramientas MCP:</strong> " . count($mcp_tools) . "</li>\n";
            echo "</ul>\n";
            
            echo "<h4>🛠️ Herramientas MCP Registradas:</h4>\n";
            echo "<ul>\n";
            foreach ($mcp_tools as $tool) {
                echo "<li><strong>{$tool['name']}</strong> - {$tool['description']}</li>\n";
                echo "<ul>\n";
                echo "<li>Categoría: {$tool['category']}</li>\n";
                echo "<li>Implementación: {$tool['implementation']}</li>\n";
                echo "</ul>\n";
            }
            echo "</ul>\n";
            echo "</div>\n";
            
            $this->test_results['mcp_tools_available'] = 'PASS';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error al obtener herramientas MCP</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['mcp_tools_available'] = 'FAIL';
        }
    }

    /**
     * Test 2: Verificar herramientas del Agent Factory
     */
    private function test_agent_factory_tools() {
        echo "<h2>🤖 Test 2: Herramientas del Agent Factory</h2>\n";
        
        try {
            $available_tools = LexAI_Agent_Factory::AVAILABLE_TOOLS;
            
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ Herramientas del Agent Factory</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>Total herramientas:</strong> " . count($available_tools) . "</li>\n";
            echo "</ul>\n";
            
            echo "<h4>🎯 Herramientas Definidas:</h4>\n";
            echo "<ul>\n";
            foreach ($available_tools as $tool_name => $description) {
                echo "<li><strong>{$tool_name}</strong> - {$description}</li>\n";
            }
            echo "</ul>\n";
            
            // Identificar herramientas que usan MCP
            $mcp_tools = array();
            foreach ($available_tools as $tool_name => $description) {
                if (strpos($description, 'MCP') !== false) {
                    $mcp_tools[] = $tool_name;
                }
            }
            
            echo "<h4>🔗 Herramientas que usan MCP:</h4>\n";
            echo "<ul>\n";
            foreach ($mcp_tools as $tool_name) {
                echo "<li><strong>{$tool_name}</strong> - {$available_tools[$tool_name]}</li>\n";
            }
            echo "</ul>\n";
            
            echo "</div>\n";
            
            $this->test_results['agent_factory_tools'] = 'PASS';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error al obtener herramientas del Agent Factory</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['agent_factory_tools'] = 'FAIL';
        }
    }

    /**
     * Test 3: Verificar mapeo de herramientas
     */
    private function test_tool_mapping() {
        echo "<h2>🗺️ Test 3: Mapeo de Herramientas</h2>\n";
        
        try {
            $available_tools = LexAI_Agent_Factory::AVAILABLE_TOOLS;
            $mcp_tools = $this->mcp_manager->get_available_tools();
            
            // Crear mapeo de herramientas MCP por nombre
            $mcp_by_name = array();
            foreach ($mcp_tools as $tool) {
                $mcp_by_name[$tool['name']] = $tool;
            }
            
            echo "<div class='test-result'>\n";
            echo "<h3>🔍 Análisis de Mapeo</h3>\n";
            
            $mapping_issues = array();
            
            foreach ($available_tools as $agent_tool => $description) {
                if (strpos($description, 'MCP') !== false) {
                    echo "<h4>📋 {$agent_tool}</h4>\n";
                    echo "<p><em>{$description}</em></p>\n";
                    
                    // Verificar si existe mapeo directo
                    if (isset($mcp_by_name[$agent_tool])) {
                        echo "<p>✅ <strong>Mapeo directo encontrado:</strong> {$agent_tool}</p>\n";
                    } else {
                        // Buscar mapeos posibles
                        $possible_mappings = array();
                        foreach ($mcp_by_name as $mcp_name => $mcp_tool) {
                            if (strpos($mcp_name, 'web') !== false && strpos($agent_tool, 'legal') !== false) {
                                $possible_mappings[] = $mcp_name;
                            }
                            if (strpos($mcp_name, 'pinecone') !== false && strpos($agent_tool, 'search') !== false) {
                                $possible_mappings[] = $mcp_name;
                            }
                        }
                        
                        if (!empty($possible_mappings)) {
                            echo "<p>⚠️ <strong>Mapeo indirecto posible:</strong> " . implode(', ', $possible_mappings) . "</p>\n";
                        } else {
                            echo "<p>❌ <strong>Sin mapeo encontrado</strong></p>\n";
                            $mapping_issues[] = $agent_tool;
                        }
                    }
                }
            }
            
            if (empty($mapping_issues)) {
                echo "<div class='mapping-summary success'>\n";
                echo "<h4>✅ Mapeo Completo</h4>\n";
                echo "<p>Todas las herramientas MCP tienen mapeo adecuado.</p>\n";
                echo "</div>\n";
                $this->test_results['tool_mapping'] = 'PASS';
            } else {
                echo "<div class='mapping-summary warning'>\n";
                echo "<h4>⚠️ Problemas de Mapeo Detectados</h4>\n";
                echo "<p>Herramientas sin mapeo: " . implode(', ', $mapping_issues) . "</p>\n";
                echo "</div>\n";
                $this->test_results['tool_mapping'] = 'PARTIAL';
            }
            
            echo "</div>\n";
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en análisis de mapeo</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['tool_mapping'] = 'FAIL';
        }
    }

    /**
     * Test 4: Test de ejecución de herramientas
     */
    private function test_tool_execution() {
        echo "<h2>⚡ Test 4: Ejecución de Herramientas</h2>\n";
        
        $test_tools = array(
            'legal_research' => array(
                'topic' => 'reforma judicial',
                'legal_sites_only' => true,
                'limit' => 3
            ),
            'legal_news' => array(
                'topic' => 'nuevas leyes',
                'date_range' => '30d',
                'limit' => 3
            )
        );
        
        foreach ($test_tools as $tool_name => $test_params) {
            echo "<h3>🔧 Probando: {$tool_name}</h3>\n";
            
            try {
                $result = $this->tool_executor->execute_tool($tool_name, $test_params, 1);
                
                if ($result['success']) {
                    echo "<div class='test-result success'>\n";
                    echo "<p>✅ <strong>Ejecución exitosa</strong></p>\n";
                    echo "<ul>\n";
                    echo "<li>Tipo de ejecución: {$result['execution_type']}</li>\n";
                    echo "<li>Timestamp: " . date('Y-m-d H:i:s', $result['timestamp']) . "</li>\n";
                    echo "</ul>\n";
                    echo "</div>\n";
                } else {
                    echo "<div class='test-result error'>\n";
                    echo "<p>❌ <strong>Ejecución fallida</strong></p>\n";
                    echo "<p>Error: {$result['error']}</p>\n";
                    echo "</div>\n";
                }
                
            } catch (Exception $e) {
                echo "<div class='test-result error'>\n";
                echo "<p>❌ <strong>Excepción durante ejecución</strong></p>\n";
                echo "<p>Error: " . $e->getMessage() . "</p>\n";
                echo "</div>\n";
            }
        }
        
        $this->test_results['tool_execution'] = 'PASS';
    }

    /**
     * Test 5: Test de schemas de herramientas
     */
    private function test_tool_schemas() {
        echo "<h2>📋 Test 5: Schemas de Herramientas</h2>\n";
        
        $mcp_tools = array('legal_research', 'legal_news');
        
        foreach ($mcp_tools as $tool_name) {
            echo "<h3>📄 Schema: {$tool_name}</h3>\n";
            
            try {
                $schema = $this->agent_factory->get_tool_schema($tool_name);
                
                if ($schema) {
                    echo "<div class='test-result success'>\n";
                    echo "<p>✅ <strong>Schema encontrado</strong></p>\n";
                    echo "<ul>\n";
                    echo "<li>Nombre: {$schema['name']}</li>\n";
                    echo "<li>Descripción: " . substr($schema['description'], 0, 100) . "...</li>\n";
                    echo "<li>Parámetros: " . count($schema['parameters']['properties']) . " propiedades</li>\n";
                    echo "</ul>\n";
                    echo "</div>\n";
                } else {
                    echo "<div class='test-result error'>\n";
                    echo "<p>❌ <strong>Schema no encontrado</strong></p>\n";
                    echo "</div>\n";
                }
                
            } catch (Exception $e) {
                echo "<div class='test-result error'>\n";
                echo "<p>❌ <strong>Error al obtener schema</strong></p>\n";
                echo "<p>Error: " . $e->getMessage() . "</p>\n";
                echo "</div>\n";
            }
        }
        
        $this->test_results['tool_schemas'] = 'PASS';
    }

    /**
     * Test 6: Test específico Web Scraper
     */
    private function test_web_scraper_compatibility() {
        echo "<h2>🌐 Test 6: Compatibilidad Web Scraper</h2>\n";
        
        try {
            // Verificar que el Web Scraper MCP esté disponible
            $mcp_tools = $this->mcp_manager->get_available_tools();
            $web_scraper_found = false;
            
            foreach ($mcp_tools as $tool) {
                if (strpos($tool['name'], 'web_scraper') !== false) {
                    $web_scraper_found = true;
                    echo "<div class='test-result success'>\n";
                    echo "<h3>✅ Web Scraper MCP Encontrado</h3>\n";
                    echo "<ul>\n";
                    echo "<li><strong>Nombre:</strong> {$tool['name']}</li>\n";
                    echo "<li><strong>Descripción:</strong> {$tool['description']}</li>\n";
                    echo "<li><strong>Categoría:</strong> {$tool['category']}</li>\n";
                    echo "</ul>\n";
                    echo "</div>\n";
                    break;
                }
            }
            
            if (!$web_scraper_found) {
                echo "<div class='test-result error'>\n";
                echo "<h3>❌ Web Scraper MCP No Encontrado</h3>\n";
                echo "<p>El Web Scraper MCP no está registrado correctamente.</p>\n";
                echo "</div>\n";
            }
            
            $this->test_results['web_scraper_compatibility'] = $web_scraper_found ? 'PASS' : 'FAIL';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en test Web Scraper</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['web_scraper_compatibility'] = 'FAIL';
        }
    }

    /**
     * Mostrar resumen de tests
     */
    private function show_test_summary() {
        echo "<h2>📊 Resumen de Compatibilidad</h2>\n";
        
        $total_tests = count($this->test_results);
        $passed_tests = count(array_filter($this->test_results, function($result) {
            return $result === 'PASS';
        }));
        $partial_tests = count(array_filter($this->test_results, function($result) {
            return $result === 'PARTIAL';
        }));
        
        echo "<div class='summary-box'>\n";
        echo "<h3>🎯 Resultados Finales</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Tests ejecutados:</strong> {$total_tests}</li>\n";
        echo "<li><strong>Tests exitosos:</strong> {$passed_tests}</li>\n";
        echo "<li><strong>Tests parciales:</strong> {$partial_tests}</li>\n";
        echo "<li><strong>Tests fallidos:</strong> " . ($total_tests - $passed_tests - $partial_tests) . "</li>\n";
        echo "<li><strong>Tasa de éxito:</strong> " . round(($passed_tests / $total_tests) * 100, 1) . "%</li>\n";
        echo "</ul>\n";
        
        echo "<h4>📋 Detalle por Test:</h4>\n";
        echo "<ul>\n";
        foreach ($this->test_results as $test_name => $result) {
            $icon = $result === 'PASS' ? '✅' : ($result === 'PARTIAL' ? '⚠️' : '❌');
            echo "<li>{$icon} {$test_name}: {$result}</li>\n";
        }
        echo "</ul>\n";
        
        // Recomendaciones
        echo "<h4>💡 Recomendaciones:</h4>\n";
        echo "<ul>\n";
        if (isset($this->test_results['tool_mapping']) && $this->test_results['tool_mapping'] !== 'PASS') {
            echo "<li>🔧 Corregir mapeo de herramientas en Tool Executor</li>\n";
        }
        if (isset($this->test_results['web_scraper_compatibility']) && $this->test_results['web_scraper_compatibility'] !== 'PASS') {
            echo "<li>🌐 Verificar registro del Web Scraper MCP</li>\n";
        }
        echo "<li>📝 Actualizar documentación de herramientas</li>\n";
        echo "<li>🧪 Implementar tests automáticos de compatibilidad</li>\n";
        echo "</ul>\n";
        
        echo "</div>\n";
    }
}

// CSS para mejorar la presentación
echo "<style>
.test-result { margin: 10px 0; padding: 15px; border-radius: 5px; }
.test-result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.test-result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.test-result.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.mapping-summary { margin: 15px 0; padding: 15px; border-radius: 5px; }
.mapping-summary.success { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
.mapping-summary.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
.summary-box { background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0; }
</style>";

// Ejecutar tests
$test = new LexAI_Agent_MCP_Compatibility_Test();
$test->run_all_tests();
?>
