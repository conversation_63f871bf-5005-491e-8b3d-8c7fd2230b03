<?php
/**
 * <PERSON>ript para ejecutar tests del MCP Web Scraper
 * Simula el entorno de WordPress para poder ejecutar los tests
 */

// Simular constantes de WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/var/www/html/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/var/www/html/wp-content/plugins/lexai-orch-wp/');
}

// Simular funciones básicas de WordPress que podrían ser necesarias
if (!function_exists('wp_remote_post')) {
    function wp_remote_post($url, $args = array()) {
        $ch = curl_init();
        
        $headers = array();
        if (isset($args['headers'])) {
            foreach ($args['headers'] as $key => $value) {
                $headers[] = "$key: $value";
            }
        }
        
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $args['body'] ?? '',
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $args['timeout'] ?? 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; LexAI-Bot/1.0)'
        ));
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($response === false) {
            return new WP_Error('http_request_failed', $error);
        }
        
        return array(
            'response' => array('code' => $http_code),
            'body' => $response
        );
    }
}

if (!function_exists('wp_remote_get')) {
    function wp_remote_get($url, $args = array()) {
        $ch = curl_init();
        
        $headers = array();
        if (isset($args['headers'])) {
            foreach ($args['headers'] as $key => $value) {
                $headers[] = "$key: $value";
            }
        }
        
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $args['timeout'] ?? 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; LexAI-Bot/1.0)'
        ));
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($response === false) {
            return new WP_Error('http_request_failed', $error);
        }
        
        return array(
            'response' => array('code' => $http_code),
            'body' => $response
        );
    }
}

if (!function_exists('wp_remote_retrieve_response_code')) {
    function wp_remote_retrieve_response_code($response) {
        if (is_wp_error($response)) {
            return 0;
        }
        return $response['response']['code'] ?? 0;
    }
}

if (!function_exists('wp_remote_retrieve_body')) {
    function wp_remote_retrieve_body($response) {
        if (is_wp_error($response)) {
            return '';
        }
        return $response['body'] ?? '';
    }
}

if (!function_exists('is_wp_error')) {
    function is_wp_error($thing) {
        return $thing instanceof WP_Error;
    }
}

// Clase WP_Error simulada
if (!class_exists('WP_Error')) {
    class WP_Error {
        private $errors = array();
        private $error_data = array();
        
        public function __construct($code = '', $message = '', $data = '') {
            if (!empty($code)) {
                $this->errors[$code][] = $message;
                if (!empty($data)) {
                    $this->error_data[$code] = $data;
                }
            }
        }
        
        public function get_error_message($code = '') {
            if (empty($code)) {
                $code = $this->get_error_code();
            }
            if (isset($this->errors[$code])) {
                return $this->errors[$code][0];
            }
            return '';
        }
        
        public function get_error_code() {
            $codes = array_keys($this->errors);
            return $codes[0] ?? '';
        }
    }
}

echo "<h1>🧪 Ejecutando Tests del MCP Web Scraper</h1>\n";
echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>\n";

// Test 1: Test básico de funcionalidades
echo "<h2>📋 Test 1: Funcionalidades Básicas</h2>\n";
try {
    include LEXAI_PLUGIN_DIR . 'tests/test-web-scraper-mcp.php';
    echo "<p>✅ Test de funcionalidades ejecutado</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Error en test de funcionalidades: " . $e->getMessage() . "</p>\n";
}

echo "<hr>\n";

// Test 2: Simulación de agente legal
echo "<h2>🤖 Test 2: Simulación de Agente Legal</h2>\n";
try {
    include LEXAI_PLUGIN_DIR . 'tests/test-legal-agent-simulation.php';
    echo "<p>✅ Simulación de agente ejecutada</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Error en simulación de agente: " . $e->getMessage() . "</p>\n";
}

echo "<hr>\n";

// Test 3: Análisis de capacidades
echo "<h2>🔍 Test 3: Análisis de Capacidades</h2>\n";
try {
    include LEXAI_PLUGIN_DIR . 'tests/web-scraper-capabilities-analysis.php';
    echo "<p>✅ Análisis de capacidades ejecutado</p>\n";
} catch (Exception $e) {
    echo "<p>❌ Error en análisis de capacidades: " . $e->getMessage() . "</p>\n";
}

echo "<h2>🎉 Tests Completados</h2>\n";
echo "<p>Todos los tests han sido ejecutados. Revisa los resultados arriba.</p>\n";
?>
