<?php
/**
 * Test del MCP Web Scraper mejorado con PHPScraper
 * 
 * Test completo del nuevo MCP Web Scraper que usa PHPScraper
 * para extracción avanzada de contenido web
 *
 * @package LexAI
 * @since 2.1.0
 */

// Simular entorno WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/');
}

// Load required files
require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/interfaces/interface-mcp-tool.php';
require_once LEXAI_PLUGIN_DIR . 'includes/mcp-native/tools/class-lexai-web-scraper-tool-native.php';

/**
 * Test del MCP Web Scraper con PHPScraper
 */
class LexAI_PHPScraper_MCP_Test {

    private $web_scraper;
    private $test_results = array();

    public function __construct() {
        try {
            $this->web_scraper = new LexAI_Web_Scraper_Tool_Native();
            echo "<p>✅ <strong>MCP Web Scraper inicializado correctamente</strong></p>\n";
        } catch (Exception $e) {
            echo "<p>❌ <strong>Error al inicializar MCP:</strong> " . $e->getMessage() . "</p>\n";
            return;
        }
    }

    /**
     * Ejecutar todos los tests
     */
    public function run_all_tests() {
        echo "<h1>🚀 Test del MCP Web Scraper Mejorado con PHPScraper</h1>\n";
        echo "<p><strong>Fecha:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
        
        if (!$this->web_scraper) {
            echo "<p>❌ No se puede ejecutar tests sin MCP inicializado</p>\n";
            return;
        }
        
        // Test 1: Verificar disponibilidad
        $this->test_availability();
        
        // Test 2: Extracción comprehensiva
        $this->test_comprehensive_extraction();
        
        // Test 3: Extracción de texto avanzada
        $this->test_advanced_text_extraction();
        
        // Test 4: Extracción de enlaces con detalles
        $this->test_detailed_links_extraction();
        
        // Test 5: Extracción de metadatos completos
        $this->test_complete_metadata_extraction();
        
        // Test 6: Análisis de estructura de documento
        $this->test_document_structure_analysis();
        
        // Test 7: Extracción de palabras clave
        $this->test_keyword_extraction();
        
        // Test 8: Análisis legal automático
        $this->test_legal_content_analysis();
        
        // Test 9: Extracción de redes sociales
        $this->test_social_media_extraction();
        
        // Test 10: Seguimiento de enlaces relacionados
        $this->test_related_content_extraction();
        
        // Mostrar resumen
        $this->show_test_summary();
    }

    /**
     * Test 1: Verificar disponibilidad
     */
    private function test_availability() {
        echo "<h2>🔧 Test 1: Verificación de Disponibilidad</h2>\n";
        
        try {
            $available = $this->web_scraper->is_available();
            $metadata = $this->web_scraper->get_metadata();
            
            echo "<div class='test-result success'>\n";
            echo "<h3>✅ Herramienta Disponible</h3>\n";
            echo "<ul>\n";
            echo "<li><strong>Disponible:</strong> " . ($available ? 'Sí' : 'No') . "</li>\n";
            echo "<li><strong>Versión:</strong> {$metadata['version']}</li>\n";
            echo "<li><strong>Tipo:</strong> {$metadata['type']}</li>\n";
            echo "<li><strong>Librería:</strong> {$metadata['library']}</li>\n";
            echo "<li><strong>Capacidades:</strong> " . count($metadata['capabilities']) . "</li>\n";
            echo "</ul>\n";
            
            echo "<h4>🎯 Capacidades Disponibles:</h4>\n";
            echo "<ul>\n";
            foreach ($metadata['capabilities'] as $capability) {
                echo "<li>✅ " . str_replace('_', ' ', ucfirst($capability)) . "</li>\n";
            }
            echo "</ul>\n";
            echo "</div>\n";
            
            $this->test_results['availability'] = 'PASS';
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Verificación</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['availability'] = 'FAIL';
        }
    }

    /**
     * Test 2: Extracción comprehensiva
     */
    private function test_comprehensive_extraction() {
        echo "<h2>🌟 Test 2: Extracción Comprehensiva</h2>\n";
        
        $test_url = 'https://www.scjn.gob.mx/';
        
        try {
            $parameters = array(
                'url' => $test_url,
                'extract_type' => 'comprehensive',
                'max_length' => 20000,
                'include_analysis' => true
            );
            
            $start_time = microtime(true);
            $result = $this->web_scraper->execute($parameters);
            $end_time = microtime(true);
            $execution_time = round(($end_time - $start_time) * 1000, 2);
            
            if ($result['success']) {
                echo "<div class='test-result success'>\n";
                echo "<h3>✅ Extracción Comprehensiva Exitosa</h3>\n";
                echo "<ul>\n";
                echo "<li><strong>URL:</strong> {$result['url']}</li>\n";
                echo "<li><strong>Tiempo de ejecución:</strong> {$execution_time}ms</li>\n";
                echo "<li><strong>Título:</strong> {$result['metadata']['title']}</li>\n";
                echo "<li><strong>Idioma:</strong> {$result['metadata']['language']}</li>\n";
                echo "<li><strong>Longitud total:</strong> {$result['metadata']['content_length']} caracteres</li>\n";
                echo "</ul>\n";
                
                // Mostrar estructura de datos extraídos
                echo "<h4>📊 Estructura de Datos Extraídos:</h4>\n";
                $this->display_data_structure($result['data']);
                
                // Mostrar análisis legal si está disponible
                if (isset($result['data']['legal_analysis'])) {
                    echo "<h4>⚖️ Análisis Legal:</h4>\n";
                    $this->display_legal_analysis($result['data']['legal_analysis']);
                }
                
                echo "</div>\n";
                $this->test_results['comprehensive'] = 'PASS';
            } else {
                throw new Exception($result['error']);
            }
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Extracción Comprehensiva</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['comprehensive'] = 'FAIL';
        }
    }

    /**
     * Test 3: Extracción de texto avanzada
     */
    private function test_advanced_text_extraction() {
        echo "<h2>📄 Test 3: Extracción de Texto Avanzada</h2>\n";
        
        $test_url = 'https://www.dof.gob.mx/';
        
        try {
            $parameters = array(
                'url' => $test_url,
                'extract_type' => 'text',
                'max_length' => 5000
            );
            
            $result = $this->web_scraper->execute($parameters);
            
            if ($result['success']) {
                echo "<div class='test-result success'>\n";
                echo "<h3>✅ Texto Extraído Correctamente</h3>\n";
                echo "<ul>\n";
                echo "<li><strong>Contenido principal:</strong> " . strlen($result['data']['content']) . " caracteres</li>\n";
                echo "<li><strong>Párrafos:</strong> " . count($result['data']['paragraphs']) . "</li>\n";
                echo "<li><strong>Texto limpio:</strong> " . strlen($result['data']['clean_text']) . " caracteres</li>\n";
                echo "</ul>\n";
                
                echo "<h4>📄 Vista Previa del Contenido:</h4>\n";
                echo "<div class='content-preview'>" . htmlspecialchars(substr($result['data']['content'], 0, 300)) . "...</div>\n";
                
                echo "</div>\n";
                $this->test_results['text_extraction'] = 'PASS';
            } else {
                throw new Exception($result['error']);
            }
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Extracción de Texto</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['text_extraction'] = 'FAIL';
        }
    }

    /**
     * Test 4: Extracción de enlaces con detalles
     */
    private function test_detailed_links_extraction() {
        echo "<h2>🔗 Test 4: Extracción de Enlaces Detallada</h2>\n";
        
        $test_url = 'https://www.ordenjuridico.gob.mx/';
        
        try {
            $parameters = array(
                'url' => $test_url,
                'extract_type' => 'links'
            );
            
            $result = $this->web_scraper->execute($parameters);
            
            if ($result['success']) {
                echo "<div class='test-result success'>\n";
                echo "<h3>✅ Enlaces Extraídos con Detalles</h3>\n";
                echo "<ul>\n";
                echo "<li><strong>Enlaces totales:</strong> " . count($result['data']['links']) . "</li>\n";
                echo "<li><strong>Enlaces internos:</strong> " . count($result['data']['internal_links']) . "</li>\n";
                echo "<li><strong>Enlaces externos:</strong> " . count($result['data']['external_links']) . "</li>\n";
                echo "<li><strong>Enlaces con detalles:</strong> " . count($result['data']['links_with_details']) . "</li>\n";
                echo "</ul>\n";
                
                echo "<h4>🔗 Primeros 5 Enlaces con Detalles:</h4>\n";
                echo "<ul>\n";
                foreach (array_slice($result['data']['links_with_details'], 0, 5) as $link) {
                    echo "<li><strong>" . htmlspecialchars($link['text'] ?? 'Sin texto') . "</strong> → " . htmlspecialchars($link['url']) . "</li>\n";
                }
                echo "</ul>\n";
                
                echo "</div>\n";
                $this->test_results['links_extraction'] = 'PASS';
            } else {
                throw new Exception($result['error']);
            }
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Extracción de Enlaces</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['links_extraction'] = 'FAIL';
        }
    }

    /**
     * Test 5: Extracción de metadatos completos
     */
    private function test_complete_metadata_extraction() {
        echo "<h2>🏷️ Test 5: Extracción de Metadatos Completos</h2>\n";
        
        $test_url = 'https://www.juridicas.unam.mx/';
        
        try {
            $parameters = array(
                'url' => $test_url,
                'extract_type' => 'metadata'
            );
            
            $result = $this->web_scraper->execute($parameters);
            
            if ($result['success']) {
                echo "<div class='test-result success'>\n";
                echo "<h3>✅ Metadatos Completos Extraídos</h3>\n";
                
                $metadata = $result['data'];
                
                echo "<h4>📋 Metadatos Básicos:</h4>\n";
                echo "<ul>\n";
                foreach ($metadata['basic'] as $key => $value) {
                    if ($value) {
                        echo "<li><strong>" . ucfirst($key) . ":</strong> " . htmlspecialchars(substr($value, 0, 100)) . "</li>\n";
                    }
                }
                echo "</ul>\n";
                
                echo "<h4>⚙️ Metadatos Técnicos:</h4>\n";
                echo "<ul>\n";
                foreach ($metadata['technical'] as $key => $value) {
                    if ($value) {
                        echo "<li><strong>" . ucfirst($key) . ":</strong> " . htmlspecialchars(substr($value, 0, 100)) . "</li>\n";
                    }
                }
                echo "</ul>\n";
                
                echo "<h4>📱 Redes Sociales:</h4>\n";
                if (!empty($metadata['social']['twitter']['title'])) {
                    echo "<p><strong>Twitter:</strong> " . htmlspecialchars($metadata['social']['twitter']['title']) . "</p>\n";
                }
                if (!empty($metadata['social']['facebook']['title'])) {
                    echo "<p><strong>Facebook:</strong> " . htmlspecialchars($metadata['social']['facebook']['title']) . "</p>\n";
                }
                
                echo "</div>\n";
                $this->test_results['metadata_extraction'] = 'PASS';
            } else {
                throw new Exception($result['error']);
            }
            
        } catch (Exception $e) {
            echo "<div class='test-result error'>\n";
            echo "<h3>❌ Error en Extracción de Metadatos</h3>\n";
            echo "<p>Error: " . $e->getMessage() . "</p>\n";
            echo "</div>\n";
            
            $this->test_results['metadata_extraction'] = 'FAIL';
        }
    }

    /**
     * Mostrar estructura de datos extraídos
     */
    private function display_data_structure($data) {
        echo "<ul>\n";
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                echo "<li><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> " . count($value) . " elementos</li>\n";
            } else {
                echo "<li><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> " . (is_string($value) ? strlen($value) . " caracteres" : "Valor") . "</li>\n";
            }
        }
        echo "</ul>\n";
    }

    /**
     * Mostrar análisis legal
     */
    private function display_legal_analysis($analysis) {
        echo "<ul>\n";
        if (isset($analysis['legal_categories'])) {
            $categories = array_filter($analysis['legal_categories']);
            echo "<li><strong>Categorías legales encontradas:</strong> " . count($categories) . "</li>\n";
            foreach ($categories as $category => $terms) {
                echo "<li><strong>" . ucfirst($category) . ":</strong> " . implode(', ', $terms) . "</li>\n";
            }
        }
        if (isset($analysis['legal_relevance'])) {
            echo "<li><strong>Relevancia legal:</strong> {$analysis['legal_relevance']}%</li>\n";
        }
        if (isset($analysis['document_type'])) {
            echo "<li><strong>Tipo de documento:</strong> {$analysis['document_type']}</li>\n";
        }
        if (isset($analysis['jurisdiction'])) {
            echo "<li><strong>Jurisdicción:</strong> {$analysis['jurisdiction']}</li>\n";
        }
        echo "</ul>\n";
    }

    /**
     * Mostrar resumen de tests
     */
    private function show_test_summary() {
        echo "<h2>📊 Resumen de Tests PHPScraper</h2>\n";
        
        $total_tests = count($this->test_results);
        $passed_tests = count(array_filter($this->test_results, function($result) {
            return $result === 'PASS';
        }));
        
        echo "<div class='summary-box'>\n";
        echo "<h3>🎯 Resultados Finales</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Tests ejecutados:</strong> {$total_tests}</li>\n";
        echo "<li><strong>Tests exitosos:</strong> {$passed_tests}</li>\n";
        echo "<li><strong>Tests fallidos:</strong> " . ($total_tests - $passed_tests) . "</li>\n";
        echo "<li><strong>Tasa de éxito:</strong> " . round(($passed_tests / $total_tests) * 100, 1) . "%</li>\n";
        echo "</ul>\n";
        
        echo "<h4>📋 Detalle por Test:</h4>\n";
        echo "<ul>\n";
        foreach ($this->test_results as $test_name => $result) {
            $icon = $result === 'PASS' ? '✅' : '❌';
            echo "<li>{$icon} {$test_name}: {$result}</li>\n";
        }
        echo "</ul>\n";
        
        // Evaluación general
        if ($passed_tests >= ($total_tests * 0.8)) {
            echo "<div class='final-verdict success'>\n";
            echo "<h3>🎉 VEREDICTO: MCP PHPSCRAPER EXCELENTE</h3>\n";
            echo "<p>El MCP Web Scraper mejorado con PHPScraper está funcionando perfectamente y listo para producción.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div class='final-verdict warning'>\n";
            echo "<h3>⚠️ VEREDICTO: MCP PHPSCRAPER REQUIERE AJUSTES</h3>\n";
            echo "<p>El MCP Web Scraper necesita algunos ajustes antes de estar listo para producción.</p>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
    }
}

// CSS para mejorar la presentación
echo "<style>
.test-result { margin: 10px 0; padding: 15px; border-radius: 5px; }
.test-result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.test-result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.content-preview { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; max-height: 150px; overflow-y: auto; }
.summary-box { background: #e9ecef; padding: 20px; border-radius: 8px; margin: 20px 0; }
.final-verdict { padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
.final-verdict.success { background: #d1ecf1; border: 2px solid #bee5eb; color: #0c5460; }
.final-verdict.warning { background: #fff3cd; border: 2px solid #ffeaa7; color: #856404; }
</style>";

// Ejecutar tests
$test = new LexAI_PHPScraper_MCP_Test();
$test->run_all_tests();
?>
