<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Cambios CSS - LexAI</title>
    <link rel="stylesheet" href="../assets/css/lexai-fullpage-chat.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison > div {
            flex: 1;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .before {
            background: #ffe6e6;
        }
        .after {
            background: #e6ffe6;
        }
    </style>
</head>
<body class="lexai-fullpage-chat">
    <button class="theme-toggle" onclick="toggleTheme()">
        🌓 Cambiar Tema
    </button>

    <div class="test-container">
        <h1>🎨 Test de Cambios CSS Aplicados</h1>
        <p><strong>Fecha:</strong> <span id="current-date"></span></p>
        <p><strong>Tema actual:</strong> <span id="current-theme">Light</span></p>

        <!-- Test 1: Conversation Items -->
        <div class="test-section">
            <h2>📋 Test 1: Conversation Items</h2>
            <div class="comparison">
                <div class="before">
                    <h3>❌ Antes</h3>
                    <p>Background: var(--lexai-bg-card)</p>
                    <p>Color: var(--lexai-text-primary)</p>
                    <p>Border: var(--lexai-border)</p>
                </div>
                <div class="after">
                    <h3>✅ Después</h3>
                    <p><strong>Light:</strong> rgb(145 153 163) + #ffffff</p>
                    <p><strong>Dark:</strong> rgb(31 41 55 / 95%) + #ffffff</p>
                </div>
            </div>
            
            <div class="lexai-conversations-list">
                <div class="lexai-conversation-item">
                    <i class="fas fa-comment"></i>
                    <span>Conversación de prueba 1</span>
                </div>
                <div class="lexai-conversation-item active">
                    <i class="fas fa-comment"></i>
                    <span>Conversación activa</span>
                </div>
                <div class="lexai-conversation-item">
                    <i class="fas fa-comment"></i>
                    <span>Conversación de prueba 3</span>
                </div>
            </div>
        </div>

        <!-- Test 2: Search Box -->
        <div class="test-section">
            <h2>🔍 Test 2: Search Box</h2>
            <div class="comparison">
                <div class="before">
                    <h3>❌ Antes</h3>
                    <p>Background: var(--lexai-bg-card)</p>
                    <p>Color: var(--lexai-text-primary)</p>
                    <p>Placeholder: default</p>
                </div>
                <div class="after">
                    <h3>✅ Después</h3>
                    <p><strong>Light:</strong> rgb(98 102 107 / 27%) + #ffffff</p>
                    <p><strong>Dark:</strong> rgb(31 41 55 / 80%) + #ffffff</p>
                    <p><strong>Placeholder:</strong> #ffffff con opacity 0.7</p>
                </div>
            </div>
            
            <div class="lexai-search-section">
                <div class="lexai-search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="Buscar conversaciones...">
                </div>
            </div>
        </div>

        <!-- Test 3: Buttons -->
        <div class="test-section">
            <h2>🔘 Test 3: Buttons</h2>
            <div class="comparison">
                <div class="before">
                    <h3>❌ Antes</h3>
                    <p>Background: var(--lexai-bg-card)</p>
                    <p>Color: var(--lexai-text-primary)</p>
                    <p>Border: var(--lexai-border)</p>
                </div>
                <div class="after">
                    <h3>✅ Después</h3>
                    <p><strong>Light:</strong> rgb(145 153 163) + #ffffff</p>
                    <p><strong>Dark:</strong> rgb(31 41 55 / 95%) + #ffffff</p>
                </div>
            </div>
            
            <div style="display: flex; gap: 15px; margin: 20px 0;">
                <button class="lexai-btn lexai-btn-primary">
                    <i class="fas fa-plus"></i>
                    Botón Primario
                </button>
                <button class="lexai-btn lexai-btn-secondary">
                    <i class="fas fa-cog"></i>
                    Botón Secundario
                </button>
            </div>
        </div>

        <!-- Test 4: Theme Variables -->
        <div class="test-section">
            <h2>🎨 Test 4: Variables de Tema</h2>
            <div class="comparison">
                <div class="before">
                    <h3>❌ Antes</h3>
                    <p>Solo variables genéricas</p>
                    <p>Sin diferenciación por tema</p>
                </div>
                <div class="after">
                    <h3>✅ Después</h3>
                    <p>Variables específicas por tema</p>
                    <p>Fallbacks para compatibilidad</p>
                    <p>Overrides con !important</p>
                </div>
            </div>
            
            <div id="theme-variables">
                <h4>Variables CSS Aplicadas:</h4>
                <ul id="variables-list">
                    <!-- Se llenarán con JavaScript -->
                </ul>
            </div>
        </div>

        <!-- Test 5: Responsive -->
        <div class="test-section">
            <h2>📱 Test 5: Responsive Design</h2>
            <p>Los cambios mantienen la responsividad en todos los dispositivos.</p>
            <p><strong>Breakpoints:</strong> 768px, 480px</p>
            <p><strong>Comportamiento:</strong> Los estilos se adaptan automáticamente</p>
        </div>

        <!-- Resumen -->
        <div class="test-section" style="background: #f0f8ff; border-color: #007cba;">
            <h2>🎯 Resumen de Cambios Aplicados</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h3>✅ Cambios Exitosos:</h3>
                    <ul>
                        <li>Conversation items: Fondo gris + texto blanco</li>
                        <li>Search box: Fondo gris + texto blanco + placeholder blanco</li>
                        <li>Botones secundarios: Fondo gris + texto blanco</li>
                        <li>Variables específicas por tema</li>
                        <li>Overrides con !important para garantizar aplicación</li>
                        <li>Compatibilidad con ambos temas (light/dark)</li>
                    </ul>
                </div>
                <div>
                    <h3>🔧 Implementación Técnica:</h3>
                    <ul>
                        <li>Variables CSS personalizadas</li>
                        <li>Fallbacks para compatibilidad</li>
                        <li>Selectores específicos por tema</li>
                        <li>Transiciones suaves mantenidas</li>
                        <li>Responsive design preservado</li>
                        <li>Accesibilidad mantenida</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Función para cambiar tema
        function toggleTheme() {
            const body = document.body;
            const themeSpan = document.getElementById('current-theme');
            
            if (body.classList.contains('lexai-theme-dark')) {
                body.classList.remove('lexai-theme-dark');
                body.classList.add('lexai-theme-light');
                themeSpan.textContent = 'Light';
            } else {
                body.classList.remove('lexai-theme-light');
                body.classList.add('lexai-theme-dark');
                themeSpan.textContent = 'Dark';
            }
            
            updateVariablesList();
        }

        // Función para mostrar variables CSS aplicadas
        function updateVariablesList() {
            const list = document.getElementById('variables-list');
            const isDark = document.body.classList.contains('lexai-theme-dark');
            
            const variables = isDark ? [
                '--lexai-conversation-bg: rgb(31 41 55 / 95%)',
                '--lexai-search-bg: rgb(31 41 55 / 80%)',
                '--lexai-btn-secondary-bg: rgb(31 41 55 / 95%)',
                '--lexai-conversation-color: #ffffff',
                '--lexai-search-color: #ffffff'
            ] : [
                '--lexai-conversation-bg: rgb(145 153 163)',
                '--lexai-search-bg: rgb(98 102 107 / 27%)',
                '--lexai-btn-secondary-bg: rgb(145 153 163)',
                '--lexai-conversation-color: #ffffff',
                '--lexai-search-color: #ffffff'
            ];
            
            list.innerHTML = variables.map(v => `<li><code>${v}</code></li>`).join('');
        }

        // Inicializar
        document.getElementById('current-date').textContent = new Date().toLocaleString('es-ES');
        updateVariablesList();

        // Aplicar tema light por defecto
        document.body.classList.add('lexai-theme-light');
    </script>
</body>
</html>
