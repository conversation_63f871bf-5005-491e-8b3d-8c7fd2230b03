<?php
/**
 * Test simple de PHPScraper para verificar propiedades disponibles
 */

// Simular entorno WordPress
if (!defined('ABSPATH')) {
    define('ABSPATH', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/');
}

if (!defined('LEXAI_PLUGIN_DIR')) {
    define('LEXAI_PLUGIN_DIR', '/home/<USER>/htdocs/tuasesorlegalvirtual.online/wp-content/plugins/lexai-orch-wp/');
}

// Load PHPScraper
require_once LEXAI_PLUGIN_DIR . 'vendor/autoload.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPScraper\PHPScraper;

echo "<h1>🧪 Test Simple de PHPScraper</h1>\n";

try {
    $scraper = new PHPScraper();
    echo "<p>✅ PHPScraper inicializado correctamente</p>\n";
    
    // Configurar scraper
    $scraper->setConfig([
        'agent' => 'Mozilla/5.0 (compatible; LexAI-Legal-Bot/2.1; +https://tuasesorlegalvirtual.online)',
        'timeout' => 30
    ]);
    
    // Navegar a una página de test
    echo "<h2>📋 Navegando a página de test...</h2>\n";
    $scraper->go('https://www.scjn.gob.mx/');
    echo "<p>✅ Navegación exitosa</p>\n";
    
    // Probar propiedades básicas
    echo "<h2>🔍 Propiedades Básicas Disponibles:</h2>\n";
    echo "<ul>\n";
    
    // Title
    try {
        $title = $scraper->title;
        echo "<li><strong>Title:</strong> " . htmlspecialchars($title ?? 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Title:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Description
    try {
        $description = $scraper->description;
        echo "<li><strong>Description:</strong> " . htmlspecialchars(substr($description ?? 'null', 0, 100)) . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Description:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Keywords
    try {
        $keywords = $scraper->keywords;
        echo "<li><strong>Keywords:</strong> " . (is_array($keywords) ? implode(', ', array_slice($keywords, 0, 5)) : htmlspecialchars($keywords ?? 'null')) . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Keywords:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Paragraphs
    try {
        $paragraphs = $scraper->paragraphs;
        echo "<li><strong>Paragraphs:</strong> " . (is_array($paragraphs) ? count($paragraphs) . " párrafos" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Paragraphs:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Links
    try {
        $links = $scraper->links;
        echo "<li><strong>Links:</strong> " . (is_array($links) ? count($links) . " enlaces" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Links:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Images
    try {
        $images = $scraper->images;
        echo "<li><strong>Images:</strong> " . (is_array($images) ? count($images) . " imágenes" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Images:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Headings
    try {
        $headings = $scraper->headings;
        echo "<li><strong>Headings:</strong> " . (is_array($headings) ? count($headings) . " encabezados" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Headings:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // H1
    try {
        $h1 = $scraper->h1;
        echo "<li><strong>H1:</strong> " . (is_array($h1) ? count($h1) . " elementos" : htmlspecialchars($h1 ?? 'null')) . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>H1:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Lists
    try {
        $lists = $scraper->lists;
        echo "<li><strong>Lists:</strong> " . (is_array($lists) ? count($lists) . " listas" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Lists:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    echo "</ul>\n";
    
    // Probar propiedades avanzadas
    echo "<h2>🚀 Propiedades Avanzadas:</h2>\n";
    echo "<ul>\n";
    
    // Content Keywords
    try {
        $contentKeywords = $scraper->contentKeywords;
        echo "<li><strong>Content Keywords:</strong> " . (is_array($contentKeywords) ? count($contentKeywords) . " palabras clave" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Content Keywords:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Meta Tags
    try {
        $metaTags = $scraper->metaTags;
        echo "<li><strong>Meta Tags:</strong> " . (is_array($metaTags) ? count($metaTags) . " meta tags" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Meta Tags:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Links with Details
    try {
        $linksWithDetails = $scraper->linksWithDetails;
        echo "<li><strong>Links with Details:</strong> " . (is_array($linksWithDetails) ? count($linksWithDetails) . " enlaces detallados" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Links with Details:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // Internal Links
    try {
        $internalLinks = $scraper->internalLinks;
        echo "<li><strong>Internal Links:</strong> " . (is_array($internalLinks) ? count($internalLinks) . " enlaces internos" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>Internal Links:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    // External Links
    try {
        $externalLinks = $scraper->externalLinks;
        echo "<li><strong>External Links:</strong> " . (is_array($externalLinks) ? count($externalLinks) . " enlaces externos" : 'null') . "</li>\n";
    } catch (Exception $e) {
        echo "<li><strong>External Links:</strong> Error - " . $e->getMessage() . "</li>\n";
    }
    
    echo "</ul>\n";
    
    // Mostrar algunos datos de ejemplo
    echo "<h2>📄 Datos de Ejemplo:</h2>\n";
    
    if (isset($paragraphs) && is_array($paragraphs) && count($paragraphs) > 0) {
        echo "<h3>Primer párrafo:</h3>\n";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px;'>";
        echo htmlspecialchars(substr($paragraphs[0], 0, 200)) . "...";
        echo "</div>\n";
    }
    
    if (isset($links) && is_array($links) && count($links) > 0) {
        echo "<h3>Primeros 5 enlaces:</h3>\n";
        echo "<ul>\n";
        for ($i = 0; $i < min(5, count($links)); $i++) {
            echo "<li>" . htmlspecialchars($links[$i]) . "</li>\n";
        }
        echo "</ul>\n";
    }
    
    echo "<h2>🎉 Test Completado Exitosamente</h2>\n";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>❌ Error en Test</h3>\n";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>\n";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>\n";
    echo "</div>\n";
}
?>
