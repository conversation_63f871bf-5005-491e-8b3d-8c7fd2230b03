<?php
/**
 * Login Form Template - Glassmorphism Design
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="lexai-auth-container" data-theme="<?php echo esc_attr($atts['theme']); ?>">
    <!-- Animated Background - EXACT SAME AS CHAT -->
    <div class="lexai-auth-bg">
        <div class="lexai-floating-element lexai-element-1"></div>
        <div class="lexai-floating-element lexai-element-2"></div>
        <div class="lexai-floating-element lexai-element-3"></div>
        <div class="lexai-floating-element lexai-element-4"></div>
        <div class="lexai-floating-element lexai-element-5"></div>
    </div>
    
    <div class="lexai-auth-card">
        <div class="lexai-auth-header">
            <div class="lexai-auth-logo">
                <div class="lexai-auth-icon"><i class="fas fa-balance-scale" aria-label="LexAI Legal Assistant"></i></div>
                <h1>LexAI</h1>
                <p><?php _e('Asistente Legal Inteligente', 'lexai'); ?></p>
            </div>
        </div>
        
        <div class="lexai-auth-body">
            <h2><?php _e('Iniciar Sesión', 'lexai'); ?></h2>
            <p class="lexai-auth-subtitle"><?php _e('Accede a tu asistente legal especializado', 'lexai'); ?></p>
            
            <form id="lexai-login-form" class="lexai-auth-form" method="post">
                <?php wp_nonce_field('lexai_login', 'lexai_login_nonce'); ?>
                
                <div class="lexai-form-group">
                    <label for="lexai-username"><?php _e('Usuario o Email', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-user"></i></span>
                        <input 
                            type="text" 
                            id="lexai-username" 
                            name="username" 
                            class="lexai-input" 
                            placeholder="<?php _e('Ingresa tu usuario o email', 'lexai'); ?>"
                            required
                        >
                    </div>
                </div>
                
                <div class="lexai-form-group">
                    <label for="lexai-password"><?php _e('Contraseña', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-lock"></i></span>
                        <input 
                            type="password" 
                            id="lexai-password" 
                            name="password" 
                            class="lexai-input" 
                            placeholder="<?php _e('Ingresa tu contraseña', 'lexai'); ?>"
                            required
                        >
                        <button type="button" class="lexai-password-toggle" data-target="lexai-password">
                            <span class="lexai-show-password">👁️</span>
                            <span class="lexai-hide-password" style="display: none;">🙈</span>
                        </button>
                    </div>
                </div>
                
                <div class="lexai-form-options">
                    <label class="lexai-checkbox">
                        <input type="checkbox" name="remember" value="1">
                        <span class="lexai-checkmark"></span>
                        <span class="lexai-checkbox-text"><?php _e('Recordarme', 'lexai'); ?></span>
                    </label>
                    
                    <a href="#" class="lexai-forgot-link" data-form="forgot-password">
                        <?php _e('¿Olvidaste tu contraseña?', 'lexai'); ?>
                    </a>
                </div>
                
                <button type="submit" class="lexai-auth-btn lexai-auth-btn-primary">
                    <span class="lexai-btn-text"><?php _e('Iniciar Sesión', 'lexai'); ?></span>
                    <span class="lexai-btn-loading" style="display: none;">
                        <span class="lexai-spinner"></span>
                        <?php _e('Iniciando...', 'lexai'); ?>
                    </span>
                </button>
                
                <input type="hidden" name="redirect_to" value="<?php echo esc_url($atts['redirect']); ?>">
                <?php wp_nonce_field('lexai_login_nonce'); ?>
            </form>
            
            <div class="lexai-auth-divider">
                <span><?php _e('o', 'lexai'); ?></span>
            </div>
            
            <div class="lexai-social-login">
                <button class="lexai-social-btn lexai-google-btn">
                    <span class="lexai-social-icon">🔍</span>
                    <?php _e('Continuar con Google', 'lexai'); ?>
                </button>
            </div>
            
            <div class="lexai-auth-footer">
                <p>
                    <?php _e('¿No tienes cuenta?', 'lexai'); ?>
                    <a href="#" class="lexai-switch-form" data-form="register">
                        <?php _e('Regístrate aquí', 'lexai'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- Features Section -->
    <div class="lexai-auth-features">
        <h3><?php _e('¿Por qué elegir LexAI?', 'lexai'); ?></h3>
        <div class="lexai-features-grid">
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-robot"></i></div>
                <h4><?php _e('IA Especializada', 'lexai'); ?></h4>
                <p><?php _e('Agentes especializados en derecho mexicano', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-book"></i></div>
                <h4><?php _e('Base de Conocimientos', 'lexai'); ?></h4>
                <p><?php _e('Acceso a jurisprudencia y legislación actualizada', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-shield-alt"></i></div>
                <h4><?php _e('Seguro y Confiable', 'lexai'); ?></h4>
                <p><?php _e('Tus consultas están protegidas y son confidenciales', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-bolt"></i></div>
                <h4><?php _e('Respuestas Rápidas', 'lexai'); ?></h4>
                <p><?php _e('Obtén respuestas legales en segundos', 'lexai'); ?></p>
            </div>
        </div>
    </div>
</div>

<?php
// Prevent loading fullpage chat scripts on login page
remove_action('wp_enqueue_scripts', array($GLOBALS['lexai']->public_fullpage ?? null, 'enqueue_scripts'));

// Enqueue external CSS and JS files with updated version
wp_enqueue_style('lexai-auth', LEXAI_PLUGIN_URL . 'assets/css/lexai-auth.css', array(), LEXAI_VERSION);
wp_enqueue_script('lexai-auth', LEXAI_PLUGIN_URL . 'assets/js/lexai-auth.js', array('jquery'), LEXAI_VERSION, true);

// Localize script for AJAX with proper configuration
wp_localize_script('lexai-auth', 'lexai_ajax', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lexai_auth_nonce')
));

// Also provide lexaiConfig for compatibility (but empty since user is not logged in)
wp_localize_script('lexai-auth', 'lexaiConfig', array(
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lexai_auth_nonce'),
    'userId' => 0, // Not logged in
    'userRole' => 'guest',
    'isLoginPage' => true
));
?>

<!-- Prevent conflicts with other scripts -->
<script>
    // Prevent lazyload conflicts
    if (typeof window.lazyloadRunObserver !== 'undefined') {
        console.warn('LexAI Auth: Clearing existing lazyloadRunObserver to prevent conflicts');
        delete window.lazyloadRunObserver;
    }

    // Prevent fullpage chat scripts from loading
    window.lexaiAuthPageActive = true;

    // Override any fullpage chat initialization
    window.initLexAI = function() {
        console.log('LexAI: Fullpage chat initialization blocked on auth page');
    };

    // Ensure lexaiConfig is available for auth page
    if (typeof window.lexaiConfig === 'undefined') {
        window.lexaiConfig = {
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('lexai_auth_nonce'); ?>',
            userId: 0,
            userRole: 'guest',
            isLoginPage: true
        };
    }

    console.log('LexAI Auth: Page initialized', window.lexaiConfig);
</script>







