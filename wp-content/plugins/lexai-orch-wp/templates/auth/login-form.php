<?php
/**
 * Login Form Template - Glassmorphism Design
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="lexai-auth-container" data-theme="<?php echo esc_attr($atts['theme']); ?>">
    <!-- Animated Background - EXACT SAME AS CHAT -->
    <div class="lexai-auth-bg">
        <div class="lexai-floating-element lexai-element-1"></div>
        <div class="lexai-floating-element lexai-element-2"></div>
        <div class="lexai-floating-element lexai-element-3"></div>
        <div class="lexai-floating-element lexai-element-4"></div>
        <div class="lexai-floating-element lexai-element-5"></div>
    </div>
    
    <div class="lexai-auth-card">
        <div class="lexai-auth-header">
            <div class="lexai-auth-logo">
                <div class="lexai-auth-icon"><i class="fas fa-balance-scale" aria-label="LexAI Legal Assistant"></i></div>
                <h1>LexAI</h1>
                <p><?php _e('Asistente Legal Inteligente', 'lexai'); ?></p>
            </div>
        </div>
        
        <div class="lexai-auth-body">
            <h2><?php _e('Iniciar Sesión', 'lexai'); ?></h2>
            <p class="lexai-auth-subtitle"><?php _e('Accede a tu asistente legal especializado', 'lexai'); ?></p>
            
            <form id="lexai-login-form" class="lexai-auth-form" method="post">
                <?php wp_nonce_field('lexai_login', 'lexai_login_nonce'); ?>
                
                <div class="lexai-form-group">
                    <label for="lexai-username"><?php _e('Usuario o Email', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-user"></i></span>
                        <input 
                            type="text" 
                            id="lexai-username" 
                            name="username" 
                            class="lexai-input" 
                            placeholder="<?php _e('Ingresa tu usuario o email', 'lexai'); ?>"
                            required
                        >
                    </div>
                </div>
                
                <div class="lexai-form-group">
                    <label for="lexai-password"><?php _e('Contraseña', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-lock"></i></span>
                        <input 
                            type="password" 
                            id="lexai-password" 
                            name="password" 
                            class="lexai-input" 
                            placeholder="<?php _e('Ingresa tu contraseña', 'lexai'); ?>"
                            required
                        >
                        <button type="button" class="lexai-password-toggle" data-target="lexai-password">
                            <span class="lexai-show-password">👁️</span>
                            <span class="lexai-hide-password" style="display: none;">🙈</span>
                        </button>
                    </div>
                </div>
                
                <div class="lexai-form-options">
                    <label class="lexai-checkbox">
                        <input type="checkbox" name="remember" value="1">
                        <span class="lexai-checkmark"></span>
                        <span class="lexai-checkbox-text"><?php _e('Recordarme', 'lexai'); ?></span>
                    </label>
                    
                    <a href="#" class="lexai-forgot-link" data-form="forgot-password">
                        <?php _e('¿Olvidaste tu contraseña?', 'lexai'); ?>
                    </a>
                </div>
                
                <button type="submit" class="lexai-auth-btn lexai-auth-btn-primary">
                    <span class="lexai-btn-text"><?php _e('Iniciar Sesión', 'lexai'); ?></span>
                    <span class="lexai-btn-loading" style="display: none;">
                        <span class="lexai-spinner"></span>
                        <?php _e('Iniciando...', 'lexai'); ?>
                    </span>
                </button>
                
                <input type="hidden" name="redirect_to" value="<?php echo esc_url($atts['redirect']); ?>">
                <?php wp_nonce_field('lexai_login_nonce'); ?>
            </form>
            
            <div class="lexai-auth-divider">
                <span><?php _e('o', 'lexai'); ?></span>
            </div>
            
            <div class="lexai-social-login">
                <button class="lexai-social-btn lexai-google-btn">
                    <span class="lexai-social-icon">🔍</span>
                    <?php _e('Continuar con Google', 'lexai'); ?>
                </button>
            </div>
            
            <div class="lexai-auth-footer">
                <p>
                    <?php _e('¿No tienes cuenta?', 'lexai'); ?>
                    <a href="#" class="lexai-switch-form" data-form="register">
                        <?php _e('Regístrate aquí', 'lexai'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- Features Section -->
    <div class="lexai-auth-features">
        <h3><?php _e('¿Por qué elegir LexAI?', 'lexai'); ?></h3>
        <div class="lexai-features-grid">
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-robot"></i></div>
                <h4><?php _e('IA Especializada', 'lexai'); ?></h4>
                <p><?php _e('Agentes especializados en derecho mexicano', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-book"></i></div>
                <h4><?php _e('Base de Conocimientos', 'lexai'); ?></h4>
                <p><?php _e('Acceso a jurisprudencia y legislación actualizada', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-shield-alt"></i></div>
                <h4><?php _e('Seguro y Confiable', 'lexai'); ?></h4>
                <p><?php _e('Tus consultas están protegidas y son confidenciales', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-bolt"></i></div>
                <h4><?php _e('Respuestas Rápidas', 'lexai'); ?></h4>
                <p><?php _e('Obtén respuestas legales en segundos', 'lexai'); ?></p>
            </div>
        </div>
    </div>
</div>

<?php
// Prevent loading fullpage chat scripts on login page
remove_action('wp_enqueue_scripts', array($GLOBALS['lexai']->public_fullpage ?? null, 'enqueue_scripts'));

// Enqueue external CSS and JS files with FORCED cache busting
$css_version = '2.0.1-' . time(); // Force cache refresh
wp_enqueue_style('lexai-auth', LEXAI_PLUGIN_URL . 'assets/css/lexai-auth.css', array(), $css_version);
wp_enqueue_script('lexai-auth', LEXAI_PLUGIN_URL . 'assets/js/lexai-auth.js', array('jquery'), LEXAI_VERSION, true);

// Localize script for AJAX with proper configuration
wp_localize_script('lexai-auth', 'lexai_ajax', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lexai_auth_nonce')
));

// Also provide lexaiConfig for compatibility (but empty since user is not logged in)
wp_localize_script('lexai-auth', 'lexaiConfig', array(
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lexai_auth_nonce'),
    'userId' => 0, // Not logged in
    'userRole' => 'guest',
    'isLoginPage' => true
));
?>

<!-- Prevent conflicts with other scripts -->
<script>
    // Prevent lazyload conflicts
    if (typeof window.lazyloadRunObserver !== 'undefined') {
        console.warn('LexAI Auth: Clearing existing lazyloadRunObserver to prevent conflicts');
        delete window.lazyloadRunObserver;
    }

    // Prevent fullpage chat scripts from loading
    window.lexaiAuthPageActive = true;

    // Override any fullpage chat initialization
    window.initLexAI = function() {
        console.log('LexAI: Fullpage chat initialization blocked on auth page');
    };

    // Ensure lexaiConfig is available for auth page
    if (typeof window.lexaiConfig === 'undefined') {
        window.lexaiConfig = {
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('lexai_auth_nonce'); ?>',
            userId: 0,
            userRole: 'guest',
            isLoginPage: true
        };
    }

    console.log('LexAI Auth: Page initialized', window.lexaiConfig);
</script>

<!-- FORCE INLINE CSS TO OVERRIDE CACHE -->
<style>
/* FORCE NEW STYLES - INLINE TO BYPASS CACHE */
:root {
    --lexai-bg-main: #f8fafc !important;
    --lexai-bg-glass: rgba(248, 250, 252, 0.6) !important;
    --lexai-bg-card: rgba(248, 250, 252, 0.9) !important;
    --lexai-accent: #64748b !important;
    --lexai-text-primary: #1e293b !important;
    --lexai-text-secondary: #64748b !important;
    --lexai-transition: 0.3s ease !important;
}

.lexai-auth-container {
    background: var(--lexai-bg-main) !important;
    position: relative !important;
    overflow: hidden !important;
}

.lexai-auth-bg {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    z-index: -1 !important;
    overflow: hidden !important;
    pointer-events: none !important;
}

.lexai-floating-element {
    position: absolute !important;
    border-radius: 50% !important;
    background: radial-gradient(circle at 60% 40%, var(--lexai-accent) 0%, var(--lexai-primary) 60%, transparent 100%) !important;
    filter: blur(40px) !important;
    opacity: 0.15 !important;
    animation: lexai-float 20s ease-in-out infinite !important;
}

.lexai-element-1 {
    width: 300px !important;
    height: 300px !important;
    top: 10% !important;
    left: 10% !important;
    animation-delay: 0s !important;
}

.lexai-element-2 {
    width: 200px !important;
    height: 200px !important;
    top: 60% !important;
    right: 15% !important;
    animation-delay: -5s !important;
}

.lexai-element-3 {
    width: 250px !important;
    height: 250px !important;
    bottom: 20% !important;
    left: 20% !important;
    animation-delay: -10s !important;
}

.lexai-element-4 {
    width: 180px !important;
    height: 180px !important;
    top: 30% !important;
    right: 30% !important;
    animation-delay: -15s !important;
}

.lexai-element-5 {
    width: 220px !important;
    height: 220px !important;
    bottom: 40% !important;
    right: 10% !important;
    animation-delay: -20s !important;
}

@keyframes lexai-float {
    0%, 100% {
        transform: translate(0, 0) scale(1) !important;
        opacity: 0.15 !important;
    }
    25% {
        transform: translate(30px, -30px) scale(1.1) !important;
        opacity: 0.25 !important;
    }
    50% {
        transform: translate(-20px, 20px) scale(0.9) !important;
        opacity: 0.1 !important;
    }
    75% {
        transform: translate(20px, -10px) scale(1.05) !important;
        opacity: 0.2 !important;
    }
}

.lexai-auth-card {
    background: var(--lexai-bg-glass) !important;
    backdrop-filter: blur(20px) !important;
    -webkit-backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    position: relative !important;
    z-index: 1 !important;
}
</style>







