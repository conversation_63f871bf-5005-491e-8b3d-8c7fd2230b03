<?php
/**
 * Forgot Password Form Template - Glassmorphism Design
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="lexai-auth-container" data-theme="<?php echo esc_attr($atts['theme']); ?>">
    <!-- Animated Background - EXACT SAME AS CHAT -->
    <div class="lexai-auth-bg">
        <div class="lexai-floating-element lexai-element-1"></div>
        <div class="lexai-floating-element lexai-element-2"></div>
        <div class="lexai-floating-element lexai-element-3"></div>
        <div class="lexai-floating-element lexai-element-4"></div>
        <div class="lexai-floating-element lexai-element-5"></div>
    </div>
    
    <div class="lexai-auth-card">
        <div class="lexai-auth-header">
            <div class="lexai-auth-logo">
                <div class="lexai-auth-icon"><i class="fas fa-balance-scale" aria-label="LexAI Legal Assistant"></i></div>
                <h1>LexAI</h1>
                <p><?php _e('Asistente Legal Inteligente', 'lexai'); ?></p>
            </div>
        </div>
        
        <div class="lexai-auth-body">
            <h2><?php _e('Recuperar Contraseña', 'lexai'); ?></h2>
            <p class="lexai-auth-subtitle"><?php _e('Te enviaremos un enlace para restablecer tu contraseña', 'lexai'); ?></p>
            
            <form id="lexai-forgot-password-form" class="lexai-auth-form" method="post">
                <?php wp_nonce_field('lexai_forgot_password', 'lexai_forgot_password_nonce'); ?>
                
                <div class="lexai-form-group">
                    <label for="lexai-user-login"><?php _e('Usuario o Email', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-envelope"></i></span>
                        <input 
                            type="text" 
                            id="lexai-user-login" 
                            name="user_login" 
                            class="lexai-input" 
                            placeholder="<?php _e('Ingresa tu usuario o email', 'lexai'); ?>"
                            required
                        >
                    </div>
                    <small class="lexai-form-help">
                        <?php _e('Ingresa el email o nombre de usuario asociado a tu cuenta', 'lexai'); ?>
                    </small>
                </div>

                <button type="submit" class="lexai-submit-btn">
                    <span class="lexai-btn-text"><?php _e('Enviar Enlace de Recuperación', 'lexai'); ?></span>
                    <div class="lexai-loading">
                        <div class="lexai-spinner"></div>
                    </div>
                </button>
                
                <input type="hidden" name="redirect_to" value="<?php echo esc_url($atts['redirect']); ?>">
                <?php wp_nonce_field('lexai_forgot_password_nonce'); ?>
            </form>
            
            <div class="lexai-auth-divider">
                <span><?php _e('o', 'lexai'); ?></span>
            </div>
            
            <div class="lexai-auth-footer">
                <p>
                    <?php _e('¿Recordaste tu contraseña?', 'lexai'); ?>
                    <a href="#" class="lexai-switch-form" data-form="login">
                        <?php _e('Inicia sesión aquí', 'lexai'); ?>
                    </a>
                </p>
                <p>
                    <?php _e('¿No tienes cuenta?', 'lexai'); ?>
                    <a href="#" class="lexai-switch-form" data-form="register">
                        <?php _e('Regístrate aquí', 'lexai'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- Help Section -->
    <div class="lexai-help-section">
        <h3><?php _e('¿Necesitas ayuda?', 'lexai'); ?></h3>
        <div class="lexai-help-grid">
            <div class="lexai-help-item">
                <div class="lexai-help-icon"><i class="fas fa-question-circle"></i></div>
                <h4><?php _e('¿No recibes el email?', 'lexai'); ?></h4>
                <p><?php _e('Revisa tu carpeta de spam o correo no deseado. El email puede tardar unos minutos en llegar.', 'lexai'); ?></p>
            </div>
            <div class="lexai-help-item">
                <div class="lexai-help-icon"><i class="fas fa-clock"></i></div>
                <h4><?php _e('Enlace Expirado', 'lexai'); ?></h4>
                <p><?php _e('Los enlaces de recuperación expiran en 24 horas por seguridad. Solicita uno nuevo si es necesario.', 'lexai'); ?></p>
            </div>
            <div class="lexai-help-item">
                <div class="lexai-help-icon"><i class="fas fa-shield-alt"></i></div>
                <h4><?php _e('Seguridad', 'lexai'); ?></h4>
                <p><?php _e('Solo tú puedes usar el enlace de recuperación. Es único y seguro para tu cuenta.', 'lexai'); ?></p>
            </div>
            <div class="lexai-help-item">
                <div class="lexai-help-icon"><i class="fas fa-headset"></i></div>
                <h4><?php _e('Soporte', 'lexai'); ?></h4>
                <p><?php _e('Si tienes problemas, contacta a nuestro equipo de soporte técnico.', 'lexai'); ?></p>
            </div>
        </div>
    </div>
</div>

<?php
// Prevent loading fullpage chat scripts on forgot password page
remove_action('wp_enqueue_scripts', array($GLOBALS['lexai']->public_fullpage ?? null, 'enqueue_scripts'));

// Enqueue external CSS and JS files with updated version
wp_enqueue_style('lexai-auth', LEXAI_PLUGIN_URL . 'assets/css/lexai-auth.css', array(), LEXAI_VERSION);
wp_enqueue_script('lexai-auth', LEXAI_PLUGIN_URL . 'assets/js/lexai-auth.js', array('jquery'), LEXAI_VERSION, true);

// Localize script for AJAX with proper configuration
wp_localize_script('lexai-auth', 'lexai_ajax', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lexai_auth_nonce')
));

// Also provide lexaiConfig for compatibility (but empty since user is not logged in)
wp_localize_script('lexai-auth', 'lexaiConfig', array(
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lexai_auth_nonce'),
    'userId' => 0, // Not logged in
    'userRole' => 'guest',
    'isForgotPasswordPage' => true
));
?>

<!-- Prevent conflicts with other scripts -->
<script>
    // Prevent lazyload conflicts
    if (typeof window.lazyloadRunObserver !== 'undefined') {
        console.warn('LexAI Auth: Clearing existing lazyloadRunObserver to prevent conflicts');
        delete window.lazyloadRunObserver;
    }
    
    // Prevent fullpage chat scripts from loading
    window.lexaiAuthPageActive = true;
    
    // Override any fullpage chat initialization
    window.initLexAI = function() {
        console.log('LexAI: Fullpage chat initialization blocked on auth page');
    };
    
    // Ensure lexaiConfig is available for auth page
    if (typeof window.lexaiConfig === 'undefined') {
        window.lexaiConfig = {
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('lexai_auth_nonce'); ?>',
            userId: 0,
            userRole: 'guest',
            isForgotPasswordPage: true
        };
    }
    
    console.log('LexAI Auth: Forgot password page initialized', window.lexaiConfig);
</script>
