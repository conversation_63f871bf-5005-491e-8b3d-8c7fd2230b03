<?php
/**
 * Register Form Template - Glassmorphism Design
 *
 * @package LexAI
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="lexai-auth-container" data-theme="<?php echo esc_attr($atts['theme']); ?>">
    <!-- Animated Background - EXACT SAME AS CHAT -->
    <div class="lexai-auth-bg">
        <div class="lexai-floating-element lexai-element-1"></div>
        <div class="lexai-floating-element lexai-element-2"></div>
        <div class="lexai-floating-element lexai-element-3"></div>
        <div class="lexai-floating-element lexai-element-4"></div>
        <div class="lexai-floating-element lexai-element-5"></div>
    </div>
    
    <div class="lexai-auth-card">
        <div class="lexai-auth-header">
            <div class="lexai-auth-logo">
                <div class="lexai-auth-icon"><i class="fas fa-balance-scale" aria-label="LexAI Legal Assistant"></i></div>
                <h1>LexAI</h1>
                <p><?php _e('Asistente Legal Inteligente', 'lexai'); ?></p>
            </div>
        </div>
        
        <div class="lexai-auth-body">
            <h2><?php _e('Crear Cuenta', 'lexai'); ?></h2>
            <p class="lexai-auth-subtitle"><?php _e('Únete a la revolución de la consultoría legal', 'lexai'); ?></p>
            
            <form id="lexai-register-form" class="lexai-auth-form" method="post">
                <?php wp_nonce_field('lexai_register', 'lexai_register_nonce'); ?>
                
                <div class="lexai-form-group">
                    <label for="lexai-first-name"><?php _e('Nombre', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-user"></i></span>
                        <input 
                            type="text" 
                            id="lexai-first-name" 
                            name="first_name" 
                            class="lexai-input" 
                            placeholder="<?php _e('Tu nombre', 'lexai'); ?>"
                            required
                        >
                    </div>
                </div>
                
                <div class="lexai-form-group">
                    <label for="lexai-last-name"><?php _e('Apellidos', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-user"></i></span>
                        <input 
                            type="text" 
                            id="lexai-last-name" 
                            name="last_name" 
                            class="lexai-input" 
                            placeholder="<?php _e('Tus apellidos', 'lexai'); ?>"
                            required
                        >
                    </div>
                </div>
                
                <div class="lexai-form-group">
                    <label for="lexai-email"><?php _e('Email', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-envelope"></i></span>
                        <input 
                            type="email" 
                            id="lexai-email" 
                            name="email" 
                            class="lexai-input" 
                            placeholder="<?php _e('<EMAIL>', 'lexai'); ?>"
                            required
                        >
                    </div>
                </div>
                
                <div class="lexai-form-group">
                    <label for="lexai-username"><?php _e('Usuario', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-at"></i></span>
                        <input 
                            type="text" 
                            id="lexai-username" 
                            name="username" 
                            class="lexai-input" 
                            placeholder="<?php _e('Nombre de usuario', 'lexai'); ?>"
                            required
                        >
                    </div>
                </div>

                <div class="lexai-form-group">
                    <label for="lexai-password"><?php _e('Contraseña', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-lock"></i></span>
                        <input 
                            type="password" 
                            id="lexai-password" 
                            name="password" 
                            class="lexai-input" 
                            placeholder="<?php _e('Crea una contraseña segura', 'lexai'); ?>"
                            required
                        >
                        <button type="button" class="lexai-password-toggle" data-target="lexai-password" title="<?php _e('Mostrar contraseña', 'lexai'); ?>">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="lexai-password-strength">
                        <div class="lexai-strength-meter">
                            <div class="lexai-strength-bar"></div>
                        </div>
                        <span class="lexai-strength-text"><?php _e('Ingresa una contraseña', 'lexai'); ?></span>
                    </div>
                </div>

                <div class="lexai-form-group">
                    <label for="lexai-confirm-password"><?php _e('Confirmar Contraseña', 'lexai'); ?></label>
                    <div class="lexai-input-wrapper">
                        <span class="lexai-input-icon"><i class="fas fa-lock"></i></span>
                        <input 
                            type="password" 
                            id="lexai-confirm-password" 
                            name="confirm_password" 
                            class="lexai-input" 
                            placeholder="<?php _e('Confirma tu contraseña', 'lexai'); ?>"
                            required
                        >
                        <button type="button" class="lexai-password-toggle" data-target="lexai-confirm-password" title="<?php _e('Mostrar contraseña', 'lexai'); ?>">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="lexai-form-group lexai-checkbox-group">
                    <label class="lexai-checkbox-label">
                        <input type="checkbox" name="terms_accepted" required>
                        <span class="lexai-checkbox-custom"></span>
                        <?php _e('Acepto los', 'lexai'); ?> 
                        <a href="#" class="lexai-link"><?php _e('Términos y Condiciones', 'lexai'); ?></a> 
                        <?php _e('y la', 'lexai'); ?> 
                        <a href="#" class="lexai-link"><?php _e('Política de Privacidad', 'lexai'); ?></a>
                    </label>
                </div>

                <div class="lexai-form-group lexai-checkbox-group">
                    <label class="lexai-checkbox-label">
                        <input type="checkbox" name="newsletter_subscribe">
                        <span class="lexai-checkbox-custom"></span>
                        <?php _e('Quiero recibir actualizaciones y noticias legales', 'lexai'); ?>
                    </label>
                </div>

                <button type="submit" class="lexai-submit-btn">
                    <span class="lexai-btn-text"><?php _e('Crear Cuenta', 'lexai'); ?></span>
                    <div class="lexai-loading">
                        <div class="lexai-spinner"></div>
                    </div>
                </button>
                
                <input type="hidden" name="redirect_to" value="<?php echo esc_url($atts['redirect']); ?>">
                <?php wp_nonce_field('lexai_register_nonce'); ?>
            </form>
            
            <div class="lexai-auth-divider">
                <span><?php _e('o', 'lexai'); ?></span>
            </div>
            
            <div class="lexai-social-login">
                <button class="lexai-social-btn lexai-google-btn">
                    <span class="lexai-social-icon">🔍</span>
                    <?php _e('Continuar con Google', 'lexai'); ?>
                </button>
            </div>
            
            <div class="lexai-auth-footer">
                <p>
                    <?php _e('¿Ya tienes cuenta?', 'lexai'); ?>
                    <a href="#" class="lexai-switch-form" data-form="login">
                        <?php _e('Inicia sesión aquí', 'lexai'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
    
    <!-- Features Section -->
    <div class="lexai-features-section">
        <h3><?php _e('¿Por qué elegir LexAI?', 'lexai'); ?></h3>
        <div class="lexai-features-grid">
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-brain"></i></div>
                <h4><?php _e('IA Especializada', 'lexai'); ?></h4>
                <p><?php _e('Inteligencia artificial entrenada específicamente en derecho mexicano', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-gavel"></i></div>
                <h4><?php _e('Base Legal Actualizada', 'lexai'); ?></h4>
                <p><?php _e('Acceso a jurisprudencia y legislación actualizada', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-shield-alt"></i></div>
                <h4><?php _e('Seguro y Confiable', 'lexai'); ?></h4>
                <p><?php _e('Tus consultas están protegidas y son confidenciales', 'lexai'); ?></p>
            </div>
            <div class="lexai-feature-item">
                <div class="lexai-feature-icon"><i class="fas fa-bolt"></i></div>
                <h4><?php _e('Respuestas Rápidas', 'lexai'); ?></h4>
                <p><?php _e('Obtén respuestas legales en segundos', 'lexai'); ?></p>
            </div>
        </div>
    </div>
</div>

<?php
// Prevent loading fullpage chat scripts on register page
remove_action('wp_enqueue_scripts', array($GLOBALS['lexai']->public_fullpage ?? null, 'enqueue_scripts'));

// Enqueue external CSS and JS files with FORCED cache busting
$css_version = '2.0.1-' . time(); // Force cache refresh
wp_enqueue_style('lexai-auth', LEXAI_PLUGIN_URL . 'assets/css/lexai-auth.css', array(), $css_version);
wp_enqueue_script('lexai-auth', LEXAI_PLUGIN_URL . 'assets/js/lexai-auth.js', array('jquery'), LEXAI_VERSION, true);

// Localize script for AJAX with proper configuration
wp_localize_script('lexai-auth', 'lexai_ajax', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lexai_auth_nonce')
));

// Also provide lexaiConfig for compatibility (but empty since user is not logged in)
wp_localize_script('lexai-auth', 'lexaiConfig', array(
    'ajaxUrl' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('lexai_auth_nonce'),
    'userId' => 0, // Not logged in
    'userRole' => 'guest',
    'isRegisterPage' => true
));
?>

<!-- Prevent conflicts with other scripts -->
<script>
    // Prevent lazyload conflicts
    if (typeof window.lazyloadRunObserver !== 'undefined') {
        console.warn('LexAI Auth: Clearing existing lazyloadRunObserver to prevent conflicts');
        delete window.lazyloadRunObserver;
    }
    
    // Prevent fullpage chat scripts from loading
    window.lexaiAuthPageActive = true;
    
    // Override any fullpage chat initialization
    window.initLexAI = function() {
        console.log('LexAI: Fullpage chat initialization blocked on auth page');
    };
    
    // Ensure lexaiConfig is available for auth page
    if (typeof window.lexaiConfig === 'undefined') {
        window.lexaiConfig = {
            ajaxUrl: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('lexai_auth_nonce'); ?>',
            userId: 0,
            userRole: 'guest',
            isRegisterPage: true
        };
    }
    
    console.log('LexAI Auth: Register page initialized', window.lexaiConfig);
</script>
